apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-adapter-config
  namespace: ai-coding-agent
  labels:
    app: prometheus-adapter
    project: ai-coding-agent
data:
  config.yaml: |
    rules:
      - seriesQuery: 'request_rate_per_second{namespace!=""}'
        resources:
          overrides:
            namespace: {resource: "namespace"}
            pod: {resource: "pod"}
        name:
          matches: "request_rate_per_second"
          as: "request_rate"
        metricsQuery: 'sum(rate(request_rate_per_second[1m])) by (namespace, pod)'
      - seriesQuery: 'average_response_time_ms{namespace!=""}'
        resources:
          overrides:
            namespace: {resource: "namespace"}
            pod: {resource: "pod"}
        name:
          matches: "average_response_time_ms"
          as: "response_time"
        metricsQuery: 'avg(average_response_time_ms) by (namespace, pod)'
      - seriesQuery: 'active_user_sessions{namespace!=""}'
        resources:
          overrides:
            namespace: {resource: "namespace"}
            pod: {resource: "pod"}
        name:
          matches: "active_user_sessions"
          as: "active_sessions"
        metricsQuery: 'sum(active_user_sessions) by (namespace, pod)'
      - seriesQuery: 'database_connection_pool_usage{namespace!=""}'
        resources:
          overrides:
            namespace: {resource: "namespace"}
            pod: {resource: "pod"}
        name:
          matches: "database_connection_pool_usage"
          as: "db_pool_usage"
        metricsQuery: 'avg(database_connection_pool_usage) by (namespace, pod)'
