/**
 * Resource Usage Visualization Component
 * 
 * Provides real-time visualization of system and container resource usage
 * with charts, graphs, and monitoring dashboards.
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  Cpu,
  HardDrive,
  Network,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

interface ResourceMetrics {
  timestamp: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkRx: number;
  networkTx: number;
  containerCount: number;
  activeUsers: number;
}

interface SystemOverview {
  totalCpuUsage: number;
  totalMemoryUsage: number;
  totalDiskUsage: number;
  totalContainers: number;
  runningContainers: number;
  alerts: Array<{
    id: string;
    severity: 'info' | 'warning' | 'critical';
    message: string;
    timestamp: string;
  }>;
}

const ResourceUsageVisualization: React.FC = () => {
  const [metrics, setMetrics] = useState<ResourceMetrics[]>([]);
  const [overview, setOverview] = useState<SystemOverview>({
    totalCpuUsage: 0,
    totalMemoryUsage: 0,
    totalDiskUsage: 0,
    totalContainers: 0,
    runningContainers: 0,
    alerts: []
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch resource metrics
  const fetchMetrics = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/v1/admin/monitoring/overview', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setOverview(data);
        
        // Add current metrics to history (keep last 20 points)
        const newMetric: ResourceMetrics = {
          timestamp: new Date().toISOString(),
          cpuUsage: data.totalCpuUsage || 0,
          memoryUsage: data.totalMemoryUsage || 0,
          diskUsage: data.totalDiskUsage || 0,
          networkRx: data.networkRx || 0,
          networkTx: data.networkTx || 0,
          containerCount: data.totalContainers || 0,
          activeUsers: data.activeUsers || 0
        };
        
        setMetrics(prev => [...prev.slice(-19), newMetric]);
      }
    } catch (error) {
      console.error('Failed to fetch metrics:', error);
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // Auto-refresh every 5 seconds
    const interval = setInterval(fetchMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'text-red-500';
    if (usage >= 70) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getUsageBarColor = (usage: number) => {
    if (usage >= 90) return 'bg-red-500';
    if (usage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 border-red-200 text-red-800 dark:bg-red-900 dark:border-red-800 dark:text-red-300';
      case 'warning':
        return 'bg-yellow-100 border-yellow-200 text-yellow-800 dark:bg-yellow-900 dark:border-yellow-800 dark:text-yellow-300';
      default:
        return 'bg-blue-100 border-blue-200 text-blue-800 dark:bg-blue-900 dark:border-blue-800 dark:text-blue-300';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading metrics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Resource Usage Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time system and container resource monitoring
          </p>
        </div>
        <button
          onClick={fetchMetrics}
          disabled={refreshing}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md shadow hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Resource Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* CPU Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <Cpu className="w-6 h-6 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">CPU Usage</span>
            </div>
            <span className={`text-lg font-bold ${getUsageColor(overview.totalCpuUsage)}`}>
              {overview.totalCpuUsage.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getUsageBarColor(overview.totalCpuUsage)}`}
              style={{ width: `${Math.min(100, overview.totalCpuUsage)}%` }}
            ></div>
          </div>
        </div>

        {/* Memory Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <HardDrive className="w-6 h-6 text-green-500 mr-2" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Memory Usage</span>
            </div>
            <span className={`text-lg font-bold ${getUsageColor(overview.totalMemoryUsage)}`}>
              {overview.totalMemoryUsage.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getUsageBarColor(overview.totalMemoryUsage)}`}
              style={{ width: `${Math.min(100, overview.totalMemoryUsage)}%` }}
            ></div>
          </div>
        </div>

        {/* Disk Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <HardDrive className="w-6 h-6 text-purple-500 mr-2" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Disk Usage</span>
            </div>
            <span className={`text-lg font-bold ${getUsageColor(overview.totalDiskUsage)}`}>
              {overview.totalDiskUsage.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getUsageBarColor(overview.totalDiskUsage)}`}
              style={{ width: `${Math.min(100, overview.totalDiskUsage)}%` }}
            ></div>
          </div>
        </div>

        {/* Container Count */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <Activity className="w-6 h-6 text-orange-500 mr-2" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Containers</span>
            </div>
            <span className="text-lg font-bold text-gray-900 dark:text-white">
              {overview.runningContainers}/{overview.totalContainers}
            </span>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {overview.runningContainers} running
          </div>
        </div>
      </div>

      {/* Resource Trends Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Resource Trends (Last 20 measurements)
        </h3>
        
        {metrics.length > 0 ? (
          <div className="space-y-4">
            {/* Simple line chart representation */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">CPU Trend</span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {metrics[metrics.length - 1]?.cpuUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="h-16 flex items-end space-x-1">
                  {metrics.slice(-10).map((metric, index) => (
                    <div
                      key={index}
                      className="flex-1 bg-blue-500 rounded-t"
                      style={{ height: `${Math.max(4, (metric.cpuUsage / 100) * 64)}px` }}
                      title={`${formatTime(metric.timestamp)}: ${metric.cpuUsage.toFixed(1)}%`}
                    ></div>
                  ))}
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Memory Trend</span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {metrics[metrics.length - 1]?.memoryUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="h-16 flex items-end space-x-1">
                  {metrics.slice(-10).map((metric, index) => (
                    <div
                      key={index}
                      className="flex-1 bg-green-500 rounded-t"
                      style={{ height: `${Math.max(4, (metric.memoryUsage / 100) * 64)}px` }}
                      title={`${formatTime(metric.timestamp)}: ${metric.memoryUsage.toFixed(1)}%`}
                    ></div>
                  ))}
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Container Count</span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {metrics[metrics.length - 1]?.containerCount || 0}
                  </span>
                </div>
                <div className="h-16 flex items-end space-x-1">
                  {metrics.slice(-10).map((metric, index) => {
                    const maxContainers = Math.max(...metrics.map(m => m.containerCount), 1);
                    return (
                      <div
                        key={index}
                        className="flex-1 bg-purple-500 rounded-t"
                        style={{ height: `${Math.max(4, (metric.containerCount / maxContainers) * 64)}px` }}
                        title={`${formatTime(metric.timestamp)}: ${metric.containerCount} containers`}
                      ></div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Activity className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">No metrics data available yet</p>
          </div>
        )}
      </div>

      {/* Alerts */}
      {overview.alerts.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            System Alerts ({overview.alerts.length})
          </h3>
          <div className="space-y-3">
            {overview.alerts.map((alert) => (
              <div
                key={alert.id}
                className={`flex items-start p-3 rounded-lg border ${getAlertColor(alert.severity)}`}
              >
                <div className="flex-shrink-0 mr-3 mt-0.5">
                  {getAlertIcon(alert.severity)}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{alert.message}</p>
                  <p className="text-xs mt-1 opacity-75">
                    {formatTime(alert.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ResourceUsageVisualization;
