"""
Unified Rate Limiting Service for AI Coding Agent.

This service consolidates all rate limiting functionality into a single,
comprehensive system that handles:
- Admin endpoint protection
- AI operation rate limiting
- General API rate limiting
- Authentication rate limiting
- User-specific and IP-based limiting
"""

import time
import asyncio
from typing import Dict, Optional, List, Tuple, Any
from collections import defaultdict, deque
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass
import redis.asyncio as redis
from fastapi import HTT<PERSON>Exception, Request, status

from ..config import settings
from ..services.audit_service import log_security_event, AuditCategory, AuditLevel


class RateLimitType(str, Enum):
    """Types of rate limiting."""
    ADMIN = "admin"
    AI = "ai"
    API = "api"
    AUTH = "auth"
    UPLOAD = "upload"


class RateLimitScope(str, Enum):
    """Scope of rate limiting."""
    IP = "ip"
    USER = "user"
    GLOBAL = "global"


@dataclass
class RateLimitConfig:
    """Configuration for a specific rate limit."""
    limit: int
    window_seconds: int
    burst_limit: Optional[int] = None
    scope: RateLimitScope = RateLimitScope.IP
    block_duration_seconds: int = 300


@dataclass
class RateLimitResult:
    """Result of a rate limit check."""
    allowed: bool
    remaining: int
    reset_time: int
    retry_after: Optional[int] = None
    headers: Optional[Dict[str, str]] = None


class UnifiedRateLimitService:
    """
    Unified rate limiting service that handles all types of rate limiting
    across the AI Coding Agent platform.
    """

    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.fallback_storage: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Dict[str, datetime] = {}

        # Load rate limit configurations from settings
        rate_settings = settings.rate_limit

        self.configs = {
            RateLimitType.ADMIN: RateLimitConfig(
                limit=rate_settings.admin_limit,
                window_seconds=rate_settings.admin_window_seconds,
                burst_limit=5,
                block_duration_seconds=rate_settings.admin_block_duration
            ),
            RateLimitType.AI: RateLimitConfig(
                limit=rate_settings.ai_limit,
                window_seconds=rate_settings.window_seconds,
                burst_limit=rate_settings.ai_burst_limit,
                scope=RateLimitScope.USER
            ),
            RateLimitType.API: RateLimitConfig(
                limit=rate_settings.api_limit,
                window_seconds=rate_settings.window_seconds,
                burst_limit=rate_settings.api_burst_limit
            ),
            RateLimitType.AUTH: RateLimitConfig(
                limit=rate_settings.auth_limit,
                window_seconds=rate_settings.auth_window_seconds,
                block_duration_seconds=rate_settings.auth_block_duration
            ),
            RateLimitType.UPLOAD: RateLimitConfig(
                limit=rate_settings.upload_limit,
                window_seconds=rate_settings.upload_window_seconds,
                burst_limit=rate_settings.upload_burst_limit
            )
        }

    async def get_redis_client(self) -> redis.Redis:
        """Get Redis client for distributed rate limiting."""
        if self.redis_client is None:
            redis_settings = settings.redis
            self.redis_client = redis.Redis(
                host=redis_settings.url.split("://")[1].split(":")[0] if "://" in redis_settings.url else "localhost",
                port=int(redis_settings.url.split(":")[-1]) if ":" in redis_settings.url else 6379,
                password=redis_settings.password,
                db=redis_settings.db,
                decode_responses=True
            )
        return self.redis_client

    def _get_rate_limit_key(
        self,
        limit_type: RateLimitType,
        identifier: str,
        window: int
    ) -> str:
        """Generate Redis key for rate limiting."""
        return f"rate_limit:{limit_type.value}:{identifier}:{window}"

    def _extract_identifier(
        self,
        request: Request,
        scope: RateLimitScope,
        user_id: Optional[str] = None
    ) -> str:
        """Extract identifier for rate limiting based on scope."""
        if scope == RateLimitScope.USER and user_id:
            return f"user:{user_id}"
        elif scope == RateLimitScope.IP:
            return f"ip:{request.client.host if request.client else 'unknown'}"
        else:
            return "global"

    async def check_rate_limit(
        self,
        request: Request,
        limit_type: RateLimitType,
        user_id: Optional[str] = None,
        custom_config: Optional[RateLimitConfig] = None
    ) -> RateLimitResult:
        """
        Check if a request should be rate limited.

        Args:
            request: FastAPI request object
            limit_type: Type of rate limiting to apply
            user_id: User ID for user-scoped rate limiting
            custom_config: Custom rate limit configuration

        Returns:
            RateLimitResult with decision and metadata
        """
        config = custom_config or self.configs.get(limit_type)
        if not config:
            # No rate limiting configured
            return RateLimitResult(
                allowed=True,
                remaining=999999,
                reset_time=int(time.time() + 3600),
                headers={}
            )

        identifier = self._extract_identifier(request, config.scope, user_id)
        now = int(time.time())
        window = now // config.window_seconds

        try:
            redis_client = await self.get_redis_client()
            key = self._get_rate_limit_key(limit_type, identifier, window)

            # Check if IP is blocked
            if config.scope == RateLimitScope.IP:
                block_key = f"blocked:{identifier}"
                blocked_until = await redis_client.get(block_key)
                if blocked_until and int(blocked_until) > now:
                    return RateLimitResult(
                        allowed=False,
                        remaining=0,
                        reset_time=int(blocked_until),
                        retry_after=int(blocked_until) - now,
                        headers={
                            "X-RateLimit-Limit": str(config.limit),
                            "X-RateLimit-Remaining": "0",
                            "X-RateLimit-Reset": str(blocked_until),
                            "Retry-After": str(int(blocked_until) - now)
                        }
                    )

            # Increment counter
            current = await redis_client.incr(key)
            if current == 1:
                await redis_client.expire(key, config.window_seconds)

            remaining = max(0, config.limit - current)
            reset_time = (window + 1) * config.window_seconds

            # Check if limit exceeded
            if current > config.limit:
                # Log security event
                log_security_event(
                    user_id=user_id or "anonymous",
                    action="rate_limit_exceeded",
                    resource=f"{limit_type.value}_endpoint",
                    ip_address=request.client.host if request.client else None,
                    user_agent=request.headers.get("user-agent")
                )

                # Block IP if configured
                if config.scope == RateLimitScope.IP and config.block_duration_seconds:
                    block_until = now + config.block_duration_seconds
                    block_key = f"blocked:{identifier}"
                    await redis_client.setex(block_key, config.block_duration_seconds, block_until)

                return RateLimitResult(
                    allowed=False,
                    remaining=0,
                    reset_time=reset_time,
                    retry_after=config.window_seconds - (now % config.window_seconds),
                    headers={
                        "X-RateLimit-Limit": str(config.limit),
                        "X-RateLimit-Remaining": "0",
                        "X-RateLimit-Reset": str(reset_time),
                        "Retry-After": str(config.window_seconds - (now % config.window_seconds))
                    }
                )

            return RateLimitResult(
                allowed=True,
                remaining=remaining,
                reset_time=reset_time,
                headers={
                    "X-RateLimit-Limit": str(config.limit),
                    "X-RateLimit-Remaining": str(remaining),
                    "X-RateLimit-Reset": str(reset_time)
                }
            )

        except Exception as e:
            # Fallback to in-memory rate limiting
            return await self._fallback_rate_limit(request, limit_type, config, identifier)

    async def _fallback_rate_limit(
        self,
        request: Request,
        limit_type: RateLimitType,
        config: RateLimitConfig,
        identifier: str
    ) -> RateLimitResult:
        """Fallback in-memory rate limiting when Redis is unavailable."""
        now = time.time()
        window_start = now - config.window_seconds

        # Clean old requests
        requests = self.fallback_storage[identifier]
        while requests and requests[0] < window_start:
            requests.popleft()

        # Check if blocked
        if identifier in self.blocked_ips:
            if self.blocked_ips[identifier] > datetime.now():
                return RateLimitResult(
                    allowed=False,
                    remaining=0,
                    reset_time=int(self.blocked_ips[identifier].timestamp()),
                    retry_after=int((self.blocked_ips[identifier] - datetime.now()).total_seconds())
                )
            else:
                del self.blocked_ips[identifier]

        # Check limit
        if len(requests) >= config.limit:
            # Block if configured
            if config.block_duration_seconds:
                self.blocked_ips[identifier] = datetime.now() + timedelta(seconds=config.block_duration_seconds)

            return RateLimitResult(
                allowed=False,
                remaining=0,
                reset_time=int(now + config.window_seconds),
                retry_after=config.window_seconds
            )

        # Add current request
        requests.append(now)

        return RateLimitResult(
            allowed=True,
            remaining=config.limit - len(requests),
            reset_time=int(now + config.window_seconds)
        )


# Global service instance
_rate_limit_service: Optional[UnifiedRateLimitService] = None


def get_rate_limit_service() -> UnifiedRateLimitService:
    """Get the global rate limit service instance."""
    global _rate_limit_service
    if _rate_limit_service is None:
        _rate_limit_service = UnifiedRateLimitService()
    return _rate_limit_service
