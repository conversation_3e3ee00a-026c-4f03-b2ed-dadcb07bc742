#!/usr/bin/env python3
"""
Test script for the enhanced orchestrator with verification and dependency management.
"""

import asyncio
import sys
from pathlib import Path

# Import path is handled by pyproject.toml configuration

from ai_coding_agent.services.ai.orchestrator import AgentOrchestrator, TaskRequest, AgentCollaboration
from ai_coding_agent.agents import AgentRole


async def test_verification_system():
    """Test the agent verification system."""
    print("=== Testing Agent Verification System ===")

    orchestrator = AgentOrchestrator()

    try:
        # Test Backend Agent with verification
        task_request = TaskRequest(
            task_description="""
Create a FastAPI endpoint for user registration that:
1. Accepts email and password
2. Validates email format
3. Hashes the password
4. Stores user in database
5. Returns JWT token
""",
            preferred_agent=AgentRole.BACKEND,
            verify_output=True,
            auto_fix=True
        )

        print("Executing Backend Agent task with verification...")
        result = await orchestrator.execute_task(task_request)

        print(f"Task Success: {result.success}")
        print(f"Agent: {result.agent_role}")
        print(f"Verification Status: {result.verification_status}")
        if result.dependencies_installed:
            print(f"Dependencies Installed: {result.dependencies_installed}")

        if result.result:
            print("\nGenerated Code:")
            print("=" * 50)
            print(result.result[:500] + "..." if len(result.result) > 500 else result.result)
            print("=" * 50)

        if result.error:
            print(f"Error: {result.error}")

    except Exception as e:
        print(f"Test failed with error: {e}")
    finally:
        await orchestrator.close()


async def test_dependency_management():
    """Test automatic dependency management."""
    print("\n=== Testing Dependency Management ===")

    orchestrator = AgentOrchestrator()

    try:
        # Test task that mentions dependencies
        task_request = TaskRequest(
            task_description="""
Create a FastAPI application with SQLAlchemy models for a blog system.
Include user authentication with JWT tokens using PyJWT.
Add API endpoints for creating and retrieving blog posts.
Use Pydantic for request/response models.
""",
            preferred_agent=AgentRole.BACKEND,
            auto_fix=True  # This should trigger dependency detection
        )

        print("Executing task with automatic dependency detection...")
        result = await orchestrator.execute_task(task_request)

        print(f"Task Success: {result.success}")
        print(f"Dependencies Installed: {result.dependencies_installed}")

        if result.result:
            print("\nGenerated Code (preview):")
            print("=" * 50)
            print(result.result[:300] + "..." if len(result.result) > 300 else result.result)
            print("=" * 50)

    except Exception as e:
        print(f"Test failed with error: {e}")
    finally:
        await orchestrator.close()


async def test_collaborative_workflow():
    """Test enhanced collaborative workflow."""
    print("\n=== Testing Enhanced Collaborative Workflow ===")

    orchestrator = AgentOrchestrator()

    try:
        # Test collaborative development with verification
        task_request = TaskRequest(
            task_description="""
Build a complete user management system with:
1. Backend API for user CRUD operations
2. Frontend React component for user list
3. Unit tests for the API endpoints
""",
            auto_fix=True
        )

        collaboration = AgentCollaboration(
            primary_agent=AgentRole.ARCHITECT,
            supporting_agents=[AgentRole.BACKEND, AgentRole.FRONTEND, AgentRole.TEST],
            coordination_strategy="sequential",
            verification_enabled=True,
            auto_dependency_management=True
        )

        print("Executing collaborative workflow...")
        results = await orchestrator.collaborate_agents(task_request, collaboration)

        print(f"\nCollaborative Results: {len(results)} agents participated")
        for i, result in enumerate(results):
            print(f"\n--- Agent {i+1}: {result.agent_role} ---")
            print(f"Success: {result.success}")
            print(f"Verification: {result.verification_status}")
            if result.error:
                print(f"Error: {result.error}")
            if result.result:
                preview = result.result[:200] + "..." if len(result.result) > 200 else result.result
                print(f"Result Preview: {preview}")

    except Exception as e:
        print(f"Test failed with error: {e}")
    finally:
        await orchestrator.close()


async def test_shell_agent_dependencies():
    """Test Shell Agent dependency installation specifically."""
    print("\n=== Testing Shell Agent Dependency Installation ===")

    orchestrator = AgentOrchestrator()

    try:
        # Direct Shell Agent test for dependency installation
        task_request = TaskRequest(
            task_description="Install the following Python packages: requests, pytest, black",
            preferred_agent=AgentRole.SHELL,
            verify_output=False,  # Shell commands don't need code verification
            auto_fix=False
        )

        print("Testing Shell Agent dependency installation...")
        result = await orchestrator.execute_task(task_request)

        print(f"Task Success: {result.success}")
        print(f"Agent: {result.agent_role}")

        if result.result:
            print("\nShell Agent Response:")
            print("=" * 50)
            print(result.result)
            print("=" * 50)

        if result.error:
            print(f"Error: {result.error}")

    except Exception as e:
        print(f"Test failed with error: {e}")
    finally:
        await orchestrator.close()


async def main():
    """Run all tests."""
    print("Enhanced AI Agent Orchestrator Test Suite")
    print("=========================================")

    # Test individual components
    await test_verification_system()
    await test_dependency_management()
    await test_collaborative_workflow()
    await test_shell_agent_dependencies()

    print("\n=== Test Suite Complete ===")


if __name__ == "__main__":
    asyncio.run(main())
