apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-coding-agent-frontend
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-coding-agent-frontend
  template:
    metadata:
      labels:
        app: ai-coding-agent-frontend
    spec:
      containers:
        - name: frontend
          image: ai-coding-agent-frontend:latest
          resources:
            requests:
              cpu: "250m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          ports:
            - containerPort: 80
          readinessProbe:
            httpGet:
              path: /nginx-health
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /nginx-health
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: ai-coding-agent-frontend
  namespace: ai-coding-agent
spec:
  selector:
    app: ai-coding-agent-frontend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
