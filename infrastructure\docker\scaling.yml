# AI Coding Agent - Scaling Configuration
# Docker Compose configuration for horizontal scaling and load balancing

version: '3.8'

services:
  # Load Balancer (HAProxy)
  load-balancer:
    image: haproxy:2.8-alpine
    container_name: ai-coding-agent-lb
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8404:8404"  # HAProxy stats
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ../ssl/certs:/etc/ssl/certs:ro
    networks:
      - ai-coding-agent-network
      - monitoring
    depends_on:
      - backend-1
      - backend-2
      - backend-3
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8404/stats"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Backend Service Instances (Scaled)
  backend-1:
    extends:
      file: production.yml
      service: backend
    container_name: ai-coding-agent-backend-1
    environment:
      - INSTANCE_ID=1
      - WORKER_PROCESSES=2
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
        reservations:
          cpus: '0.75'
          memory: 1.5G

  backend-2:
    extends:
      file: production.yml
      service: backend
    container_name: ai-coding-agent-backend-2
    ports: []  # Remove port mapping for scaled instances
    environment:
      - INSTANCE_ID=2
      - WORKER_PROCESSES=2
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
        reservations:
          cpus: '0.75'
          memory: 1.5G

  backend-3:
    extends:
      file: production.yml
      service: backend
    container_name: ai-coding-agent-backend-3
    ports: []  # Remove port mapping for scaled instances
    environment:
      - INSTANCE_ID=3
      - WORKER_PROCESSES=2
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
        reservations:
          cpus: '0.75'
          memory: 1.5G

  # Frontend Service Instances (Scaled)
  frontend-1:
    extends:
      file: production.yml
      service: frontend
    container_name: ai-coding-agent-frontend-1
    environment:
      - INSTANCE_ID=1

  frontend-2:
    extends:
      file: production.yml
      service: frontend
    container_name: ai-coding-agent-frontend-2
    ports: []  # Remove port mapping for scaled instances
    environment:
      - INSTANCE_ID=2

  frontend-3:
    extends:
      file: production.yml
      service: frontend
    container_name: ai-coding-agent-frontend-3
    ports: []  # Remove port mapping for scaled instances
    environment:
      - INSTANCE_ID=3

  # Vector DB service removed: now using PostgreSQL with pgvector extension (scale Postgres instead)
  # PostgreSQL Primary-Replica Setup
  postgres-primary:
    extends:
      file: production.yml
      service: postgres
    container_name: ai-coding-agent-postgres-primary
    environment:
      - POSTGRES_REPLICATION_MODE=master
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres-primary-data:/var/lib/postgresql/data
    command: >
      postgres
      -c wal_level=replica
      -c max_wal_senders=3
      -c max_replication_slots=3
      -c hot_standby=on

  postgres-replica:
    image: postgres:15-alpine
    container_name: ai-coding-agent-postgres-replica
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      - PGUSER=replicator
      - POSTGRES_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
      - POSTGRES_MASTER_SERVICE=postgres-primary
      - POSTGRES_REPLICATION_MODE=slave
    volumes:
      - postgres-replica-data:/var/lib/postgresql/data
    networks:
      - ai-coding-agent-network
      - monitoring
    depends_on:
      - postgres-primary
    command: >
      bash -c "
      until pg_basebackup --pgdata=/var/lib/postgresql/data -R --slot=replication_slot --host=postgres-primary --port=5432
      do
        echo 'Waiting for primary to connect...'
        sleep 1s
      done
      echo 'Backup done, starting replica...'
      chmod 0700 /var/lib/postgresql/data
      postgres
      "

  # Ollama Cluster (Multiple Model Servers)
  ollama-1:
    extends:
      file: production.yml
      service: ollama
    container_name: ai-coding-agent-ollama-1
    environment:
      - INSTANCE_ID=1
      - OLLAMA_MODELS=mistral:7b-instruct-q4_0,qwen2.5:3b
    volumes:
      - ollama-1-data:/root/.ollama

  ollama-2:
    extends:
      file: production.yml
      service: ollama
    container_name: ai-coding-agent-ollama-2
    ports:
      - "11435:11434"
    environment:
      - INSTANCE_ID=2
      - OLLAMA_MODELS=starcoder2:3b,deepseek-coder:6.7b-instruct
    volumes:
      - ollama-2-data:/root/.ollama

  ollama-3:
    extends:
      file: production.yml
      service: ollama
    container_name: ai-coding-agent-ollama-3
    ports:
      - "11436:11434"
    environment:
      - INSTANCE_ID=3
      - OLLAMA_MODELS=yi-coder:1.5b,nomic-embed-text:v1.5
    volumes:
      - ollama-3-data:/root/.ollama

  # Redis Cluster for Session Management and Caching
  redis-1:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis-1
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-1-data:/data
    networks:
      - ai-coding-agent-network
    command: redis-server --appendonly yes --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  redis-2:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis-2
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis-2-data:/data
    networks:
      - ai-coding-agent-network
    command: redis-server --appendonly yes --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000

  redis-3:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis-3
    restart: unless-stopped
    ports:
      - "6381:6379"
    volumes:
      - redis-3-data:/data
    networks:
      - ai-coding-agent-network
    command: redis-server --appendonly yes --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000

  # Auto-scaler service (custom implementation)
  autoscaler:
    build:
      context: ../../scripts/scaling
      dockerfile: Dockerfile
    container_name: ai-coding-agent-autoscaler
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./scaling-config.yml:/app/config.yml:ro
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
      - PROMETHEUS_URL=http://prometheus:9090
      - MIN_REPLICAS=2
      - MAX_REPLICAS=10
      - TARGET_CPU_UTILIZATION=70
      - TARGET_MEMORY_UTILIZATION=80
      - SCALE_UP_THRESHOLD=80
      - SCALE_DOWN_THRESHOLD=30
      - COOLDOWN_PERIOD=300
    networks:
      - ai-coding-agent-network
      - monitoring
    depends_on:
      - prometheus

# Networks
networks:
  ai-coding-agent-network:
    driver: bridge
    name: ai-coding-agent-scaling-network
    ipam:
      config:
        - subnet: **********/16
  monitoring:
    external: true

# Volumes for scaled services
volumes:
  # PostgreSQL volumes
  postgres-primary-data:
    driver: local
  postgres-replica-data:
    driver: local

  # Ollama volumes
  ollama-1-data:
    driver: local
  ollama-2-data:
    driver: local
  ollama-3-data:
    driver: local

  # Redis volumes
  redis-1-data:
    driver: local
  redis-2-data:
    driver: local
  redis-3-data:
    driver: local
