"""
Structured logging configuration for AI Coding Agent.

This module provides centralized logging configuration with
structured logging support using structlog.
"""

import logging
import sys
from typing import Any, Dict, Optional

import structlog
from structlog.typing import FilteringBoundLogger

from ai_coding_agent.config import settings


def configure_logging() -> None:
    """
    Configure structured logging for the application.

    Sets up structlog with JSON formatting for production
    and human-readable formatting for development.
    """
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=logging.DEBUG if settings.debug else logging.INFO,
    )

    # Configure structlog
    structlog.configure(
        processors=[
            # Add log level to event dict
            structlog.stdlib.add_log_level,
            # Add timestamp
            structlog.processors.TimeStamper(fmt="iso"),
            # Add logger name
            structlog.stdlib.add_logger_name,
            # Format for development vs production
            structlog.dev.ConsoleRenderer() if settings.debug else structlog.processors.JSO<PERSON>enderer(),
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> FilteringBoundLogger:
    """
    Get a configured structured logger.

    Args:
        name: Logger name (usually __name__)

    Returns:
        FilteringBoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


def log_request(
    method: str,
    url: str,
    status_code: int,
    response_time: float,
    user_id: Optional[int] = None,
    extra: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log HTTP request with structured data.

    Args:
        method: HTTP method
        url: Request URL
        status_code: Response status code
        response_time: Response time in seconds
        user_id: Optional user ID
        extra: Additional logging data
    """
    logger = get_logger("request")

    log_data = {
        "event": "http_request",
        "method": method,
        "url": url,
        "status_code": status_code,
        "response_time": response_time,
    }

    if user_id:
        log_data["user_id"] = user_id

    if extra:
        log_data.update(extra)

    if status_code >= 400:
        logger.error("HTTP request failed", **log_data)
    else:
        logger.info("HTTP request completed", **log_data)


def log_auth_event(
    event_type: str,
    user_id: Optional[int] = None,
    username: Optional[str] = None,
    success: bool = True,
    extra: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log authentication events for security monitoring.

    Args:
        event_type: Type of auth event (login, logout, register, etc.)
        user_id: Optional user ID
        username: Optional username
        success: Whether the event was successful
        extra: Additional logging data
    """
    logger = get_logger("auth")

    log_data = {
        "event": "auth_event",
        "event_type": event_type,
        "success": success,
    }

    if user_id:
        log_data["user_id"] = user_id

    if username:
        log_data["username"] = username

    if extra:
        log_data.update(extra)

    if success:
        logger.info("Authentication event", **log_data)
    else:
        logger.warning("Authentication failure", **log_data)


def log_ai_interaction(
    model: str,
    prompt_tokens: int,
    completion_tokens: int,
    response_time: float,
    user_id: Optional[int] = None,
    agent_type: Optional[str] = None,
    extra: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log AI model interactions for monitoring and billing.

    Args:
        model: AI model name
        prompt_tokens: Number of prompt tokens
        completion_tokens: Number of completion tokens
        response_time: Response time in seconds
        user_id: Optional user ID
        agent_type: Optional agent type
        extra: Additional logging data
    """
    logger = get_logger("ai")

    log_data = {
        "event": "ai_interaction",
        "model": model,
        "prompt_tokens": prompt_tokens,
        "completion_tokens": completion_tokens,
        "total_tokens": prompt_tokens + completion_tokens,
        "response_time": response_time,
    }

    if user_id:
        log_data["user_id"] = user_id

    if agent_type:
        log_data["agent_type"] = agent_type

    if extra:
        log_data.update(extra)

    logger.info("AI interaction completed", **log_data)
