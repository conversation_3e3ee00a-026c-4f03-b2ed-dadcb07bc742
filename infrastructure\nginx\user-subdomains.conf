# AI Coding Agent - User Subdomains Configuration
# Dynamic subdomain routing for user containers: preview-{user_id}.yourdomain.com
# This configuration is used by the DynamicHosting service for container-per-user architecture

# Rate limiting zones for user subdomains
limit_req_zone $binary_remote_addr zone=user_subdomain:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=user_api:10m rate=15r/s;
limit_req_zone $binary_remote_addr zone=user_ws:10m rate=10r/s;

# Map user ID from subdomain to container port
map $http_host $user_id {
    default "";
    ~^preview-(?<uid>\w+)\.yourdomain\.com$ $uid;
    ~^preview-(?<uid>\w+)\.ai-coding-agent\.local$ $uid;
    ~^(?<uid>\w+)\.preview\.localhost$ $uid;
}

# Map user ID to container port (dynamically updated by DynamicHosting service)
map $user_id $container_port {
    default 3000;
    # Dynamic entries added by DynamicHosting.generate_subdomain_config():
    # user123 3001;
    # user456 3002;
    # etc.
}

# Upstream template for user containers
upstream user_container_template {
    server 127.0.0.1:3000 max_fails=2 fail_timeout=10s;
    keepalive 4;
}

# Main server block for user subdomains
server {
    listen 80;
    server_name ~^preview-(?<user_id>\w+)\.(yourdomain\.com|ai-coding-agent\.local)$;
    server_name ~^(?<user_id>\w+)\.preview\.localhost$;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-User-Container $user_id always;
    
    # Rate limiting
    limit_req zone=user_subdomain burst=40 nodelay;
    
    # Validate user ID exists (basic check)
    if ($user_id = "") {
        return 404 "Invalid user subdomain";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:$container_port/health;
        proxy_connect_timeout 3s;
        proxy_send_timeout 3s;
        proxy_read_timeout 3s;
        add_header X-User-Container $user_id always;
    }
    
    # WebSocket support for hot reload and development
    location /ws/ {
        limit_req zone=user_ws burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:$container_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        
        # User isolation
        add_header X-User-Container $user_id always;
    }
    
    # API routes for user projects
    location /api/ {
        limit_req zone=user_api burst=30 nodelay;
        
        proxy_pass http://127.0.0.1:$container_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # User isolation
        add_header X-User-Container $user_id always;
    }
    
    # File upload with size limits
    location /upload/ {
        limit_req zone=user_api burst=5 nodelay;
        client_max_body_size 100M;
        client_body_timeout 60s;
        
        proxy_pass http://127.0.0.1:$container_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_request_buffering off;
        
        # User isolation
        add_header X-User-Container $user_id always;
    }
    
    # Static assets with caching
    location /static/ {
        proxy_pass http://127.0.0.1:$container_port;
        expires 30m;
        add_header Cache-Control "public";
        add_header X-User-Container $user_id always;
    }
    
    # Development server assets (React, Vite, etc.)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:$container_port;
        expires 1h;
        add_header Cache-Control "public";
        add_header X-User-Container $user_id always;
    }
    
    # Main application routes
    location / {
        proxy_pass http://127.0.0.1:$container_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # User isolation
        add_header X-User-Container $user_id always;
        
        # Handle SPA routing (React Router, Vue Router, etc.)
        try_files $uri $uri/ /index.html;
    }
    
    # Security: Block access to sensitive files and directories
    location ~ /\.(env|git|svn|htaccess|htpasswd|DS_Store) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(log|conf|config|ini|sql|bak|backup)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(node_modules|\.git|\.svn|\.hg|\.bzr) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Error handling
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
        add_header X-User-Container $user_id always;
        add_header Content-Type text/html;
    }
    
    # Custom error page for container not found
    error_page 404 /404.html;
    location = /404.html {
        root /usr/share/nginx/html;
        add_header X-User-Container $user_id always;
        add_header Content-Type text/html;
    }
}

# Default server for non-matching subdomains
server {
    listen 80 default_server;
    server_name _;
    
    location / {
        return 404 "User project subdomain not found. Use format: preview-{user_id}.yourdomain.com";
    }
}

# HTTPS server block (for production with SSL)
server {
    listen 443 ssl http2;
    server_name ~^preview-(?<user_id>\w+)\.yourdomain\.com$;
    
    # SSL configuration (use your SSL certificates)
    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    
    # Include all the same location blocks as the HTTP server
    # (This would be a copy of the above configuration with HTTPS settings)
    
    # Redirect to HTTP version for development
    # In production, implement full HTTPS configuration
    return 301 http://$server_name$request_uri;
}
