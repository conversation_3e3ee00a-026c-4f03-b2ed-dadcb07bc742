# AI Coding Agent - Development Commands Quick Reference

*Essential commands for containerized development workflow*

## 🚀 Mode Management

### **Start Development Mode**
```bash
# Full stack development
# Use only the root Compose file for development and production
# For hot reload, mount source code as needed in docker-compose.yml

docker-compose up -d --build
```

### **Start Production Mode**
```bash
# Stop development first

docker-compose down

# Start production

docker-compose up -d
```

### **Clean Rebuild**
```bash
# Stop everything

docker-compose down

# Remove containers and images

docker-compose rm -f
docker rmi $(docker images -q ai-coding-agent-*)

# Prune cache and rebuild

docker system prune -f
docker-compose build --no-cache
docker-compose up -d
```

## 🔍 Status & Health Checks

### **Container Status**
```bash
# All containers
docker ps

# Specific service
docker ps | findstr backend

# Health status
docker inspect ai-coding-agent-app | findstr Health -A 5
```

### **Service Health**
```bash
# Backend API
curl http://localhost:8000/api/v1/health
# Frontend (served by consolidated app)
curl http://localhost:3000
# NGINX proxy
curl http://localhost/health
```

## 📝 Development Workflow

### **Backend Development**
```bash
# Edit files in: backend/src/ai_coding_agent/
# Changes auto-apply via hot reload

# Test endpoint
curl http://localhost:8000/api/v1/containers/dev-status

# Check logs
docker logs ai-coding-agent-backend -f

# Run tests
docker exec ai-coding-agent-backend python -m pytest tests/
```

### **Frontend Development**
```bash
# Option A: Local development (recommended)
cd frontend
npm install
npm start
# Access: http://localhost:3000

# Option B: Container development
docker-compose -f docker-compose.yml -f scripts/setup/docker-compose.dev.yml up -d frontend
# Access: http://localhost:3001
```

## 🧪 Testing Commands

### **Backend Testing**
```bash
# All tests
docker exec ai-coding-agent-backend python -m pytest tests/

# Specific test file
docker exec ai-coding-agent-backend python -m pytest tests/test_containers.py -v

# With coverage
docker exec ai-coding-agent-backend python -m pytest --cov=ai_coding_agent tests/

# Unit tests only
docker exec ai-coding-agent-backend python -m pytest tests/unit/

# Integration tests
docker exec ai-coding-agent-backend python -m pytest tests/integration/
```

### **Frontend Testing**
```bash
# Local testing
cd frontend && npm test

# Container testing
docker exec ai-coding-agent-frontend npm test
```

## 🗄️ Database Management

### **PostgreSQL**
```bash
# Connect to database
docker exec -it ai-coding-agent-postgres psql -U agent -d ai_coding_agent

# Run migrations
docker exec ai-coding-agent-backend alembic upgrade head

# Create migration
docker exec ai-coding-agent-backend alembic revision --autogenerate -m "description"

# Check database health
docker exec ai-coding-agent-postgres pg_isready -U agent
```

### **Redis**
```bash
# Connect to Redis
docker exec -it ai-coding-agent-redis redis-cli

# Monitor Redis activity
docker exec ai-coding-agent-redis redis-cli monitor

# Check Redis info
docker exec ai-coding-agent-redis redis-cli info
```

## 📊 Monitoring & Debugging

### **Logs**
```bash
# Backend logs
docker logs ai-coding-agent-backend -f

# Frontend logs
docker logs ai-coding-agent-frontend -f

# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
```

### **Resource Monitoring**
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Network inspection
docker network ls
docker network inspect aicodingaagent_ai-coding-agent-network
```

## 🔧 Troubleshooting

### **Common Fixes**
```bash
# Restart specific service
docker-compose restart backend

# Rebuild specific service
docker-compose build backend
docker-compose up -d backend

# Check port conflicts
netstat -ano | findstr :8000

# Reset development environment
docker-compose -f docker-compose.yml -f scripts/setup/docker-compose.dev.yml down
docker-compose -f docker-compose.yml -f scripts/setup/docker-compose.dev.yml up -d
```

### **Emergency Reset**
```bash
# Nuclear option - reset everything
docker-compose down
docker system prune -a -f
docker volume prune -f
docker-compose build --no-cache
docker-compose up -d
```

## 🎯 Implementation Plan Workflow

### **Working on Consolidated Plan Items**
```bash
# 1. Start development mode
docker-compose -f docker-compose.yml -f scripts/setup/docker-compose.dev.yml up -d

# 2. Edit code locally (hot reload active)
code backend/src/ai_coding_agent/services/container_manager.py

# 3. Test immediately
curl http://localhost:8000/api/v1/containers/implementation-progress

# 4. Run tests
docker exec ai-coding-agent-backend python -m pytest tests/test_containers.py

# 5. Check logs if needed
docker logs ai-coding-agent-backend -f
```

## 📋 Development Checklist

### **Before Starting Work**
- [ ] `docker ps` - containers running
- [ ] `curl http://localhost:8000/api/v1/health` - backend healthy
- [ ] `curl http://localhost:8000/api/v1/containers/dev-status` - dev mode active
- [ ] Virtual environment activated

### **Before Committing**
- [ ] Tests passing: `docker exec ai-coding-agent-backend python -m pytest tests/`
- [ ] No errors in logs: `docker logs ai-coding-agent-backend`
- [ ] Health checks green: `curl http://localhost:8000/api/v1/health`
- [ ] Hot reload working: `curl http://localhost:8000/api/v1/containers/dev-status`

### **Before Production Deploy**
- [ ] Switch to production mode: `docker-compose up -d`
- [ ] Production health check: `curl http://localhost:8000/api/v1/health`
- [ ] All services healthy: `docker ps`
- [ ] Performance tests passing

## 🔗 Quick Access URLs

- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Frontend Local**: http://localhost:3000
- **Frontend Container**: http://localhost:3001
- **NGINX Proxy**: http://localhost
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## 📚 Documentation Links

- **Full Workflow**: `docs/DEVELOPMENT_WORKFLOW.md`
- **Implementation Plan**: `docs/CONSOLIDATED_IMPLEMENTATION_PLAN.md`
- **Project Rules**: `.augment/rules/Project-rules.md`
- **Architecture**: `docs/dockerarchitecture.md`
