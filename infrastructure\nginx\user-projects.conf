# AI Coding Agent - User Projects Configuration
# Nginx configuration for user project hosting and container routing

# Rate limiting for user projects
limit_req_zone $binary_remote_addr zone=user_projects:10m rate=30r/s;
limit_req_zone $binary_remote_addr zone=user_uploads:10m rate=5r/s;

# Map for user container ports (dynamically updated)
map $http_host $user_container_port {
    default 3000;
    # Dynamic entries will be added here by the DynamicHosting service
    # Example: preview-user123.yourdomain.com 3001;
}

# Upstream for user containers (dynamically managed)
upstream user_containers {
    # This will be dynamically populated by the container manager
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

server {
    listen 80;
    server_name ~^preview-(?<user_id>\w+)\.yourdomain\.com$;
    
    # Security headers for user projects
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Rate limiting for user projects
    limit_req zone=user_projects burst=60 nodelay;
    
    # Health check for user containers
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:$user_container_port/health;
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }
    
    # WebSocket support for hot reload
    location /ws/ {
        proxy_pass http://127.0.0.1:$user_container_port;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        
        # User isolation headers
        add_header X-User-Container $user_id always;
    }
    
    # File upload endpoint with rate limiting
    location /upload/ {
        limit_req zone=user_uploads burst=10 nodelay;
        client_max_body_size 50M;
        
        proxy_pass http://127.0.0.1:$user_container_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_request_buffering off;
        
        # User isolation headers
        add_header X-User-Container $user_id always;
    }
    
    # API routes for user projects
    location /api/ {
        limit_req zone=user_projects burst=30 nodelay;
        
        proxy_pass http://127.0.0.1:$user_container_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # User isolation headers
        add_header X-User-Container $user_id always;
    }
    
    # Static assets with caching
    location /static/ {
        proxy_pass http://127.0.0.1:$user_container_port;
        expires 1h;
        add_header Cache-Control "public";
        add_header X-User-Container $user_id always;
    }
    
    # Main application routes
    location / {
        proxy_pass http://127.0.0.1:$user_container_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_http_version 1.1;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # User isolation headers
        add_header X-User-Container $user_id always;
        
        # Handle SPA routing
        try_files $uri $uri/ /index.html;
    }
    
    # Security: Block access to sensitive files
    location ~ /\.(env|git|svn|htaccess|htpasswd) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(log|conf|config)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Error handling
    error_page 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
        add_header X-User-Container $user_id always;
    }
}

# Default server block for non-matching hosts
server {
    listen 80 default_server;
    server_name _;
    
    # Return 404 for non-matching user project domains
    location / {
        return 404 "User project not found";
    }
}
