"""
Check DB connectivity for Supabase, Redis, and local Postgres.
"""
import os
from database.supabase_client import get_supabase, query_table
from database.redis_client import redis_set, redis_get
from database.pg_client import run_sql

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
DATABASE_URL = os.getenv("DATABASE_URL")
REDIS_URL = os.getenv("REDIS_URL")

def check_supabase():
    """Check the Supabase connection."""
    print("Checking Supabase connection...")
    try:
        result = query_table("pgvector_table")  # Replace with your table name
        print("Supabase SELECT success:", result)
    except Exception as e:
        print("Supabase error:", e)

def check_postgres():
    """Check the local Postgres connection."""
    print("Checking local Postgres connection...")
    if not DATABASE_URL:
        print("DATABASE_URL not set, skipping local Postgres check.")
        return
    try:
        result = run_sql("SELECT 1;")
        print("Postgres SELECT success:", result)
    except Exception as e:
        print("Postgres error:", e)

def check_redis():
    """Check the Redis connection."""
    print("Checking Redis connection...")
    try:
        redis_set("test_key", "test_value")
        val = redis_get("test_key")
        print("Redis SET/GET success:", val)
    except Exception as e:
        print("Redis error:", e)

def main():
    """Main function to check all connections."""
    check_supabase()
    check_postgres()
    check_redis()

if __name__ == "__main__":
    main()
