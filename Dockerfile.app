# Combined Dockerfile for backend (FastAPI) + frontend (React/Vite)
# Multi-stage build: frontend build, backend build, final runtime
# This architecture reduces resource usage and simplifies development.

# --- Frontend Build Stage ---
FROM node:20-alpine AS frontend-build
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --omit=dev
COPY frontend/ .
RUN npm run build

# --- Backend Build Stage ---
FROM python:3.11-slim AS backend-build
WORKDIR /app/backend
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt
COPY backend/ .

# --- Final Runtime Stage ---
FROM python:3.11-alpine AS runtime
WORKDIR /app
# Create non-root user for security (UID 1000, GID 1000)
RUN addgroup -g 1000 appuser && adduser -D -u 1000 -G appuser appuser
# Create logs directory with proper permissions
RUN mkdir -p /app/backend/src/ai_coding_agent/logs && \
    chown -R 1000:1000 /app/backend/src/ai_coding_agent/logs
USER appuser
# Copy backend
COPY --from=backend-build /app/backend /app/backend
# Copy frontend build to backend static directory
COPY --from=frontend-build /app/frontend/dist /app/backend/static
EXPOSE 8000
CMD ["python", "-m", "backend.main"]

# FastAPI should serve /app/backend/static as static files for the frontend.
# This setup allows you to run everything with one container and one command.
# To run migrations after startup:
#   docker compose exec app python -m database.migrations
