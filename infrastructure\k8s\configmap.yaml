apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-backend
    project: ai-coding-agent

data:
  POSTGRES_HOST: "ai-coding-agent-db"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "ai_coding_agent"
  REDIS_HOST: "ai-coding-agent-cache"
  REDIS_PORT: "6379"
  DEBUG: "true"
  LOG_LEVEL: "debug"
  API_URL: "http://ai-coding-agent-backend"
  FRONTEND_URL: "http://ai-coding-agent-frontend"
