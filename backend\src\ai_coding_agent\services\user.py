"""
User management service for CRUD operations.

This module provides user management functionality including
creation, updates, and user queries.
"""

from typing import List, Optional

from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from ai_coding_agent.models import User, UserCreate, UserUpdate


def create_user(db: Session, user: UserCreate) -> User:
    """
    Create a new user in the database.

    Args:
        db: Database session
        user: User creation data

    Returns:
        User: The created user

    Raises:
        HTTPException: If username or email already exists
    """
    # Check if username already exists
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )

    # Check if email already exists
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create user (password handled by <PERSON><PERSON><PERSON> Auth)
    db_user = User(
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        is_active=user.is_active,
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def get_user(db: Session, user_id: int) -> Optional[User]:
    """
    Get a user by ID.

    Args:
        db: Database session
        user_id: User ID to retrieve

    Returns:
        User: The user object, or None if not found
    """
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """
    Get a user by email address.

    Args:
        db: Database session
        email: Email address to search for

    Returns:
        User: The user object, or None if not found
    """
    return db.query(User).filter(User.email == email).first()


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """
    Get a user by username.

    Args:
        db: Database session
        username: Username to search for

    Returns:
        User: The user object, or None if not found
    """
    return db.query(User).filter(User.username == username).first()


def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
    """
    Get a list of users with pagination.

    Args:
        db: Database session
        skip: Number of records to skip
        limit: Maximum number of records to return

    Returns:
        List[User]: List of user objects
    """
    return db.query(User).offset(skip).limit(limit).all()


def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
    """
    Update an existing user.

    Args:
        db: Database session
        user_id: ID of user to update
        user_update: User update data

    Returns:
        User: The updated user, or None if not found

    Raises:
        HTTPException: If username or email conflicts exist
    """
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return None

    # Check for username conflicts (if username is being updated)
    if user_update.username and user_update.username != db_user.username:
        existing_user = db.query(User).filter(User.username == user_update.username).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )

    # Check for email conflicts (if email is being updated)
    if user_update.email and user_update.email != db_user.email:
        existing_user = db.query(User).filter(User.email == user_update.email).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

    # Update user fields
    update_data = user_update.dict(exclude_unset=True)

    # Remove password from update data (handled by Supabase Auth)
    if "password" in update_data:
        update_data.pop("password")  # Password updates handled by Supabase

    for field, value in update_data.items():
        setattr(db_user, field, value)

    db.commit()
    db.refresh(db_user)
    return db_user


def delete_user(db: Session, user_id: int) -> bool:
    """
    Delete a user from the database.

    Args:
        db: Database session
        user_id: ID of user to delete

    Returns:
        bool: True if user was deleted, False if not found
    """
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return False

    db.delete(db_user)
    db.commit()
    return True
