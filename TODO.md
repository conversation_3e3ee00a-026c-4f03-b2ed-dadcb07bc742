# AI Coding Agent Monitoring & Metrics TODO

## Backend Metrics Implementation
- [ ] Install `prometheus-fastapi-instrumentator` in backend
- [ ] Add `/api/v1/metrics` endpoint to FastAPI app
- [ ] Implement custom Prometheus metrics:
    - [ ] Request rate per endpoint
    - [ ] Response time percentiles
    - [ ] Active user sessions
    - [ ] Database connection pool usage
    - [ ] Memory and CPU utilization

## Frontend/Nginx Metrics
- [ ] Enable Nginx `stub_status` module
- [ ] Expose `/nginx_status` endpoint for Prometheus scraping

## Grafana Dashboard Setup
- [ ] Access Grafana at `http://localhost:3001`
- [ ] Add Prometheus data source in Grafana
- [ ] Import or create dashboards for:
    - [ ] FastAPI metrics
    - [ ] Nginx metrics
    - [ ] Node Exporter metrics
    - [ ] Custom application metrics
- [ ] Create panels for:
    - [ ] Request rate
    - [ ] Response time (95th percentile)
    - [ ] Active user sessions
    - [ ] DB pool usage
    - [ ] CPU/Memory usage

## Next Steps
- [ ] Add metrics code to backend
- [ ] Enable `/nginx_status` in Nginx
- [ ] Start Grafana and create dashboards
- [ ] Monitor custom metrics and system health visually
