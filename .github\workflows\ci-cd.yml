# GitHub Actions CI/CD Workflow for AI Coding Agent
# Follows ultra-optimized, consolidated container setup and all project rules

name: CI/CD Pipeline

on:
  push:
    branches: [main, staging, dev]
  pull_request:
    branches: [main, staging, dev]
  workflow_dispatch:

jobs:
  build-test-deploy:
    runs-on: ubuntu-latest
    env:
      COMPOSE_FILE: docker-compose.yml
      ENV_FILE: .env.production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install backend dependencies
        run: |
          cd backend
          python -m venv venv
          source venv/bin/activate
          pip install -r requirements.txt

      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci

      - name: Run backend tests
        run: |
          cd backend
          source venv/bin/activate
          pytest

      - name: Run frontend tests
        run: |
          cd frontend
          npm run test

      - name: Build Docker images
        run: |
          docker-compose -f $COMPOSE_FILE build

      - name: Run Trivy vulnerability scan
        uses: aquasecurity/trivy-action@v0.13.0
        with:
          image-ref: 'ai-coding-agent-backend-prod'
          format: 'table'
          exit-code: '1'
          ignore-unfixed: true

      - name: Deploy containers
        run: |
          docker-compose -f $COMPOSE_FILE up -d

      - name: Health check (backend)
        run: |
          curl --fail http://localhost:8000/health

      - name: Health check (frontend)
        run: |
          curl --fail http://localhost:3000

      - name: Resource usage validation
        run: |
          docker stats --no-stream

      - name: Audit trail & compliance report
        run: |
          python scripts/audit_report.py

      - name: Cleanup unused containers/images/volumes
        run: |
          bash scripts/cleanup_docker.sh

      - name: Upload compliance report
        uses: actions/upload-artifact@v4
        with:
          name: audit-report
          path: scripts/audit_report_output.json

  schedule-monitoring:
    runs-on: ubuntu-latest
    schedule:
      - cron: '0 * * * *'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Scheduled resource monitoring
        run: |
          python scripts/resource_monitor.py
