#!/bin/bash
# Redis Cluster Initialization Script for AI Coding Agent

set -e

echo "🚀 Initializing Redis Cluster for AI Coding Agent..."

# Wait for Redis instances to be ready
echo "⏳ Waiting for Redis instances to start..."
for i in {1..30}; do
    if docker exec ai-coding-agent-redis-1 redis-cli ping >/dev/null 2>&1 && \
       docker exec ai-coding-agent-redis-2 redis-cli ping >/dev/null 2>&1 && \
       docker exec ai-coding-agent-redis-3 redis-cli ping >/dev/null 2>&1; then
        echo "✅ All Redis instances are ready"
        break
    fi
    echo "   Attempt $i/30: Waiting for Redis instances..."
    sleep 2
done

# Check if cluster is already initialized
if docker exec ai-coding-agent-redis-1 redis-cli cluster nodes 2>/dev/null | grep -q "master"; then
    echo "✅ Redis cluster already initialized"
    exit 0
fi

# Get Redis container IPs
REDIS_1_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ai-coding-agent-redis-1)
REDIS_2_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ai-coding-agent-redis-2)
REDIS_3_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ai-coding-agent-redis-3)

echo "📍 Redis IPs:"
echo "   Redis-1: $REDIS_1_IP:6379"
echo "   Redis-2: $REDIS_2_IP:6379"
echo "   Redis-3: $REDIS_3_IP:6379"

# Create cluster
echo "🔧 Creating Redis cluster..."
docker exec ai-coding-agent-redis-1 redis-cli --cluster create \
    $REDIS_1_IP:6379 \
    $REDIS_2_IP:6379 \
    $REDIS_3_IP:6379 \
    --cluster-replicas 0 \
    --cluster-yes

# Verify cluster status
echo "🔍 Verifying cluster status..."
docker exec ai-coding-agent-redis-1 redis-cli cluster info
docker exec ai-coding-agent-redis-1 redis-cli cluster nodes

echo "✅ Redis cluster initialization complete!"

# Test cluster functionality
echo "🧪 Testing cluster functionality..."
docker exec ai-coding-agent-redis-1 redis-cli set test-key "cluster-test"
RESULT=$(docker exec ai-coding-agent-redis-2 redis-cli get test-key)

if [ "$RESULT" = "cluster-test" ]; then
    echo "✅ Cluster test successful!"
    docker exec ai-coding-agent-redis-1 redis-cli del test-key
else
    echo "❌ Cluster test failed!"
    exit 1
fi

echo "🎉 Redis cluster is ready for AI Coding Agent scaling!"
