# Unified Rate Limiting System

## Overview

The AI Coding Agent uses a comprehensive, unified rate limiting system that provides consistent protection across all endpoints while maintaining flexibility for different use cases.

## Architecture

### Core Components

1. **`services/rate_limit_service.py`** - Core business logic and Redis integration
2. **`middleware/unified_rate_limiting.py`** - FastAPI middleware for automatic protection
3. **`config/settings.py`** - Centralized configuration management

### Rate Limit Types

The system supports five distinct rate limit types:

| Type | Scope | Default Limit | Window | Purpose |
|------|-------|---------------|--------|---------|
| **ADMIN** | IP | 20 requests | 5 minutes | Admin panel protection |
| **AI** | User | 100 requests | 1 minute | AI operation limits |
| **API** | IP | 1000 requests | 1 minute | General API protection |
| **AUTH** | IP | 5 requests | 15 minutes | Authentication security |
| **UPLOAD** | IP | 10 requests | 5 minutes | File upload limits |

## Configuration

### Environment Variables

All rate limits are configurable via environment variables:

```env
# General Settings
RATE_LIMIT_WINDOW_SECONDS=60

# Admin Rate Limits
RATE_LIMIT_ADMIN_LIMIT=20
RATE_LIMIT_ADMIN_WINDOW_SECONDS=300
RATE_LIMIT_ADMIN_BLOCK_DURATION=900

# AI Rate Limits
RATE_LIMIT_AI_LIMIT=100
RATE_LIMIT_AI_BURST_LIMIT=20

# API Rate Limits
RATE_LIMIT_API_LIMIT=1000
RATE_LIMIT_API_BURST_LIMIT=200

# Auth Rate Limits
RATE_LIMIT_AUTH_LIMIT=5
RATE_LIMIT_AUTH_WINDOW_SECONDS=900
RATE_LIMIT_AUTH_BLOCK_DURATION=3600

# Upload Rate Limits
RATE_LIMIT_UPLOAD_LIMIT=10
RATE_LIMIT_UPLOAD_WINDOW_SECONDS=300
RATE_LIMIT_UPLOAD_BURST_LIMIT=2
```

### Redis Configuration

The system uses Redis for distributed rate limiting:

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=changeme123
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5
```

## Usage

### Automatic Protection

The `UnifiedRateLimitMiddleware` automatically applies appropriate rate limits based on endpoint patterns:

```python
# In main.py
from .middleware.unified_rate_limiting import UnifiedRateLimitMiddleware

app.add_middleware(UnifiedRateLimitMiddleware)
```

### Endpoint Pattern Matching

| Pattern | Rate Limit Type |
|---------|----------------|
| `/api/v1/admin/` | ADMIN |
| `/api/v1/ai/` | AI |
| `/api/v1/auth/` | AUTH |
| `/api/v1/upload/` | UPLOAD |
| `/api/` | API (default) |

### Manual Rate Limiting

For specific endpoints requiring custom limits:

```python
from ..middleware.unified_rate_limiting import check_ai_rate_limit

@router.post("/special-ai-endpoint")
async def special_endpoint(request: Request):
    await check_ai_rate_limit(request, user_id="user123")
    # Your endpoint logic here
```

### Custom Rate Limits

For endpoints requiring unique rate limiting:

```python
from ..middleware.unified_rate_limiting import custom_rate_limit
from ..services.rate_limit_service import RateLimitScope

@custom_rate_limit(
    limit=50, 
    window_seconds=300, 
    scope=RateLimitScope.USER,
    block_duration_seconds=600
)
async def custom_endpoint(request: Request):
    # Your endpoint logic here
```

## Features

### 1. Multi-Scope Rate Limiting

- **IP-based**: Default for most endpoints
- **User-based**: For AI operations requiring per-user limits
- **Global**: For system-wide limits

### 2. Burst Protection

Configurable burst limits allow temporary spikes while maintaining overall rate control.

### 3. Automatic Blocking

IPs exceeding limits are automatically blocked for configurable durations.

### 4. Fallback Support

When Redis is unavailable, the system falls back to in-memory rate limiting.

### 5. Comprehensive Headers

All responses include standard rate limit headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
Retry-After: 60
```

### 6. Audit Integration

All rate limit violations are automatically logged to the audit system.

## Monitoring

### Rate Limit Headers

Monitor rate limit usage through response headers:

- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Requests remaining in current window
- `X-RateLimit-Reset`: Unix timestamp when limit resets
- `Retry-After`: Seconds to wait before retrying (when blocked)

### Audit Logs

Rate limit violations generate security events in the audit system:

```json
{
  "user_id": "user123",
  "action": "rate_limit_exceeded",
  "resource": "ai_endpoint",
  "ip_address": "*************",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Testing

### Validation Script

Run the comprehensive validation script:

```bash
cd backend
python scripts/validate_rate_limiting.py
```

This script validates:
- Configuration loading
- Service initialization
- Rate limiting logic
- Middleware integration
- Environment variables

### Manual Testing

Test rate limits manually:

```bash
# Test API rate limit
for i in {1..10}; do
  curl -H "Authorization: Bearer $TOKEN" http://localhost:8000/api/v1/test
done

# Test AI rate limit
for i in {1..5}; do
  curl -H "Authorization: Bearer $TOKEN" \
       -H "Content-Type: application/json" \
       -d '{"message": "test"}' \
       http://localhost:8000/api/v1/ai/chat
done
```

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   - Verify Redis is running: `redis-cli ping`
   - Check connection settings in `.env`
   - Review Redis logs for authentication issues

2. **Rate Limits Not Applied**
   - Verify middleware is registered in `main.py`
   - Check endpoint patterns match expected routes
   - Review application logs for errors

3. **Incorrect Rate Limit Values**
   - Validate environment variables are loaded
   - Check `.env` file syntax
   - Restart application after configuration changes

### Debug Mode

Enable debug logging for rate limiting:

```env
LOG_LEVEL=DEBUG
```

This will log all rate limit checks and decisions.

## Security Considerations

1. **Redis Security**: Use strong passwords and network isolation
2. **Rate Limit Bypass**: Monitor for attempts to bypass rate limits
3. **DDoS Protection**: Rate limiting is one layer; use additional DDoS protection
4. **User Enumeration**: Be careful not to leak user existence through rate limits

## Performance

### Redis Optimization

- Use connection pooling (configured automatically)
- Monitor Redis memory usage
- Consider Redis clustering for high-traffic deployments

### Fallback Performance

In-memory fallback has limitations:
- Not distributed across multiple servers
- Lost on application restart
- Higher memory usage for high-traffic applications

## Migration from Old System

The unified system replaces these deprecated files:
- ❌ `middleware/rate_limiting.py`
- ❌ `middleware/ai_rate_limiting.py`
- ❌ `utils/rate_limit.py`
- ❌ `utils/rate_limit_middleware.py`

All functionality has been consolidated into the unified system with enhanced features and better configuration management.
