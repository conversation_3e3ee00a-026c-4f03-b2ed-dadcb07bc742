"""
Service layer for AI Coding Agent.

This module contains business logic and service implementations including:
- Unified audit system for comprehensive logging and compliance
- Rate limiting service for API protection
- Container management and monitoring
- AI orchestration and providers
- User data management and authentication
- Vector database and knowledge management
- Roadmap and dependency management
"""

# Core Services
from ai_coding_agent.services import user
from ai_coding_agent.services import user_data_manager
from ai_coding_agent.services import audit_service
from ai_coding_agent.services import rate_limit_service

# AI & LLM Services
from ai_coding_agent.services import ai
from ai_coding_agent.services import universal_llm
from ai_coding_agent.services import cloud_ai_providers
from ai_coding_agent.services import ai_container_agent

# Container & Infrastructure Services
from ai_coding_agent.services import container_manager
from ai_coding_agent.services import container_monitoring
from ai_coding_agent.services import dynamic_hosting

# Data & Storage Services
from ai_coding_agent.services import vector_db
from ai_coding_agent.services import redis_cache
from ai_coding_agent.services import supabase
from ai_coding_agent.services import supabase_auth

# Project Management Services
from ai_coding_agent.services import roadmap
from ai_coding_agent.services import dependency_engine
from ai_coding_agent.services import schema_validation
from ai_coding_agent.services import versioning

# Security & Configuration Services
from ai_coding_agent.services import rule_enforcement
from ai_coding_agent.services import secure_config

__all__ = [
    # Core Services
    "user",
    "user_data_manager",
    "audit_service",
    "rate_limit_service",

    # AI & LLM Services
    "ai",
    "universal_llm",
    "cloud_ai_providers",
    "ai_container_agent",

    # Container & Infrastructure Services
    "container_manager",
    "container_monitoring",
    "dynamic_hosting",

    # Data & Storage Services
    "vector_db",
    "redis_cache",
    "supabase",
    "supabase_auth",

    # Project Management Services
    "roadmap",
    "dependency_engine",
    "schema_validation",
    "versioning",

    # Security & Configuration Services
    "rule_enforcement",
    "secure_config",
]
