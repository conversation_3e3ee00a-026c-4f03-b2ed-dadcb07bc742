"""
Service layer for AI Coding Agent.

This module contains business logic and service implementations including:
- Unified audit system for comprehensive logging and compliance
- Rate limiting service for API protection
- Container management and monitoring
- AI orchestration and providers
- User data management and authentication
- Vector database and knowledge management
- Roadmap and dependency management
"""

# Core Services
from . import user
from . import user_data_manager
from . import audit_service
from . import rate_limit_service

# AI & LLM Services
from . import ai
from . import universal_llm
from . import cloud_ai_providers
from . import ai_container_agent

# Container & Infrastructure Services
from . import container_manager
from . import container_monitoring
from . import dynamic_hosting

# Data & Storage Services
from . import vector_db
from . import redis_cache
from . import supabase
from . import supabase_auth

# Project Management Services
from . import roadmap
from . import dependency_engine
from . import schema_validation
from . import versioning

# Security & Configuration Services
from . import rule_enforcement
from . import secure_config

__all__ = [
    # Core Services
    "user",
    "user_data_manager",
    "audit_service",
    "rate_limit_service",

    # AI & LLM Services
    "ai",
    "universal_llm",
    "cloud_ai_providers",
    "ai_container_agent",

    # Container & Infrastructure Services
    "container_manager",
    "container_monitoring",
    "dynamic_hosting",

    # Data & Storage Services
    "vector_db",
    "redis_cache",
    "supabase",
    "supabase_auth",

    # Project Management Services
    "roadmap",
    "dependency_engine",
    "schema_validation",
    "versioning",

    # Security & Configuration Services
    "rule_enforcement",
    "secure_config",
]
