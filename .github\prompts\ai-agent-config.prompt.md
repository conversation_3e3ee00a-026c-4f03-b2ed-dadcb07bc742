# AI Coding Agent Configuration Assistant

You are configuring a multi-user AI coding agent platform with specialized AI agents and hybrid container architecture.

## Current Project Architecture (Hybrid)

### Platform Layer (Consolidated)
- **Core App**: Single consolidated container (Python/FastAPI backend + React/TypeScript frontend)
- **Database**: PostgreSQL with pgvector extension (Supabase integration)
- **Cache**: Redis for sessions, rate limiting, and agent state management
- **Proxy**: Nginx load balancer with SSL termination
- **Deployment**: Docker Compose for platform services

### User Project Layer (Container-per-User)
- **User Containers**: Individual isolated containers per user project
- **Dynamic Provisioning**: Dock<PERSON> SDK (UserContainerManager) creates containers on-demand
- **Project Types**: React, Python, Node.js, static sites with appropriate base images
- **Resource Isolation**: Per-user CPU, memory, and storage limits

### AI Model Ecosystem

#### Primary Models (Local Ollama)
- **llama3.2:3b** - General chat and explanations (Architect/Issue Fix agents)
- **deepseek-coder:6.7b-instruct** - Complex algorithms and architecture (Architect/Shell agents)
- **starcoder2:3b** - Advanced code generation (Frontend/Backend agents)
- **qwen2.5:3b** - Code analysis and optimization (Issue Fix/Backend agents)
- **yi-coder:1.5b** - Fast code completion (Frontend/Backend agents)
- **mistral:7b-instruct-q4_0** - General reasoning and explanations

#### Embedding Models
- **nomic-embed-text:v1.5** - LTKB knowledge base embeddings
- **mxbai-embed-large** - STPM (Short-Term Project Memory) embeddings

## Specialized AI Agents Configuration

### 1. Architect Agent
**Purpose**: High-level system design, architecture decisions, and project planning

**Model Assignment**:
- Primary: `llama3.2:3b` (fast reasoning for architecture decisions)
- Complex tasks: `deepseek-coder:6.7b-instruct` (deep architectural analysis)

**Configuration**:
```yaml
architect_agent:
  primary_model: "llama3.2:3b"
  fallback_model: "deepseek-coder:6.7b-instruct"
  temperature: 0.3
  max_tokens: 2048
  context_window: 8192
  rate_limit: "10/minute"
  timeout: 30
```

### 2. Frontend Agent
**Purpose**: React/TypeScript development, UI/UX implementation, and frontend optimization

**Model Assignment**:
- Primary: `yi-coder:1.5b` (fast code completion)
- Complex tasks: `starcoder2:3b` (advanced frontend patterns)

**Configuration**:
```yaml
frontend_agent:
  primary_model: "yi-coder:1.5b"
  fallback_model: "starcoder2:3b"
  temperature: 0.2
  max_tokens: 1024
  context_window: 4096
  rate_limit: "20/minute"
  timeout: 15
```

### 3. Backend Agent
**Purpose**: Python/FastAPI development, database operations, and API implementation

**Model Assignment**:
- Primary: `yi-coder:1.5b` (fast code completion)
- Complex tasks: `qwen2.5:3b` (optimization and analysis)

**Configuration**:
```yaml
backend_agent:
  primary_model: "yi-coder:1.5b"
  fallback_model: "qwen2.5:3b"
  temperature: 0.2
  max_tokens: 1024
  context_window: 4096
  rate_limit: "20/minute"
  timeout: 15
```

### 4. Shell Agent
**Purpose**: System operations, deployment, and infrastructure management

**Model Assignment**:
- Primary: `deepseek-coder:6.7b-instruct` (complex system operations)
- Fallback: `llama3.2:3b` (general system tasks)

**Configuration**:
```yaml
shell_agent:
  primary_model: "deepseek-coder:6.7b-instruct"
  fallback_model: "llama3.2:3b"
  temperature: 0.1
  max_tokens: 1024
  context_window: 4096
  rate_limit: "5/minute"
  timeout: 45
```

### 5. Issue Fix Agent
**Purpose**: Debugging, error resolution, and code quality improvement

**Model Assignment**:
- Primary: `qwen2.5:3b` (code analysis and optimization)
- Complex issues: `mistral:7b-instruct-q4_0` (reasoning and explanation)

**Configuration**:
```yaml
issue_fix_agent:
  primary_model: "qwen2.5:3b"
  fallback_model: "mistral:7b-instruct-q4_0"
  temperature: 0.2
  max_tokens: 1536
  context_window: 6144
  rate_limit: "15/minute"
  timeout: 30
```

## Multi-User Configuration (Hybrid Architecture)

### Platform-Level Configuration
```yaml
platform:
  shared_services:
    app_replicas: 2
    max_concurrent_users: 100
    session_timeout: 3600
  rate_limiting:
    global: "1000/hour"
    per_user: "100/hour"
    per_agent: "50/hour"
  security:
    jwt_expiration: 3600
    refresh_token_expiration: 86400
    max_login_attempts: 5
    lockout_duration: 900
```

### User Container Configuration
```yaml
user_containers:
  provisioning:
    on_demand: true
    auto_cleanup: true
    idle_timeout: 3600
  resource_limits:
    memory_per_container: "512MB"
    cpu_per_container: "0.5"
    storage_per_container: "1GB"
    max_containers_per_user: 3
  isolation:
    network_isolation: true
    filesystem_isolation: true
    process_isolation: true
```

## LTKB (Long-Term Knowledge Base) Integration

### Knowledge Management
```yaml
ltkb:
  embedding_model: "nomic-embed-text:v1.5"
  vector_database: "pgvector"
  chunk_size: 1000
  chunk_overlap: 200
  similarity_threshold: 0.7
  max_results: 10
  knowledge_refresh_interval: 3600
```

### STPM (Short-Term Project Memory)
```yaml
stpm:
  embedding_model: "mxbai-embed-large"
  context_window: 4096
  memory_retention: 86400
  auto_summarization: true
  relevance_scoring: true
```

## Agent Orchestration

### Sequential Execution (ENFORCED)
```yaml
orchestration:
  execution_mode: "sequential"  # NEVER parallel
  max_concurrent_tasks: 1
  task_queue_size: 100
  agent_handoff_timeout: 30
  quality_gates: true
  user_approval_required: true
```

### Agent Communication
```yaml
communication:
  message_format: "json"
  encryption: true
  audit_logging: true
  context_sharing: "secure"
  handoff_validation: true
```

## Environment Configuration

### Development Environment
```yaml
development:
  debug_mode: true
  hot_reload: true
  model_caching: false
  verbose_logging: true
  test_mode: true
  mock_external_apis: true
```

### Production Environment
```yaml
production:
  debug_mode: false
  model_caching: true
  performance_monitoring: true
  error_reporting: true
  backup_enabled: true
  ssl_required: true
```

## Health Checks & Monitoring

### Model Health Checks
```yaml
health_checks:
  ollama_endpoint: "http://host.docker.internal:11434/api/health"
  model_availability: true
  response_time_threshold: 5000
  error_rate_threshold: 0.05
  check_interval: 30
```

### Performance Monitoring
```yaml
monitoring:
  metrics_collection: true
  response_times: true
  token_usage: true
  error_rates: true
  user_activity: true
  resource_utilization: true
```

## Required Configuration Patterns

### Always Configure These Settings
- **Multi-user data isolation**: `USER_DATA_ISOLATION: "true"`
- **Rate limiting per user**: Individual user quotas
- **Model-specific endpoints**: Dedicated endpoints per agent
- **Proper health checks**: All services must have health checks
- **Security best practices**: JWT, encryption, audit logging
- **Sequential agent execution**: Never allow parallel agent tasks
- **Resource limits**: CPU, memory, and storage quotas
- **Quality gates**: User approval for all AI-generated outputs

### Model Selection Guidelines
1. **Fast tasks** (code completion, simple queries): Use `yi-coder:1.5b`
2. **Complex reasoning** (architecture, debugging): Use larger models
3. **Code generation**: Prefer `starcoder2:3b` for advanced patterns
4. **System operations**: Use `deepseek-coder:6.7b-instruct` for reliability
5. **Analysis tasks**: Use `qwen2.5:3b` for optimization focus

### Integration Requirements
- **LTKB integration**: All agents must access knowledge base
- **STPM context**: Maintain project-specific memory
- **User approval workflows**: Critical for multi-user safety
- **Audit trails**: Complete logging of all agent interactions
- **Fallback strategies**: Always define backup models
- **Timeout handling**: Prevent hanging operations

Focus on creating configurations that ensure security, performance, and reliability in a multi-user AI coding environment.

## 📚 Related Documentation

- **Primary Architecture Rules**: [../../docs/.copilot-rules.md](../../docs/.copilot-rules.md)
- **Security Guidelines**: [docker-security.prompt.md](docker-security.prompt.md)
- **Multi-User Architecture**: [multiuser-architecture.prompt.md](multiuser-architecture.prompt.md)
- **Container Standards**: [../copilot-instructions.md](../copilot-instructions.md)
- **Documentation Index**: [../README.md](../README.md)
