"""
AI Services Package

This package provides AI integration services including:
- Abstract AI provider interfaces
- Specialized AI providers (Ollama, OpenAI, etc.)
- Agent orchestration and coordination
- Conversation management and context handling
- Model health monitoring and routing
"""

from ai_coding_agent.services.ai.base import AIProvider, ChatRequest, ChatResponse, HealthStatus
from ai_coding_agent.services.ai.orchestrator import AgentOrchestrator
from ai_coding_agent.services.ai.conversation import ConversationManager

__all__ = [
    "AIProvider",
    "ChatRequest",
    "ChatResponse",
    "HealthStatus",
    "AgentOrchestrator",
    "ConversationManager",
]
