"""
Test Phase B2: Dependency Engine & Phase Locking
Comprehensive tests for dependency engine functionality.
"""

import pytest
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from uuid import uuid4

from src.ai_coding_agent.models import (
    Base, Project, Roadmap, Phase, Step, Task,
    TaskStatus, AgentType,
    DependencyType, DependencyCheckStatus, OverrideLevel,
    ProjectCreate, RoadmapCreate, PhaseCreate, StepCreate, TaskCreate
)
from src.ai_coding_agent.services.dependency_engine import DependencyEngine
from src.ai_coding_agent.services.roadmap import RoadmapService


@pytest.fixture
def db_session():
    """Create a test database session."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.fixture
def sample_roadmap(db_session):
    """Create a sample roadmap with dependencies for testing."""
    # Create project
    project = Project(
        id=str(uuid4()),
        name="Test Project",
        description="Test project for dependency engine"
    )
    db_session.add(project)
    db_session.flush()

    # Create roadmap
    roadmap = Roadmap(
        id=str(uuid4()),
        project_id=project.id,
        name="Test Roadmap",
        version="1.0.0"
    )
    db_session.add(roadmap)
    db_session.flush()

    # Create phases
    phase1 = Phase(
        id=str(uuid4()),
        roadmap_id=roadmap.id,
        name="Phase 1",
        description="First phase",
        order_index=1,
        dependencies=[]
    )
    phase2 = Phase(
        id=str(uuid4()),
        roadmap_id=roadmap.id,
        name="Phase 2",
        description="Second phase",
        order_index=2,
        dependencies=[phase1.id]  # Depends on phase 1
    )
    db_session.add_all([phase1, phase2])
    db_session.flush()

    # Create steps
    step1 = Step(
        id=str(uuid4()),
        phase_id=phase1.id,
        name="Step 1.1",
        description="First step",
        order_index=1,
        dependencies=[]
    )
    step2 = Step(
        id=str(uuid4()),
        phase_id=phase1.id,
        name="Step 1.2",
        description="Second step",
        order_index=2,
        dependencies=[step1.id]  # Depends on step 1
    )
    db_session.add_all([step1, step2])
    db_session.flush()

    # Create tasks
    task1 = Task(
        id=str(uuid4()),
        step_id=step1.id,
        name="Task 1.1.1",
        description="First task",
        order_index=1,
        assigned_agent=AgentType.BACKEND,
        dependencies=[]
    )
    task2 = Task(
        id=str(uuid4()),
        step_id=step1.id,
        name="Task 1.1.2",
        description="Second task",
        order_index=2,
        assigned_agent=AgentType.FRONTEND,
        dependencies=[task1.id]  # Depends on task 1
    )
    task3 = Task(
        id=str(uuid4()),
        step_id=step2.id,
        name="Task 1.2.1",
        description="Third task",
        order_index=1,
        assigned_agent=AgentType.BACKEND,
        dependencies=[]
    )
    db_session.add_all([task1, task2, task3])
    db_session.commit()

    return {
        "project": project,
        "roadmap": roadmap,
        "phases": [phase1, phase2],
        "steps": [step1, step2],
        "tasks": [task1, task2, task3]
    }


class TestDependencyEngine:
    """Test cases for the dependency engine."""

    def test_can_start_task_no_dependencies(self, db_session, sample_roadmap):
        """Test that a task with no dependencies can start."""
        engine = DependencyEngine(db_session)
        task1 = sample_roadmap["tasks"][0]  # First task has no dependencies
        
        result = engine.can_start_task(task1.id)
        
        assert result.can_start is True
        assert result.status == DependencyCheckStatus.CAN_START
        assert len(result.blocking_dependencies) == 0

    def test_can_start_task_with_dependencies(self, db_session, sample_roadmap):
        """Test that a task with dependencies is blocked until dependencies are met."""
        engine = DependencyEngine(db_session)
        task2 = sample_roadmap["tasks"][1]  # Second task depends on first
        
        result = engine.can_start_task(task2.id)
        
        assert result.can_start is False
        assert result.status == DependencyCheckStatus.BLOCKED
        assert len(result.blocking_dependencies) == 1
        assert result.blocking_dependencies[0].dependency_type == DependencyType.TASK

    def test_can_start_task_after_dependency_completed(self, db_session, sample_roadmap):
        """Test that a task can start after its dependency is completed."""
        engine = DependencyEngine(db_session)
        task1 = sample_roadmap["tasks"][0]
        task2 = sample_roadmap["tasks"][1]
        
        # Complete the first task
        task1.status = TaskStatus.COMPLETED
        db_session.commit()
        
        result = engine.can_start_task(task2.id)
        
        assert result.can_start is True
        assert result.status == DependencyCheckStatus.CAN_START
        assert len(result.blocking_dependencies) == 0

    def test_phase_progression_check(self, db_session, sample_roadmap):
        """Test phase progression checking."""
        engine = DependencyEngine(db_session)
        phase1 = sample_roadmap["phases"][0]
        
        result = engine.check_phase_progression(phase1.id)
        
        assert result.phase_id == phase1.id
        assert result.can_progress is False  # No tasks completed yet
        assert result.completion_percentage == 0.0
        assert result.total_tasks == 2  # Two tasks in phase 1

    def test_automatic_progression(self, db_session, sample_roadmap):
        """Test automatic progression when tasks are completed."""
        engine = DependencyEngine(db_session)
        task1 = sample_roadmap["tasks"][0]
        task2 = sample_roadmap["tasks"][1]
        
        # Complete both tasks in step 1
        task1.status = TaskStatus.COMPLETED
        task2.status = TaskStatus.COMPLETED
        db_session.commit()
        
        events = engine.process_automatic_progression(task2.id, DependencyType.TASK)
        
        assert len(events) > 0
        # Should trigger step completion
        step_completion_events = [e for e in events if e.source_entity_type == DependencyType.STEP]
        assert len(step_completion_events) > 0

    def test_dependency_validation_request(self, db_session, sample_roadmap):
        """Test dependency validation request processing."""
        engine = DependencyEngine(db_session, user_id="test_user")
        task2 = sample_roadmap["tasks"][1]  # Task with dependencies
        
        from src.ai_coding_agent.models import DependencyValidationRequest
        
        request = DependencyValidationRequest(
            entity_id=task2.id,
            entity_type=DependencyType.TASK,
            operation="start",
            force_override=False,
            user_id="test_user"
        )
        
        response = engine.validate_dependency_request(request)
        
        assert response.allowed is False
        assert response.requires_override is True
        assert len(response.result.blocking_dependencies) > 0

    def test_override_mechanism(self, db_session, sample_roadmap):
        """Test dependency override mechanism."""
        engine = DependencyEngine(db_session, user_id="test_user")
        task2 = sample_roadmap["tasks"][1]  # Task with dependencies
        
        override = engine.apply_override(
            entity_id=task2.id,
            entity_type=DependencyType.TASK,
            reason="Testing override mechanism"
        )
        
        assert override.entity_id == task2.id
        assert override.entity_type == DependencyType.TASK
        assert override.reason == "Testing override mechanism"
        assert override.requested_by == "test_user"

    def test_warning_system(self, db_session, sample_roadmap):
        """Test warning system for out-of-order execution."""
        engine = DependencyEngine(db_session)
        task2 = sample_roadmap["tasks"][1]  # Task with dependencies
        
        warnings = engine.get_out_of_order_warnings(task2.id, DependencyType.TASK)
        
        assert len(warnings) > 0
        assert "out of order" in warnings[0].lower()

    def test_circular_dependency_detection(self, db_session, sample_roadmap):
        """Test circular dependency detection."""
        engine = DependencyEngine(db_session)
        task1 = sample_roadmap["tasks"][0]
        task2 = sample_roadmap["tasks"][1]
        
        # Create a circular dependency (task1 depends on task2, task2 depends on task1)
        task1.dependencies = [task2.id]
        db_session.commit()
        
        warnings = engine.check_circular_dependencies(task1.id, DependencyType.TASK)
        
        # Note: This is a simplified test - full circular dependency detection
        # would require more sophisticated graph traversal
        assert isinstance(warnings, list)

    def test_real_time_status_summary(self, db_session, sample_roadmap):
        """Test real-time status summary generation."""
        engine = DependencyEngine(db_session)
        roadmap = sample_roadmap["roadmap"]
        
        summary = engine.get_real_time_status_summary(roadmap.id)
        
        assert summary["roadmap_id"] == roadmap.id
        assert "phases" in summary
        assert "overall_progress" in summary
        assert len(summary["phases"]) == 2  # Two phases in test roadmap


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
