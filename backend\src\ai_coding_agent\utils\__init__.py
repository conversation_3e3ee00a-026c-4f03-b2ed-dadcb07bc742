"""
Utility modules for AI Coding Agent.

This module contains utility functions and helpers for:
- Structured logging configuration
- Model health tracking and configuration
- JSON serialization utilities

Note: Rate limiting is now handled by the unified system in:
- services/rate_limit_service.py
- middleware/unified_rate_limiting.py
"""

from . import logging
from . import models
from . import serialization

__all__ = [
    "logging",
    "models",
    "serialization",
]
