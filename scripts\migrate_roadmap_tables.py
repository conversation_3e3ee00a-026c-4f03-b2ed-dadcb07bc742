"""
Database migration script for roadmap system (Phase B1).
Creates tables for projects, roadmaps, phases, steps, and tasks.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.src.ai_coding_agent import config
from backend.src.ai_coding_agent import models


def migrate_roadmap_tables():
    """Create roadmap system tables."""
    print("🔄 Creating roadmap system tables...")

    # Create database engine
    engine = create_engine(config.settings.database.url)

    # Create all tables
    models.Base.metadata.create_all(engine)

    print("✅ Roadmap tables created successfully")
    print("📋 Tables created:")
    print("  - projects")
    print("  - roadmaps")
    print("  - phases")
    print("  - steps")
    print("  - tasks")


if __name__ == "__main__":
    migrate_roadmap_tables()
