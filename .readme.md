# Multi-Container Multi-User Project Audit Prompt for GitHub Copilot

## Comprehensive Project Scan Request

```
# Perform a comprehensive audit of this multi-container, multi-user project
# Scan ALL files and analyze the entire project structure for best practices compliance

## MULTI-CONTAINER ARCHITECTURE ANALYSIS
# Check Docker Compose orchestration:
# - Service dependencies and startup order
# - Network isolation and inter-service communication
# - Volume management and data persistence
# - Load balancing and service discovery
# - Container health checks and restart policies
# - Resource allocation and scaling capabilities

## MULTI-USER SECURITY ANALYSIS
# Analyze user isolation and security:
# - User authentication and authorization mechanisms
# - Session management and JWT token handling
# - User data isolation (database, file system, memory)
# - Privilege escalation prevention
# - Cross-user data access protection
# - Concurrent user session handling

## CONTAINER SECURITY BEST PRACTICES
# Review container security:
# - Non-root user implementation across all containers
# - Secrets management and environment variable security
# - Base image security and vulnerability exposure
# - Network security and port exposure
# - File system permissions and read-only configurations
# - Container escape prevention measures

## DATABASE MULTI-TEN<PERSON>CY ANALYSIS
# Examine database architecture:
# - Row-level security (RLS) implementation
# - User schema isolation
# - Connection pooling and user context
# - Database access patterns and query isolation
# - Data encryption at rest and in transit
# - Backup and recovery per user/tenant

## API SECURITY AND ISOLATION
# Review API design:
# - Endpoint authentication and authorization
# - Rate limiting per user and globally
# - Input validation and sanitization
# - API versioning and backward compatibility
# - Cross-origin resource sharing (CORS) configuration
# - API logging and monitoring

## PERFORMANCE AND SCALABILITY
# Analyze scalability patterns:
# - Horizontal scaling capabilities
# - Resource usage optimization
# - Caching strategies (Redis, application-level)
# - Database connection efficiency
# - Static asset delivery
# - Background job processing

## MONITORING AND OBSERVABILITY
# Check monitoring implementation:
# - Application logging and audit trails
# - Performance metrics collection
# - Error tracking and alerting
# - Health check endpoints
# - Distributed tracing capabilities
# - Security event monitoring

## DEVELOPMENT AND DEPLOYMENT
# Review DevOps practices:
# - Development vs production configuration separation
# - Environment variable management
# - CI/CD pipeline security
# - Container image optimization
# - Secret rotation capabilities
# - Backup and disaster recovery

## OUTPUT REQUIREMENTS
# Provide detailed analysis with:
# 1. COMPLIANT AREAS: What's already following best practices
# 2. SECURITY GAPS: Critical security issues found
# 3. ARCHITECTURE ISSUES: Multi-container design problems
# 4. MULTI-USER VIOLATIONS: User isolation failures
# 5. PERFORMANCE CONCERNS: Scalability and efficiency issues
# 6. MISSING COMPONENTS: Required but missing features
# 7. PRIORITY RECOMMENDATIONS: Ranked list of fixes needed
# 8. CODE EXAMPLES: Specific file locations and suggested improvements

## FOCUS AREAS FOR DEEP ANALYSIS
# Pay special attention to:
# - docker-compose.yml and docker-compose.dev.yml files
# - Dockerfile configurations for all services
# - Database models and migration scripts
# - Authentication and authorization middleware
# - API route handlers and middleware
# - Environment configuration files
# - Network and volume configurations
# - Logging and monitoring setup

## COMPLIANCE STANDARDS TO CHECK
# Verify adherence to:
# - OWASP Top 10 security practices
# - Docker security best practices
# - Multi-tenancy security patterns
# - Container orchestration best practices
# - Database security standards
# - API security guidelines
# - Privacy and data protection requirements

# Scan the entire project systematically and provide actionable recommendations
# Include specific file paths and line numbers where issues are found
# Suggest concrete code improvements with examples
```

## Usage Instructions

**Step 1: Project Overview Scan**
```
# First, analyze the overall project structure
# List all Docker containers, their purposes, and interdependencies
# Map out the multi-user architecture and data flow
# Identify the technology stack and frameworks used
```

**Step 2: Security Deep Dive**
```
# Focus specifically on security implementations
# Check for common multi-user vulnerabilities
# Analyze authentication flows and session management
# Review database access patterns and user isolation
```

**Step 3: Performance and Scalability Review**
```
# Examine resource usage and optimization
# Check caching strategies and database efficiency
# Analyze container orchestration for scalability
# Review load balancing and service distribution
```

**Step 4: Generate Remediation Plan**
```
# Create a prioritized list of improvements needed
# Provide specific code examples for fixes
# Estimate effort required for each improvement
# Suggest implementation order based on security impact
```

## Expected Output Format

The audit should produce:

- **Executive Summary**: High-level compliance status
- **Detailed Findings**: Categorized by area (security, architecture, performance)
- **Risk Assessment**: Critical, high, medium, low priority issues
- **Remediation Roadmap**: Step-by-step improvement plan
- **Code Snippets**: Specific examples of needed changes
- **Best Practice Guidelines**: Recommendations for ongoing maintenance

## How to Use This Prompt

1. **Place this prompt** at the top of your main project file (like README.md)
2. **Let Copilot analyze** the entire project structure
3. **Review the generated audit** thoroughly
4. **Create issues/tasks** based on the findings
5. **Implement fixes** using the specific recommendations
6. **Re-run the audit** after improvements to verify compliance
