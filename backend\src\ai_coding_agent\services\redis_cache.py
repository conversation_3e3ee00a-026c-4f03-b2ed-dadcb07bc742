"""
Redis Cache Service for AI Coding Agent.

This module provides Redis-based caching to replace SQLite3 local cache.
Handles session management, embedding cache, and real-time features.
"""

import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union, TYPE_CHECKING
import asyncio

try:
    import redis.asyncio as redis_module  # type: ignore[import-not-found]
    import hiredis  # type: ignore[import-not-found]
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis_module = None  # type: ignore
    hiredis = None

from pydantic import BaseModel, Field
from ..config import settings

logger = logging.getLogger(__name__)


class CacheEntry(BaseModel):
    """Cache entry with metadata."""
    key: str
    value: Any
    expires_at: Optional[datetime] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = Field(default_factory=dict)


class RedisCache:
    """Redis-based cache service for AI Coding Agent."""

    def __init__(self, redis_url: str = "redis://localhost:6379"):
        if not REDIS_AVAILABLE:
            raise RuntimeError("Redis not available. Install with: pip install redis hiredis")

        self.redis_url = redis_url
        self.client: Optional[Any] = None  # redis.Redis when available
        self._connected = False

        # Cache prefixes for different data types
        self.prefixes = {
            "session": "session:",
            "embedding": "embedding:",
            "project": "project:",
            "user": "user:",
            "agent": "agent:",
            "temp": "temp:"
        }

        logger.info(f"RedisCache initialized with URL: {redis_url}")

    async def connect(self):
        """Connect to Redis server."""
        if self._connected:
            return

        try:
            if redis_module is None:
                raise RuntimeError("Redis module not available")

            self.client = redis_module.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )

            # Test connection
            if self.client is not None:
                await self.client.ping()
                self._connected = True
                logger.info("Successfully connected to Redis")

        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise

    async def disconnect(self):
        """Disconnect from Redis server."""
        if self.client and self._connected:
            await self.client.close()
            self._connected = False
            logger.info("Disconnected from Redis")

    async def _ensure_connected(self):
        """Ensure Redis connection is active."""
        if not self._connected or self.client is None:
            await self.connect()

    def _make_key(self, prefix: str, key: str) -> str:
        """Create a prefixed cache key."""
        return f"{self.prefixes.get(prefix, prefix)}{key}"

    async def set(
        self,
        key: str,
        value: Any,
        prefix: str = "temp",
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Set a value in cache with optional TTL."""
        await self._ensure_connected()

        try:
            cache_key = self._make_key(prefix, key)

            # Prepare cache entry
            cache_entry = CacheEntry(
                key=cache_key,
                value=value,
                expires_at=datetime.now(timezone.utc) + timedelta(seconds=ttl) if ttl else None,
                metadata=metadata or {}
            )

            # Serialize the entry
            serialized_value = json.dumps(cache_entry.model_dump(), default=str)

            # Set in Redis
            if self.client is None:
                return False

            if ttl:
                result = await self.client.setex(cache_key, ttl, serialized_value)
            else:
                result = await self.client.set(cache_key, serialized_value)

            return bool(result)

        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False

    async def get(self, key: str, prefix: str = "temp") -> Optional[Any]:
        """Get a value from cache."""
        await self._ensure_connected()

        try:
            if self.client is None:
                return None

            cache_key = self._make_key(prefix, key)
            serialized_value = await self.client.get(cache_key)

            if serialized_value is None:
                return None

            # Deserialize the entry
            cache_entry_dict = json.loads(serialized_value)
            cache_entry = CacheEntry(**cache_entry_dict)

            # Check if expired (additional check beyond Redis TTL)
            if cache_entry.expires_at and datetime.now(timezone.utc) > cache_entry.expires_at:
                await self.delete(key, prefix)
                return None

            return cache_entry.value

        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None

    async def delete(self, key: str, prefix: str = "temp") -> bool:
        """Delete a value from cache."""
        await self._ensure_connected()

        try:
            if self.client is None:
                return False

            cache_key = self._make_key(prefix, key)
            result = await self.client.delete(cache_key)
            return bool(result)

        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False

    async def exists(self, key: str, prefix: str = "temp") -> bool:
        """Check if a key exists in cache."""
        await self._ensure_connected()

        try:
            if self.client is None:
                return False

            cache_key = self._make_key(prefix, key)
            result = await self.client.exists(cache_key)
            return bool(result)

        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False

    async def clear_prefix(self, prefix: str) -> int:
        """Clear all keys with a specific prefix."""
        await self._ensure_connected()

        try:
            if self.client is None:
                return 0

            pattern = f"{self.prefixes.get(prefix, prefix)}*"
            keys = await self.client.keys(pattern)

            if keys:
                result = await self.client.delete(*keys)
                logger.info(f"Cleared {result} keys with prefix {prefix}")
                return result

            return 0

        except Exception as e:
            logger.error(f"Error clearing prefix {prefix}: {e}")
            return 0

    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        await self._ensure_connected()

        try:
            if self.client is None:
                return {}

            info = await self.client.info()

            # Count keys by prefix
            prefix_counts = {}
            for prefix_name, prefix_value in self.prefixes.items():
                pattern = f"{prefix_value}*"
                keys = await self.client.keys(pattern)
                prefix_counts[prefix_name] = len(keys)

            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "total_keys": await self.client.dbsize(),
                "prefix_counts": prefix_counts,
                "redis_version": info.get("redis_version", "unknown"),
                "uptime_seconds": info.get("uptime_in_seconds", 0)
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}

    # Session management methods
    async def set_session(self, session_id: str, session_data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Set session data with TTL."""
        return await self.set(session_id, session_data, "session", ttl)

    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data."""
        return await self.get(session_id, "session")

    async def delete_session(self, session_id: str) -> bool:
        """Delete session data."""
        return await self.delete(session_id, "session")

    # Embedding cache methods
    async def cache_embedding(self, text_hash: str, embedding: List[float], model: str, ttl: int = 86400) -> bool:
        """Cache an embedding with metadata."""
        metadata = {"model": model, "cached_at": datetime.now(timezone.utc).isoformat()}
        return await self.set(text_hash, embedding, "embedding", ttl, metadata)

    async def get_cached_embedding(self, text_hash: str) -> Optional[List[float]]:
        """Get cached embedding."""
        return await self.get(text_hash, "embedding")

    # Project data methods
    async def cache_project_data(self, project_id: str, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Cache project-specific data."""
        return await self.set(project_id, data, "project", ttl)

    async def get_project_data(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get cached project data."""
        return await self.get(project_id, "project")


# Global cache instance
_cache_instance: Optional[RedisCache] = None


async def get_cache() -> RedisCache:
    """Get or create global cache instance."""
    global _cache_instance

    if _cache_instance is None:
        redis_url = settings.redis.url
        _cache_instance = RedisCache(redis_url)
        await _cache_instance.connect()

    return _cache_instance


async def close_cache():
    """Close global cache instance."""
    global _cache_instance

    if _cache_instance:
        await _cache_instance.disconnect()
        _cache_instance = None
