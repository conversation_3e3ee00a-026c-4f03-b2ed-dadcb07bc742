# PowerShell script for local dev environment setup
# Activates venv, mounts volumes, starts services, configures hot reload/debug ports

Write-Host "[Dev Setup] Activating Python venv..."
$venvPath = "backend\venv\Scripts\Activate.ps1"
if (Test-Path $venvPath) {
    . $venvPath
} else {
    python -m venv backend/venv
    . $venvPath
}

Write-Host "[Dev Setup] Installing backend dependencies..."
pip install -r backend/requirements.txt

Write-Host "[Dev Setup] Installing frontend dependencies..."
cd frontend
npm ci
cd ..

Write-Host "[Dev Setup] Starting Docker Compose (dev mode)..."
docker-compose up --build -d

Write-Host "[Dev Setup] Hot reload and debug ports configured."
