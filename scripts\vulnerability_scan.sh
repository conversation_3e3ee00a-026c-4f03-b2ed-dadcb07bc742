#!/bin/bash
# Trivy vulnerability scan automation for all built images
# Follows security and compliance rules

IMAGES=(ai-coding-agent-backend-prod ai-coding-agent-frontend-prod ai-coding-agent-db-prod ai-coding-agent-cache-prod ai-coding-agent-proxy-prod)

for IMAGE in "${IMAGES[@]}"; do
  echo "[Trivy] Scanning $IMAGE..."
  trivy image --exit-code 1 --severity HIGH,CRITICAL $IMAGE
  if [ $? -ne 0 ]; then
    echo "[Trivy] Vulnerabilities found in $IMAGE!"
  else
    echo "[Trivy] $IMAGE passed."
  fi
done
