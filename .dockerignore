__pycache__
*.pyc
*.pyo
*.pyd
*.db
*.sqlite3
*.log
node_modules/
build/
dist/
.env
.env.*
.DS_Store
.vscode/
.git/
.gitignore
coverage/
frontend/node_modules/
frontend/build/
frontend/dist/
backend/__pycache__/
backend/build/
backend/dist/
backend/.pytest_cache/
backend/.mypy_cache/
backend/.coverage
backend/.env
backend/.env.*
backend/.DS_Store
backend/.vscode/
backend/.git/
backend/.gitignore
backend/coverage/
node_modules
.venv
__pycache__
*.log
build
.env
.DS_Store
*.pyc
*.pyo
*.sqlite3
coverage
*.tgz
*.tar.gz
*.zip
secrets
