/**
 * Container Management Dashboard Component
 * 
 * Provides real-time container status monitoring, resource usage visualization,
 * and container lifecycle controls for admin users.
 */

import React, { useState, useEffect } from 'react';
import {
  Container,
  Play,
  Square,
  RotateCcw,
  Trash2,
  Activity,
  Cpu,
  HardDrive,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw
} from 'lucide-react';

interface ContainerInfo {
  id: string;
  name: string;
  userId: string;
  status: 'running' | 'stopped' | 'error' | 'creating' | 'removing';
  projectType: string;
  createdAt: string;
  lastAccessed: string;
  resourceUsage: {
    cpuPercent: number;
    memoryPercent: number;
    memoryUsageMB: number;
    memoryLimitMB: number;
    diskUsageMB: number;
  };
  port?: number;
  previewUrl?: string;
}

interface ContainerStats {
  totalContainers: number;
  runningContainers: number;
  stoppedContainers: number;
  errorContainers: number;
  totalCpuUsage: number;
  totalMemoryUsage: number;
  activeUsers: number;
}

const ContainerManagement: React.FC = () => {
  const [containers, setContainers] = useState<ContainerInfo[]>([]);
  const [stats, setStats] = useState<ContainerStats>({
    totalContainers: 0,
    runningContainers: 0,
    stoppedContainers: 0,
    errorContainers: 0,
    totalCpuUsage: 0,
    totalMemoryUsage: 0,
    activeUsers: 0
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState<string | null>(null);

  // Fetch container data
  const fetchContainers = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/v1/admin/containers/list', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setContainers(data.containers || []);
        
        // Calculate stats
        const totalContainers = data.containers?.length || 0;
        const runningContainers = data.containers?.filter((c: ContainerInfo) => c.status === 'running').length || 0;
        const stoppedContainers = data.containers?.filter((c: ContainerInfo) => c.status === 'stopped').length || 0;
        const errorContainers = data.containers?.filter((c: ContainerInfo) => c.status === 'error').length || 0;
        
        const totalCpuUsage = data.containers?.reduce((sum: number, c: ContainerInfo) => 
          sum + (c.resourceUsage?.cpuPercent || 0), 0) || 0;
        const totalMemoryUsage = data.containers?.reduce((sum: number, c: ContainerInfo) => 
          sum + (c.resourceUsage?.memoryPercent || 0), 0) || 0;
        
        const activeUsers = new Set(data.containers?.map((c: ContainerInfo) => c.userId)).size;
        
        setStats({
          totalContainers,
          runningContainers,
          stoppedContainers,
          errorContainers,
          totalCpuUsage: totalContainers > 0 ? totalCpuUsage / totalContainers : 0,
          totalMemoryUsage: totalContainers > 0 ? totalMemoryUsage / totalContainers : 0,
          activeUsers
        });
      }
    } catch (error) {
      console.error('Failed to fetch containers:', error);
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  // Container lifecycle actions
  const handleContainerAction = async (containerId: string, action: 'start' | 'stop' | 'restart' | 'remove') => {
    try {
      const response = await fetch(`/api/v1/admin/containers/${containerId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        // Refresh container list
        await fetchContainers();
      } else {
        console.error(`Failed to ${action} container:`, await response.text());
      }
    } catch (error) {
      console.error(`Error ${action}ing container:`, error);
    }
  };

  useEffect(() => {
    fetchContainers();
    
    // Auto-refresh every 10 seconds
    const interval = setInterval(fetchContainers, 10000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'stopped':
        return <Square className="w-4 h-4 text-gray-500" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'creating':
      case 'removing':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Container className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'stopped':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'creating':
      case 'removing':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading containers...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Container Management
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage user containers
          </p>
        </div>
        <button
          onClick={fetchContainers}
          disabled={refreshing}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md shadow hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <Container className="w-8 h-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Containers</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalContainers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <Activity className="w-8 h-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Running</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.runningContainers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <Cpu className="w-8 h-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg CPU Usage</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalCpuUsage.toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.activeUsers}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Container List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Active Containers ({containers.length})
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Container
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Resources
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Last Accessed
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {containers.map((container) => (
                <tr key={container.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(container.status)}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {container.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {container.projectType}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {container.userId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(container.status)}`}>
                      {container.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <div className="space-y-1">
                      <div className="flex items-center">
                        <Cpu className="w-3 h-3 mr-1" />
                        <span>{container.resourceUsage?.cpuPercent?.toFixed(1) || 0}%</span>
                      </div>
                      <div className="flex items-center">
                        <HardDrive className="w-3 h-3 mr-1" />
                        <span>{formatBytes((container.resourceUsage?.memoryUsageMB || 0) * 1024 * 1024)}</span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(container.lastAccessed)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      {container.status === 'stopped' && (
                        <button
                          onClick={() => handleContainerAction(container.id, 'start')}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          title="Start Container"
                        >
                          <Play className="w-4 h-4" />
                        </button>
                      )}
                      {container.status === 'running' && (
                        <button
                          onClick={() => handleContainerAction(container.id, 'stop')}
                          className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                          title="Stop Container"
                        >
                          <Square className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleContainerAction(container.id, 'restart')}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        title="Restart Container"
                      >
                        <RotateCcw className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleContainerAction(container.id, 'remove')}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        title="Remove Container"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {containers.length === 0 && (
            <div className="text-center py-12">
              <Container className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No containers found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                No user containers are currently active.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContainerManagement;
