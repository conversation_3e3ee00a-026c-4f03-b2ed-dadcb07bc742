/**
 * Security utilities for client-side protection
 */

import DOMPurify from 'dompurify';

// Content Security Policy helpers
export const generateCSPNonce = (): string => {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => ('0' + byte.toString(16)).slice(-2)).join('');
};

// HTTPS enforcement
export const enforceHTTPS = (): void => {
  if (process.env.NODE_ENV === 'production' && window.location.protocol !== 'https:') {
    window.location.replace(`https:${window.location.href.substring(window.location.protocol.length)}`);
  }
};

// Security initialization flag to prevent multiple executions
let securityInitialized = false;

// Secure headers check
export const checkSecurityHeaders = (): void => {
  if (process.env.NODE_ENV === 'development' && !securityInitialized) {
    console.info('🔒 Security Check: Ensure these headers are set in production:');
    console.info('  ✓ Content-Security-Policy');
    console.info('  ✓ X-Frame-Options: DENY');
    console.info('  ✓ X-Content-Type-Options: nosniff');
    console.info('  ✓ Referrer-Policy: strict-origin-when-cross-origin');
    console.info('  ✓ Permissions-Policy');
  }
};

// Clickjacking protection
export const preventClickjacking = (): void => {
  if (window.top !== window.self) {
    // Page is in a frame, could be clickjacking attempt
    document.body.style.display = 'none';
    throw new Error('Clickjacking protection: Page cannot be displayed in a frame');
  }
};

// Local storage security
export const secureStorage = {
  setItem: (key: string, value: string): void => {
    try {
      // Encrypt sensitive data before storing (in production, use proper encryption)
      const encrypted = btoa(value); // Basic encoding, use proper encryption in production
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to store item securely:', error);
    }
  },

  getItem: (key: string): string | null => {
    try {
      const item = localStorage.getItem(key);
      if (!item) return null;

      // Decrypt data (in production, use proper decryption)
      return atob(item); // Basic decoding, use proper decryption in production
    } catch (error) {
      console.error('Failed to retrieve item securely:', error);
      return null;
    }
  },

  removeItem: (key: string): void => {
    localStorage.removeItem(key);
  },

  clear: (): void => {
    localStorage.clear();
  }
};

// CSRF token management
export const csrfToken = {
  generate: (): string => {
    const token = generateCSPNonce();
    sessionStorage.setItem('csrf_token', token);
    return token;
  },

  get: (): string | null => {
    return sessionStorage.getItem('csrf_token');
  },

  validate: (token: string): boolean => {
    const storedToken = sessionStorage.getItem('csrf_token');
    return storedToken === token;
  }
};

// Input sanitization for DOM manipulation
export const sanitizeForDOM = (input: string): string => {
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
};

// URL validation for redirects
export const validateRedirectUrl = (url: string, allowedDomains: string[] = []): boolean => {
  try {
    const urlObj = new URL(url, window.location.origin);

    // Only allow same origin by default
    if (allowedDomains.length === 0) {
      return urlObj.origin === window.location.origin;
    }

    // Check against allowed domains
    return allowedDomains.some(domain => urlObj.hostname.endsWith(domain));
  } catch {
    return false;
  }
};

// Content type validation for file uploads
export const validateContentType = (file: File): boolean => {
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'text/plain',
    'application/pdf',
    'application/json'
  ];

  return allowedTypes.includes(file.type);
};

// Initialize security measures
export const initializeSecurity = (): void => {
  // Prevent multiple initializations
  if (securityInitialized) {
    return;
  }

  // Enforce HTTPS in production
  enforceHTTPS();

  // Prevent clickjacking
  preventClickjacking();

  // Check security headers (development only)
  checkSecurityHeaders();

  // Generate initial CSRF token
  if (!csrfToken.get()) {
    csrfToken.generate();
  }

  // Mark as initialized
  securityInitialized = true;

  // Disable right-click in production (optional)
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('contextmenu', (e) => {
      // Uncomment to disable right-click
      // e.preventDefault();
    });
  }

  // Disable F12 and other developer shortcuts in production (optional)
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('keydown', (e) => {
      // Uncomment to disable developer tools shortcuts
      // if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
      //   e.preventDefault();
      // }
    });
  }
};

/**
 * Security utilities for the AI Coding Agent IDE
 * Ensures all file operations and user inputs are properly sanitized
 */

// File path security validation
export const validateFilePath = (path: string): boolean => {
  // Prevent directory traversal attacks
  const dangerousPatterns = [
    '../',
    '..\\',
    '//',
    '\\\\',
    '<',
    '>',
    '|',
    ':',
    '*',
    '?',
    '"'
  ];

  return !dangerousPatterns.some(pattern => path.includes(pattern)) &&
         path.length <= 260 && // Windows path limit
         path.length > 0 &&
         !/^\s|\s$/.test(path); // No leading/trailing whitespace
};

// Sanitize file content for safe display
export const sanitizeContent = (content: string): string => {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['pre', 'code', 'span'],
    ALLOWED_ATTR: ['class']
  });
};

// Validate file size
export const validateFileSize = (size: number): boolean => {
  const maxSize = parseInt(process.env.REACT_APP_MAX_FILE_SIZE || '10485760'); // 10MB default
  return size <= maxSize && size >= 0;
};

// Validate file name
export const validateFileName = (name: string): boolean => {
  const invalidChars = /[<>:"/\\|?*]/;
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];

  return !invalidChars.test(name) &&
         !reservedNames.includes(name.toUpperCase()) &&
         name.length <= 255 &&
         name.length > 0 &&
         !name.startsWith('.') &&
         !name.endsWith(' ') &&
         !name.endsWith('.');
};

// Sanitize agent messages
export const sanitizeAgentMessage = (message: string): string => {
  return DOMPurify.sanitize(message, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'code', 'pre'],
    ALLOWED_ATTR: []
  });
};

// Validate WebSocket message structure
export const validateWebSocketMessage = (message: any): boolean => {
  return typeof message === 'object' &&
         message !== null &&
         typeof message.type === 'string' &&
         message.type.length > 0 &&
         message.type.length <= 100;
};

// Rate limiting for API calls
class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, []);
    }

    const userRequests = this.requests.get(identifier)!;

    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => time > windowStart);

    if (validRequests.length >= maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(identifier, validRequests);

    return true;
  }
}

export const rateLimiter = new RateLimiter();

// CSRF token management
export const getCsrfToken = (): string | null => {
  const meta = document.querySelector('meta[name="csrf-token"]');
  return meta ? meta.getAttribute('content') : null;
};

// Input validation for search queries
export const validateSearchQuery = (query: string): boolean => {
  return query.length <= 500 &&
         query.length > 0 &&
         !/[<>'"&]/g.test(query); // Prevent XSS
};

// Secure URL validation
export const validateUrl = (url: string): boolean => {
  try {
    const parsed = new URL(url);
    return ['http:', 'https:'].includes(parsed.protocol) &&
           (parsed.hostname !== 'localhost' || process.env.NODE_ENV === 'development');
  } catch {
    return false;
  }
};
