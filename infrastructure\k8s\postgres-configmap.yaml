apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-coding-agent-postgres-config
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-postgres-k8s
    project: ai-coding-agent
data:
  postgresql.conf: |
    max_connections = 100
    shared_buffers = 128MB
    log_statement = 'all'
    log_min_duration_statement = 0
    wal_level = replica
    archive_mode = on
    archive_command = 'cp %p /var/lib/postgresql/archive/%f'
    hot_standby = on
