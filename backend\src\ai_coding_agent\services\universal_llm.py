"""
Universal LLM Service with Rule Enforcement.

This service provides a unified interface for ALL LLMs (local Ollama and cloud models)
with mandatory rule enforcement, ensuring consistent behavior across all models.

CRITICAL: ALL model interactions MUST go through this service to ensure:
- System rules are enforced
- Agent constraints are respected
- Security policies are applied
- Orchestration workflow is followed
- Output is validated and compliant
"""

import json
import httpx
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path

from pydantic import BaseModel, Field
from ai_coding_agent.config import settings
from ai_coding_agent.services.rule_enforcement import rule_engine, RuleViolation
from ai_coding_agent.services.secure_config import get_secure_config
from ai_coding_agent.services.cloud_ai_providers import CloudAIProvider, CloudModelResponse

# Configure universal LLM logger
llm_logger = logging.getLogger("universal_llm")
llm_logger.setLevel(logging.INFO)


class LLMRequest(BaseModel):
    """Standardized LLM request."""
    prompt: str
    agent_name: str
    task_type: str
    model_name: Optional[str] = None
    provider: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 4096
    user_id: Optional[str] = None
    session_id: Optional[str] = None


class LLMResponse(BaseModel):
    """Standardized LLM response with rule compliance."""
    content: str
    model: str
    provider: str
    agent_name: str
    task_type: str
    rule_compliant: bool
    violations: List[RuleViolation] = Field(default_factory=list)
    usage: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class UniversalLLMService:
    """
    Universal LLM service that enforces rules across all models.

    This service:
    1. Validates all requests against system rules
    2. Injects system rules into prompts
    3. Routes to appropriate LLM (local or cloud)
    4. Validates all responses against rules
    5. Logs all interactions for audit
    6. Blocks non-compliant outputs
    """

    def __init__(self):
        """Initialize the universal LLM service."""
        self.secure_config = get_secure_config()
        self.ollama_client = httpx.AsyncClient()
        self.cloud_providers: Dict[str, CloudAIProvider] = {}
        self.models_config = self._load_models_config()

    def _load_models_config(self) -> Dict[str, Any]:
        """Load models configuration."""
        try:
            config_path = Path(__file__).parent.parent / "models_config.json"
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            llm_logger.error(f"Failed to load models config: {e}")
            return {}

    async def generate(self, request: LLMRequest) -> LLMResponse:
        """
        Generate response from LLM with mandatory rule enforcement.

        Args:
            request: Standardized LLM request

        Returns:
            LLMResponse: Rule-compliant response

        Raises:
            ValueError: If request violates system rules
            RuntimeError: If LLM interaction fails
        """
        start_time = datetime.utcnow()

        # Step 1: Validate request against system rules
        is_valid, violations = rule_engine.validate_prompt(
            request.prompt, request.agent_name, request.task_type
        )

        if not is_valid:
            llm_logger.error(f"Request validation failed: {[v.description for v in violations]}")
            return LLMResponse(
                content="Request blocked due to rule violations",
                model="none",
                provider="none",
                agent_name=request.agent_name,
                task_type=request.task_type,
                rule_compliant=False,
                violations=violations,
                metadata={"blocked_at": "request_validation"}
            )

        # Step 2: Inject system rules into prompt
        enhanced_prompt = rule_engine.inject_system_rules(
            request.prompt, request.agent_name, request.task_type
        )

        # Step 3: Determine target model and provider
        model_name, provider = self._determine_model_and_provider(request)

        # Step 4: Route to appropriate LLM
        try:
            if provider == "ollama":
                raw_response = await self._call_ollama(enhanced_prompt, model_name, request)
            else:
                raw_response = await self._call_cloud_provider(enhanced_prompt, model_name, provider, request)
        except Exception as e:
            llm_logger.error(f"LLM call failed: {e}")
            return LLMResponse(
                content=f"LLM generation failed: {str(e)}",
                model=model_name,
                provider=provider,
                agent_name=request.agent_name,
                task_type=request.task_type,
                rule_compliant=False,
                violations=[],
                metadata={"error": str(e), "failed_at": "llm_generation"}
            )

        # Step 5: Validate response against system rules
        is_compliant, output_violations = rule_engine.validate_output(
            raw_response, request.agent_name, request.task_type
        )

        # Step 6: Build standardized response
        response = LLMResponse(
            content=raw_response if is_compliant else "Output blocked due to rule violations",
            model=model_name,
            provider=provider,
            agent_name=request.agent_name,
            task_type=request.task_type,
            rule_compliant=is_compliant,
            violations=output_violations,
            metadata={
                "processing_time": (datetime.utcnow() - start_time).total_seconds(),
                "prompt_length": len(enhanced_prompt),
                "response_length": len(raw_response),
                "rules_injected": True
            }
        )

        # Step 7: Log interaction for audit
        self._log_interaction(request, response)

        return response

    def _determine_model_and_provider(self, request: LLMRequest) -> tuple[str, str]:
        """Determine which model and provider to use."""
        # If specific model requested, use it
        if request.model_name and request.provider:
            return request.model_name, request.provider

        # Use routing configuration
        routing = self.models_config.get("routing", {})
        agent_routing = routing.get(request.agent_name, {})

        # Get primary model for agent
        model_name = agent_routing.get("primary", settings.ai.default_model)

        # Determine provider based on model
        if model_name in self._get_local_models():
            return model_name, "ollama"
        else:
            # Check cloud providers for this model
            for provider_name, provider_config in self.models_config.get("providers", {}).items():
                if provider_name != "ollama" and model_name in provider_config.get("models", {}):
                    return model_name, provider_name

        # Fallback to local model
        return settings.ai.default_model, "ollama"

    def _get_local_models(self) -> List[str]:
        """Get list of available local Ollama models."""
        ollama_config = self.models_config.get("providers", {}).get("ollama", {})
        return list(ollama_config.get("models", {}).keys())

    async def _call_ollama(self, prompt: str, model: str, request: LLMRequest) -> str:
        """Call local Ollama model."""
        ollama_url = settings.ai.ollama_host

        response = await self.ollama_client.post(
            f"{ollama_url}/api/generate",
            json={
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens
                }
            }
        )

        if response.status_code != 200:
            raise RuntimeError(f"Ollama request failed: {response.status_code} - {response.text}")

        result = response.json()
        return result.get("response", "")

    async def _call_cloud_provider(self, prompt: str, model: str, provider: str, request: LLMRequest) -> str:
        """Call cloud provider model."""
        # Get encrypted API key
        api_key = self.secure_config.get_provider_api_key(provider)
        if not api_key:
            raise RuntimeError(f"No API key configured for provider: {provider}")

        # Initialize cloud provider if not already done
        if provider not in self.cloud_providers:
            # This would need to be implemented based on your cloud_ai_providers.py
            # For now, return a placeholder
            raise RuntimeError(f"Cloud provider {provider} not yet implemented in universal service")

        # Call cloud provider
        cloud_provider = self.cloud_providers[provider]
        cloud_response = await cloud_provider.generate(prompt, model,
                                                      temperature=request.temperature,
                                                      max_tokens=request.max_tokens)

        return cloud_response.content

    def _log_interaction(self, request: LLMRequest, response: LLMResponse):
        """Log LLM interaction for audit and monitoring."""
        log_entry = {
            "timestamp": response.timestamp.isoformat(),
            "agent_name": request.agent_name,
            "task_type": request.task_type,
            "model": response.model,
            "provider": response.provider,
            "rule_compliant": response.rule_compliant,
            "violations_count": len(response.violations),
            "user_id": request.user_id,
            "session_id": request.session_id,
            "processing_time": response.metadata.get("processing_time", 0),
            "prompt_length": response.metadata.get("prompt_length", 0),
            "response_length": response.metadata.get("response_length", 0)
        }

        # Log violations separately for security monitoring
        if response.violations:
            for violation in response.violations:
                llm_logger.warning(f"Rule violation: {violation.description} (severity: {violation.severity})")

        llm_logger.info(f"LLM interaction: {json.dumps(log_entry)}")

    async def get_available_models(self) -> Dict[str, List[str]]:
        """Get available models by provider."""
        available = {"ollama": [], "cloud": {}}

        # Get local models
        try:
            response = await self.ollama_client.get(f"{settings.ai.ollama_host}/api/tags")
            if response.status_code == 200:
                models_data = response.json()
                available["ollama"] = [model["name"] for model in models_data.get("models", [])]
        except Exception as e:
            llm_logger.warning(f"Could not fetch Ollama models: {e}")

        # Get cloud models from config
        for provider_name, provider_config in self.models_config.get("providers", {}).items():
            if provider_name != "ollama":
                available["cloud"][provider_name] = list(provider_config.get("models", {}).keys())

        return available

    async def close(self):
        """Close HTTP clients."""
        await self.ollama_client.aclose()
        for provider in self.cloud_providers.values():
            await provider.close()


# Global instance for easy access (lazy initialization)
_universal_llm_instance = None

def get_universal_llm() -> UniversalLLMService:
    """Get the global universal LLM instance (lazy initialization)."""
    global _universal_llm_instance
    if _universal_llm_instance is None:
        _universal_llm_instance = UniversalLLMService()
    return _universal_llm_instance
