apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-coding-agent-backend-hpa
  namespace: ai-coding-agent
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-coding-agent-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Pods
      pods:
        metric:
          name: request_rate
        target:
          type: AverageValue
          averageValue: "10"
    - type: Pods
      pods:
        metric:
          name: response_time
        target:
          type: AverageValue
          averageValue: "2"
