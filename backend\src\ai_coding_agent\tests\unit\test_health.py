"""
Test the health check endpoints.

This module tests the health monitoring functionality to ensure
the application provides proper health status information.
"""

import pytest
from fastapi.testclient import TestClient


def test_health_check(client: TestClient):
    """Test the main health check endpoint."""
    response = client.get("/api/v1/health")

    assert response.status_code == 200
    data = response.json()

    # Check required fields
    assert "status" in data
    assert "timestamp" in data
    assert "version" in data
    assert "environment" in data
    assert "uptime" in data
    assert "services" in data

    # Check values
    assert data["status"] == "healthy"
    assert data["version"] == "0.1.0"
    assert isinstance(data["uptime"], (int, float))
    assert data["uptime"] >= 0


def test_readiness_check(client: TestClient):
    """Test the readiness probe endpoint."""
    response = client.get("/api/v1/health/ready")

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ready"


def test_liveness_check(client: TestClient):
    """Test the liveness probe endpoint."""
    response = client.get("/api/v1/health/live")

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "alive"


def test_health_check_response_model(client: TestClient):
    """Test that health check response follows the expected model."""
    response = client.get("/api/v1/health")
    data = response.json()

    # Validate data types
    assert isinstance(data["status"], str)
    assert isinstance(data["timestamp"], str)
    assert isinstance(data["version"], str)
    assert isinstance(data["environment"], str)
    assert isinstance(data["uptime"], (int, float))
    assert isinstance(data["services"], dict)

    # Validate services structure
    services = data["services"]
    assert "database" in services
    assert "ai_service" in services
