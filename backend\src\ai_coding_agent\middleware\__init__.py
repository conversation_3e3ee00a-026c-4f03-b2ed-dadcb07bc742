"""
Middleware module for AI Coding Agent.

This module provides middleware components for request processing,
authentication, rate limiting, and input validation.
"""

from ai_coding_agent.middleware.admin_auth import get_current_admin_user
from ai_coding_agent.middleware.input_validation import (
    InputValidationMiddleware,
    EnhancedChatRequest,
    EnhancedUserInput,
    EnhancedFileInput,
    ValidationError
)
from ai_coding_agent.middleware.unified_rate_limiting import UnifiedRateLimitMiddleware

__all__ = [
    "get_current_admin_user",
    "InputValidationMiddleware",
    "EnhancedChatRequest",
    "EnhancedUserInput",
    "EnhancedFileInput",
    "ValidationError",
    "UnifiedRateLimitMiddleware"
]
