"""
Admin authentication middleware for protecting admin endpoints.

This module provides authentication and authorization middleware
specifically for admin routes, ensuring only superusers can access
administrative functions.
"""

from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
import logging

from ..models import get_db, User
from ..services.supabase_auth import get_supabase_auth_service
from ..services.audit_service import log_security_event

# Configure security logger
security_logger = logging.getLogger("security.admin_auth")
security_logger.setLevel(logging.INFO)


async def get_current_admin_user(
    request: Request,
    db: Session = Depends(get_db)
) -> User:
    """
    Enhanced dependency to ensure current user is an admin (superuser).

    This function provides comprehensive security checks including:
    - User authentication validation via Supabase JWT
    - Account status verification
    - Admin privilege verification
    - Security event logging
    - Session validation

    Args:
        request: FastAPI request object for IP and user agent logging
        db: Database session

    Returns:
        User: The current user if they are an admin

    Raises:
        HTTPException: If user is not authenticated or not an admin
    """
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    try:
        # Get authorization header
        authorization = request.headers.get("authorization")
        if not authorization or not authorization.startswith("Bearer "):
            security_logger.warning(
                f"Admin access attempt without valid authorization header from IP: {client_ip}"
            )
            # Log security event for unauthorized access attempt
            log_security_event(
                user_id="anonymous",
                action="unauthorized_admin_access_attempt",
                resource=str(request.url),
                ip_address=client_ip,
                user_agent=user_agent
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract JWT token
        token = authorization.split(" ")[1]

        # Verify token with Supabase and get user
        auth_service = get_supabase_auth_service()
        try:
            supabase_user = await auth_service.get_user_by_token(token)
            if not supabase_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token"
                )
        except Exception as e:
            security_logger.warning(
                f"Token verification failed from IP: {client_ip}, error: {str(e)}"
            )
            # Log failed token verification
            log_security_event(
                user_id="anonymous",
                action="token_verification_failed",
                resource=str(request.url),
                ip_address=client_ip,
                user_agent=user_agent
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )

        # Get local user from database
        current_user = db.query(User).filter(User.supabase_id == supabase_user.id).first()
        if not current_user:
            # Create local user if doesn't exist (first-time login)
            user_metadata = supabase_user.user_metadata or {}
            current_user = User(
                supabase_id=supabase_user.id,
                email=supabase_user.email,
                username=user_metadata.get("username", supabase_user.email.split("@")[0]),
                full_name=user_metadata.get("full_name", ""),
                is_active=True,
                is_superuser=False  # Default to non-admin
            )
            db.add(current_user)
            db.commit()
            db.refresh(current_user)

    except HTTPException:
        raise
    except Exception as e:
        security_logger.error(
            f"Unexpected error during admin authentication from IP: {client_ip}, error: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )

    # Check if account is active
    if not getattr(current_user, 'is_active', False):
        security_logger.warning(
            f"Admin access attempt by inactive user {current_user.username} from IP: {client_ip}"
        )
        log_security_event(
            user_id=str(current_user.id),
            action="inactive_admin_access_attempt",
            resource=str(request.url),
            ip_address=client_ip,
            user_agent=user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user account"
        )

    # Check admin privileges
    if not getattr(current_user, 'is_superuser', False):
        security_logger.warning(
            f"Admin access attempt by non-admin user {current_user.username} from IP: {client_ip}"
        )
        log_security_event(
            user_id=str(current_user.id),
            action="unauthorized_admin_access_attempt",
            resource=str(request.url),
            ip_address=client_ip,
            user_agent=user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required. Contact your administrator."
        )

    # Log successful admin access (for audit trail)
    security_logger.info(
        f"Admin access granted to {current_user.username} from IP: {client_ip}"
    )
    # Note: For full admin audit logging, we would need database access here
    # For now, using security event logging
    log_security_event(
        user_id=str(current_user.id),
        action="admin_login_success",
        resource=str(request.url),
        ip_address=client_ip,
        user_agent=user_agent
    )

    return current_user


async def get_current_admin_user_optional(
    # current_user: Optional[User] = Depends(get_current_user),  # Disabled until Supabase auth integration
) -> Optional[User]:
    """
    Optional admin dependency that returns None if user is not admin.

    Useful for endpoints that have different behavior for admin vs regular users.

    Args:
        current_user: The currently authenticated user (optional)

    Returns:
        User: The current user if they are an admin, None otherwise
    """
    # TODO: Implement Supabase-based optional admin authentication
    # For now, return None until proper integration is complete
    return None

    # Original code commented out until Supabase auth is integrated:
    # if not current_user:
    #     return None
    # if not getattr(current_user, 'is_active', False):
    #     return None
    # if not getattr(current_user, 'is_superuser', False):
    #     return None
    # return current_user


def require_admin_permission(permission: str = "admin"):
    """
    Decorator factory for requiring specific admin permissions.

    Args:
        permission: The required permission level

    Returns:
        Dependency function that checks the permission
    """
    async def check_permission(
        current_admin: User = Depends(get_current_admin_user)
    ) -> User:
        # For now, just check if user is superuser
        # In the future, this could check specific permissions
        if not getattr(current_admin, 'is_superuser', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_admin

    return check_permission
