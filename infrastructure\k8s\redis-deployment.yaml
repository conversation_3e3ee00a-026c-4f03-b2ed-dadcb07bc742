apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-coding-agent-redis-k
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-redis-k
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-coding-agent-redis-k
  template:
    metadata:
      labels:
        app: ai-coding-agent-redis-k
    spec:
      containers:
        - name: redis
          image: redis:7-alpine
          command: ["redis-server", "/etc/redis/redis.conf"]
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: REDIS_PASSWORD
          ports:
            - containerPort: 6379
          volumeMounts:
            - name: redis-data
              mountPath: /data
            - name: redis-config
              mountPath: /etc/redis
              subPath: redis.conf
      volumes:
        - name: redis-config
          configMap:
            name: ai-coding-agent-redis-config
        - name: redis-data
          persistentVolumeClaim:
            claimName: redis-data-pvc
