import json
import os
from datetime import datetime

# Audit trail and compliance reporting script
# Follows project security, privacy, and compliance rules

def collect_audit_data():
    audit = {
        'timestamp': datetime.utcnow().isoformat(),
        'containers': [],
        'volumes': [],
        'images': [],
        'env_vars': {},
        'secrets': [],
    }
    # List containers
    try:
        import docker
        client = docker.from_env()
        for c in client.containers.list(all=True):
            audit['containers'].append({
                'name': c.name,
                'status': c.status,
                'created': c.attrs['Created'],
                'image': c.image.tags
            })
        # List volumes
        for v in client.volumes.list():
            audit['volumes'].append(v.name)
        # List images
        for i in client.images.list():
            audit['images'].extend(i.tags)
    except Exception as e:
        audit['error'] = str(e)
    # Collect environment variables
    for k, v in os.environ.items():
        if k.startswith('AI_CODING_AGENT_') or k.startswith('POSTGRES_') or k.startswith('REDIS_'):
            audit['env_vars'][k] = v
    # List secrets (files in secrets/)
    secrets_dir = 'secrets'
    if os.path.isdir(secrets_dir):
        audit['secrets'] = os.listdir(secrets_dir)
    return audit

def main():
    audit = collect_audit_data()
    with open('scripts/audit_report_output.json', 'w') as f:
        json.dump(audit, f, indent=2)
    print("[Audit] Report written to scripts/audit_report_output.json")

if __name__ == "__main__":
    main()
