apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: ai-coding-agent-postgres-k8s
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-postgres-k8s
spec:
  serviceName: ai-coding-agent-postgres-k8s
  replicas: 1
  selector:
    matchLabels:
      app: ai-coding-agent-postgres-k8s
  template:
    metadata:
      labels:
        app: ai-coding-agent-postgres-k8s
    spec:
      containers:
        - name: postgres
          image: postgres:15-alpine
          env:
            - name: POSTGRES_DB
              value: ai_coding_agent
            - name: POSTGRES_USER
              value: ai_agent
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: POSTGRES_PASSWORD
          ports:
            - containerPort: 5432
          volumeMounts:
            - name: postgres-data
              mountPath: /var/lib/postgresql/data
            - name: postgres-config
              mountPath: /etc/postgresql
              subPath: postgresql.conf
      volumes:
        - name: postgres-config
          configMap:
            name: ai-coding-agent-postgres-config
  volumeClaimTemplates:
    - metadata:
        name: postgres-data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 10Gi
