"""
Test script to verify Ollama connectivity and AI integration.
"""

import asyncio
import aiohttp
import sys
import os

# Import path is handled by pyproject.toml configuration

async def test_ollama_connectivity():
    """Test if Ollama is running and accessible."""
    print("🔍 Testing Ollama connectivity...")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:11434/api/tags') as response:
                if response.status == 200:
                    data = await response.json()
                    model_count = len(data.get('models', []))
                    print(f'✅ Ollama is running with {model_count} models')

                    for model in data.get('models', [])[:5]:
                        model_name = model.get('name', 'unknown')
                        model_size = model.get('size', 'unknown size')
                        print(f'  - {model_name} ({model_size})')

                    return True, data.get('models', [])
                else:
                    print(f'❌ Ollama returned status {response.status}')
                    return False, []
    except Exception as e:
        print(f'❌ Ollama connection failed: {e}')
        return False, []

async def test_ai_services():
    """Test our AI services integration."""
    print("\n🧪 Testing AI services integration...")

    try:
        from ai_coding_agent.services.ai.providers.ollama import OllamaProvider
        from ai_coding_agent.services.ai.base import ChatRequest, ChatMessage
        from ai_coding_agent.agents import AgentRole

        # Create Ollama provider
        provider = OllamaProvider()

        # Test health check
        print("🔍 Testing provider health check...")
        health = await provider.health_check()
        print(f"  Provider status: {health.status.value}")

        if health.models:
            print("  Model health:")
            for model in health.models[:3]:  # Show first 3 models
                print(f"    - {model.model}: {model.status.value} ({model.latency_ms}ms)")

        # Test simple chat
        print("\n🔍 Testing simple chat...")
        messages = [ChatMessage(role="user", content="Hello! Can you help me write a simple Python function?")]
        request = ChatRequest(
            messages=messages,
            agent_role=AgentRole.BACKEND
        )

        response = await provider.chat(request)
        print(f"✅ Chat response received:")
        print(f"  Model: {response.model}")
        print(f"  Agent: {response.agent_role}")
        print(f"  Response length: {len(response.content)} characters")
        print(f"  First 100 chars: {response.content[:100]}...")

        # Close provider
        await provider.close()

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ AI services test failed: {e}")
        return False

async def test_agent_orchestrator():
    """Test the agent orchestrator."""
    print("\n🎭 Testing Agent Orchestrator...")

    try:
        from ai_coding_agent.services.ai.orchestrator import AgentOrchestrator, TaskRequest
        from ai_coding_agent.agents import AgentRole

        orchestrator = AgentOrchestrator()

        # Test simple task
        task_request = TaskRequest(
            task_description="Create a simple hello world function in Python",
            preferred_agent=AgentRole.BACKEND
        )

        result = await orchestrator.execute_task(task_request)

        print(f"✅ Task execution completed:")
        print(f"  Success: {result.success}")
        print(f"  Agent: {result.agent_role}")
        print(f"  Execution time: {result.execution_time_ms}ms")

        if result.success:
            print(f"  Result length: {len(result.result or '')} characters")
            if result.result:
                print(f"  First 100 chars: {result.result[:100]}...")
        else:
            print(f"  Error: {result.error}")

        # Close orchestrator
        await orchestrator.close()

        return result.success

    except Exception as e:
        print(f"❌ Orchestrator test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting AI Integration Test Suite\n")

    # Test Ollama connectivity
    ollama_ok, models = await test_ollama_connectivity()

    if not ollama_ok:
        print("\n❌ Ollama is not running or not accessible. Please start Ollama first.")
        return False

    # Check if required models are available
    required_models = [
        'yi-coder:1.5b',
        'mistral:7b-instruct-q4_0',
        'qwen2.5:3b',
        'starcoder2:3b',
        'deepseek-coder:6.7b-instruct-q4_0'
    ]

    available_models = [model.get('name', '') for model in models]
    missing_models = [model for model in required_models if model not in available_models]

    if missing_models:
        print(f"\n⚠️  Missing required models: {missing_models}")
        print("You may need to download them with:")
        for model in missing_models:
            print(f"  ollama pull {model}")
    else:
        print("\n✅ All required models are available")

    # Test AI services
    services_ok = await test_ai_services()

    if not services_ok:
        print("\n❌ AI services test failed")
        return False

    # Test orchestrator
    orchestrator_ok = await test_agent_orchestrator()

    if not orchestrator_ok:
        print("\n❌ Orchestrator test failed")
        return False

    print("\n🎉 All tests passed! AI Integration Foundation is working correctly.")
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
