# AI Coding Agent - Supabase + Redis Refactor Summary

## 1. db/ Module

### db/__init__.py
"""
Database module for AI Coding Agent.
Exports: supabase_client, pg_client (optional), redis_client, and helpers.
"""
from .supabase_client import get_supabase, query_table, insert_row, upsert_embedding
from .pg_client import get_pg, run_sql
from .redis_client import get_redis, redis_get, redis_set, publish, subscribe, enqueue_job, dequeue_job

### db/supabase_client.py
"""
Supabase client setup and helpers.
"""
import os
from typing import Any, Dict, List, Optional, Union
from supabase import create_client, Client

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
_supabase: Optional[Client] = None

def get_supabase() -> Client:
    global _supabase
    if _supabase is None:
        _supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    return _supabase

def query_table(table: str, filters: Optional[Dict[str, Any]] = None) -> Any:
    client = get_supabase()
    query = client.table(table).select("*")
    if filters:
        for k, v in filters.items():
            query = query.eq(k, v)
    return query.execute()

def insert_row(table: str, row: Dict[str, Any]) -> Any:
    client = get_supabase()
    return client.table(table).insert(row).execute()

def upsert_embedding(table: str, id: str, embedding: List[float], metadata: Dict[str, Any]) -> Any:
    client = get_supabase()
    data = {"id": id, "embedding": embedding, **metadata}
    return client.table(table).upsert(data).execute()

# Async helpers (if needed)
import asyncio

async def async_query_table(table: str, filters: Optional[Dict[str, Any]] = None) -> Any:
    return query_table(table, filters)

async def async_insert_row(table: str, row: Dict[str, Any]) -> Any:
    return insert_row(table, row)

async def async_upsert_embedding(table: str, id: str, embedding: List[float], metadata: Dict[str, Any]) -> Any:
    return upsert_embedding(table, id, embedding, metadata)

### db/pg_client.py
"""
Optional: Thin wrapper for local Postgres (DATABASE_URL).
"""
import os
from typing import Any, Optional
DATABASE_URL = os.getenv("DATABASE_URL")

_pg_conn = None

def get_pg():
    global _pg_conn
    if _pg_conn is None and DATABASE_URL:
        import psycopg2
        _pg_conn = psycopg2.connect(DATABASE_URL)
    return _pg_conn

def run_sql(sql: str, *params) -> Any:
    conn = get_pg()
    with conn.cursor() as cur:
        cur.execute(sql, params)
        if cur.description:
            return cur.fetchall()
        conn.commit()
        return None

# Async version
async def async_run_sql(sql: str, *params) -> Any:
    import asyncpg
    conn = await asyncpg.connect(DATABASE_URL)
    result = await conn.fetch(sql, *params)
    await conn.close()
    return result

### db/redis_client.py
"""
Redis client setup and helpers.
"""
import os
from typing import Any, Optional
import aioredis

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
_redis = None

def get_redis():
    global _redis
    if _redis is None:
        import redis
        _redis = redis.Redis.from_url(REDIS_URL)
    return _redis

def redis_get(key: str) -> Any:
    return get_redis().get(key)

def redis_set(key: str, value: Any) -> None:
    get_redis().set(key, value)

def publish(channel: str, message: str) -> None:
    get_redis().publish(channel, message)

def subscribe(channel: str):
    pubsub = get_redis().pubsub()
    pubsub.subscribe(channel)
    return pubsub

def enqueue_job(queue: str, job: Any) -> None:
    get_redis().rpush(queue, job)

def dequeue_job(queue: str) -> Any:
    return get_redis().lpop(queue)

# Async helpers
async def async_get_redis():
    return await aioredis.from_url(REDIS_URL)

async def async_redis_get(key: str) -> Any:
    redis = await async_get_redis()
    return await redis.get(key)

async def async_redis_set(key: str, value: Any) -> None:
    redis = await async_get_redis()
    await redis.set(key, value)

### db/migrations.py
"""
Simple migration runner for Supabase/Postgres.
"""
import os
from typing import List
from .pg_client import run_sql

MIGRATIONS_DIR = os.path.join(os.path.dirname(__file__), "migrations")

def apply_migrations():
    files = sorted(f for f in os.listdir(MIGRATIONS_DIR) if f.endswith(".sql"))
    for fname in files:
        with open(os.path.join(MIGRATIONS_DIR, fname)) as f:
            sql = f.read()
            run_sql(sql)
        print(f"Applied migration: {fname}")

## 2. .env.example
# Supabase (hosted)
SUPABASE_URL=
SUPABASE_KEY=

# Optional local Postgres for dev (if you want to run local Postgres instead)
DATABASE_URL=postgresql://user:password@localhost:5432/ai_coding_agent
# Use DATABASE_URL only for local dev; hosted Supabase does not need this.

# Redis
REDIS_URL=redis://localhost:6379/0

# Misc
PROJECTS_DIR=/var/www/projects
APP_ENV=development

## 3. docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    volumes:
      - redis-data:/data

  # Uncomment for local dev only
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: ai-coding-agent-postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: ai_coding_agent
  #     POSTGRES_USER: ai_agent
  #     POSTGRES_PASSWORD: change_me
  #   ports:
  #     - "5432:5432"
  #   healthcheck:
  #     test: ["CMD", "pg_isready", "-U", "ai_agent"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 20s
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data

  app:
    build:
      context: .
      dockerfile: Dockerfile.app
    container_name: ai-coding-agent-app
    restart: unless-stopped
    env_file:
      - .env
    depends_on:
      - redis
      # - postgres  # Uncomment if using local Postgres
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app/backend
      - ./frontend:/app/frontend
      - user-projects:/app/user-projects

volumes:
  redis-data:
  user-projects:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./user-projects
  # postgres-data:  # Uncomment if using local Postgres

# When using hosted Supabase, do NOT start postgres locally.

## 4. scripts/check_db.py
"""
Check DB connectivity for Supabase and Redis.
"""
import os
from database.supabase_client import get_supabase, query_table
from database.redis_client import redis_set, redis_get

def main():
    print("Checking Supabase connection...")
    try:
        result = query_table("pgvector_table")  # Replace with your table name
        print("Supabase SELECT success:", result)
    except Exception as e:
        print("Supabase error:", e)

    print("Checking Redis connection...")
    try:
        redis_set("test_key", "test_value")
        val = redis_get("test_key")
        print("Redis SET/GET success:", val)
    except Exception as e:
        print("Redis error:", e)

if __name__ == "__main__":
    main()

## 5. README Snippet
## Database Architecture

This project uses Supabase (Postgres + pgvector) as the canonical database and Redis for cache/queues.

### Environment Setup

- Configure `.env` or `.env.example` with:
  - `SUPABASE_URL` and `SUPABASE_KEY` for hosted Supabase
  - `DATABASE_URL` for optional local Postgres (dev only)
  - `REDIS_URL` for Redis

### Migrations

- Place SQL migration files in `db/migrations/`
- Run migrations with:
  ```sh
  python -m database.migrations
  ```
  Or use Alembic if preferred.

### Local Development

- By default, only Redis runs locally.
- Uncomment the `postgres` service in `docker-compose.yml` for local Postgres dev.
- Start everything:
  ```sh
  docker-compose up --build
  ```

### Cleanup

- To remove old containers, images, and cache:
  ```sh
  docker compose down --volumes --remove-orphans
  docker system prune -af
  docker volume prune -f
  docker builder prune -a -f
  ```
- Only run these after confirming the new setup works.

### Notes

- All legacy database integrations (SQLite, MySQL, MongoDB, etc.) have been removed.
- All backend code now uses the new `db/` module for database and cache access.
