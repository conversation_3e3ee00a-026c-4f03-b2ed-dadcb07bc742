"""
Simple migration runner for Supabase/Postgres.
"""
import os
from typing import List
from .pg_client import run_sql

MIGRATIONS_DIR = os.path.join(os.path.dirname(__file__), "migrations")

def apply_migrations():
    files = sorted(f for f in os.listdir(MIGRATIONS_DIR) if f.endswith(".sql"))
    for fname in files:
        with open(os.path.join(MIGRATIONS_DIR, fname)) as f:
            sql = f.read()
            run_sql(sql)
        print(f"Applied migration: {fname}")

def apply_rls_migrations():
    """
    Enable RLS and create user isolation policies for all user data tables.
    """
    # Example for a table named 'user_projects'
    run_sql("ALTER TABLE user_projects ENABLE ROW LEVEL SECURITY;")
    run_sql("CREATE POLICY user_isolation ON user_projects FOR ALL TO app_user USING (user_id = current_setting('app.current_user_id')::uuid);")
    run_sql("ALTER TABLE user_projects FORCE ROW LEVEL SECURITY;")
    print("RLS enabled and user isolation policy applied to user_projects.")
