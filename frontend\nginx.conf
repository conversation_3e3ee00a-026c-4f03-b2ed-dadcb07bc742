http {
    upstream backend_upstream {
        server ai-coding-agent-api-prod:8000 max_fails=3 fail_timeout=10s;
        server ai-coding-agent-api-prod:8000 max_fails=3 fail_timeout=10s;
        server ai-coding-agent-api-prod:8000 max_fails=3 fail_timeout=10s;
        # For Docker Compose, all replicas share the same DNS name; for Swarm/K8s, list each replica by name/IP
        # Use least_conn for load balancing
        least_conn;
        # Enable session affinity if needed
        # ip_hash;
        # Health checks (nginx plus or external)
    }

    server {
        listen 443 ssl http2;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # Enable gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied expired no-cache no-store private auth;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/javascript
            application/xml+rss
            application/json;

        # Advanced Security Headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        add_header Permissions-Policy "geolocation=(), microphone=()" always;
        add_header X-Permitted-Cross-Domain-Policies "none" always;

        # Rate Limiting for API (per upstream server)
        limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
        limit_req zone=api_limit burst=20 nodelay;

        # Handle static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # Handle API requests - proxy to backend
        location /api/ {
            proxy_pass http://backend_upstream;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # WebSocket support
        location /ws/ {
            proxy_pass http://backend_upstream;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }

        # Healthcheck endpoint
        location /nginx-health {
            access_log off;
            return 200 "OK";
        }

        # Monitoring endpoint for nginx metrics
        location /nginx_status {
            stub_status;
            allow 127.0.0.1;
            deny all;
        }

        # Handle React Router (SPA routing)
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Error pages
        error_page 404 /index.html;
        error_log /var/log/nginx/error.log warn;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
