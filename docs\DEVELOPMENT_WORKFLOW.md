# AI Coding Agent - Development Workflow Guide

*Complete guide for containerized development and production deployment*

## 🎯 Overview

This guide covers the complete development workflow for the AI Coding Agent project, including development mode with hot reload, testing, and production deployment.

## 🏗️ Architecture Modes

### **Development Mode**
- **Hot reload enabled** for both frontend and backend
- **Source code mounted** into containers via volumes
- **Debug logging** and development tools enabled
- **Faster iteration** with immediate feedback

### **Production Mode**
- **Optimized builds** with multi-stage Dockerfiles
- **No source mounting** - code baked into images
- **Security hardened** with non-root users and resource limits
- **Performance optimized** with production settings

## 🚀 Development Mode Setup

### **Prerequisites**
1. **Docker Desktop** running and healthy
2. **Git Bash PATH** configured (see `.bash_profile` setup)
3. **Virtual environment** activated: `source venv/bin/activate`

### **Start Development Environment**

```bash
# Full stack development (recommended)
docker-compose -f docker-compose.dev.yml up -d

# Backend only (if working on API)
docker-compose -f docker-compose.dev.yml up -d app-dev

# Access individual services directly
# PostgreSQL: localhost:5432
# Redis: localhost:6379
# Backend API: http://localhost:8000
# Frontend: http://localhost:80
```

### **Development Ports**
- **Backend API**: `http://localhost:8000`
- **Frontend Dev**: `http://localhost:3001` (container) or `http://localhost:3000` (local)
- **PostgreSQL**: `localhost:5432`
- **Redis**: `localhost:6379`
- **NGINX**: `http://localhost:80`

## 📝 Development Workflow

### **Backend Development (Python/FastAPI)**

**1. File Structure:**
```
backend/src/ai_coding_agent/
├── routers/          # API endpoints
├── services/         # Business logic
├── models/           # Data models
├── config/           # Configuration
└── utils/            # Utilities
```

**2. Hot Reload Workflow:**
```bash
# 1. Edit files locally in your IDE
code backend/src/ai_coding_agent/routers/containers.py

# 2. Changes auto-apply to container (no rebuild needed)
# 3. Test immediately
curl http://localhost:8000/api/v1/containers/dev-status

# 4. Check logs if needed
docker logs ai-coding-agent-app-dev -f
```

**3. Adding New Features:**
```python
# Example: Add new endpoint
@router.get("/new-feature")
async def new_feature():
    return {"status": "working", "timestamp": datetime.now().isoformat()}
```

**4. Testing:**
```bash
# Run tests in container
docker exec ai-coding-agent-app-dev python -m pytest tests/

# Run specific test
docker exec ai-coding-agent-app-dev python -m pytest tests/test_containers.py -v

# Check test coverage
docker exec ai-coding-agent-app-dev python -m pytest --cov=ai_coding_agent tests/
```

### **Frontend Development (React/TypeScript)**

**Option A: Local Development (Recommended)**
```bash
# Navigate to frontend
cd frontend

# Install dependencies
npm install

# Start development server
npm start

# Access at http://localhost:3000
```

**Option B: Container Development**
```bash
# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# Access at http://localhost:80 (through nginx) or directly at http://localhost:3000
```

**Frontend File Structure:**
```
frontend/src/
├── components/       # React components
├── pages/           # Page components
├── hooks/           # Custom hooks
├── services/        # API services
├── utils/           # Utilities
└── styles/          # CSS/styling
```

### **Database Development**

**PostgreSQL Access:**
```bash
# Connect to database
docker exec -it ai-coding-agent-postgres psql -U agent -d ai_coding_agent

# Run migrations
docker exec ai-coding-agent-app-dev alembic upgrade head

# Create new migration
docker exec ai-coding-agent-app-dev alembic revision --autogenerate -m "description"
```

**Redis Access:**
```bash
# Connect to Redis
docker exec -it ai-coding-agent-redis redis-cli

# Monitor Redis
docker exec ai-coding-agent-redis redis-cli monitor
```

## 🔄 Mode Switching

### **Switch to Development Mode**

```bash
# Stop production containers
docker-compose down

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Verify development mode
curl http://localhost:8000/api/v1/containers/dev-status
```

### **Switch to Production Mode**

```bash
# Stop development containers
docker-compose -f docker-compose.dev.yml down

# Build production images
docker-compose build

# Start production environment
docker-compose up -d

# Verify production mode
curl http://localhost:8000/api/v1/health
```

### **Clean Rebuild (When Needed)**

```bash
# Stop all containers
docker-compose down

# Remove containers and images
docker-compose rm -f
docker rmi $(docker images -q aicodingaagent-*)

# Prune build cache
docker builder prune -f

# Rebuild and start
docker-compose build --no-cache
docker-compose up -d
```

## 🧪 Testing Workflow

### **Development Testing**
```bash
# Backend unit tests
docker exec ai-coding-agent-app-dev python -m pytest tests/unit/

# Integration tests
docker exec ai-coding-agent-app-dev python -m pytest tests/integration/

# API endpoint tests
docker exec ai-coding-agent-app-dev python -m pytest tests/api/

# Frontend tests (if using container)
docker exec ai-coding-agent-app-dev npm test

# Frontend tests (if local)
cd frontend && npm test
```

### **Production Testing**
```bash
# Health checks
curl http://localhost:8000/api/v1/health
curl http://localhost/health

# Load testing
docker exec ai-coding-agent-backend python -m pytest tests/load/

# Security testing
docker exec ai-coding-agent-backend python -m pytest tests/security/
```

## 📊 Monitoring & Debugging

### **Container Status**
```bash
# Check all containers
docker ps

# Check specific service
docker ps | grep app-dev

# Check container health
docker inspect ai-coding-agent-app-dev | grep Health -A 10
```

### **Logs**
```bash
# App logs
docker logs ai-coding-agent-app-dev -f

# All services logs
docker-compose -f docker-compose.dev.yml logs -f

# Specific service logs
docker-compose -f docker-compose.dev.yml logs -f app-dev
```

### **Resource Usage**
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Network inspection
docker network ls
docker network inspect aicodingaagent_ai-coding-agent-network
```

## 🔧 Troubleshooting

### **Common Issues**

**1. Port Conflicts**
```bash
# Check what's using a port
netstat -ano | findstr :8000

# Kill process using port
taskkill /PID <process_id> /F
```

**2. Container Won't Start**
```bash
# Check logs
docker logs ai-coding-agent-app-dev

# Inspect container
docker inspect ai-coding-agent-app-dev

# Restart container
docker-compose -f docker-compose.dev.yml restart app-dev
```

**3. Hot Reload Not Working**
```bash
# Verify volume mounts
docker inspect ai-coding-agent-app-dev | grep Mounts -A 20

# Check if in development mode
curl http://localhost:8000/api/v1/containers/dev-status
```

**4. Database Connection Issues**
```bash
# Check database health
docker exec ai-coding-agent-postgres pg_isready -U agent

# Reset database
docker-compose restart postgres
docker exec ai-coding-agent-app-dev alembic upgrade head
```

## 📋 Development Checklist

### **Before Starting Development**
- [ ] Docker Desktop running
- [ ] Virtual environment activated
- [ ] Git Bash PATH configured
- [ ] Development containers started
- [ ] Health checks passing

### **Before Committing Code**
- [ ] Tests passing
- [ ] Code formatted (Black, Prettier)
- [ ] No linting errors
- [ ] Documentation updated
- [ ] Hot reload verified

### **Before Production Deployment**
- [ ] All tests passing
- [ ] Production build successful
- [ ] Security scan clean
- [ ] Performance benchmarks met
- [ ] Database migrations applied

## 🎯 Implementation Plan Integration

### **Working on Consolidated Plan Items**

When implementing features from `docs/CONSOLIDATED_IMPLEMENTATION_PLAN.md`:

1. **Start Development Mode**
2. **Edit Code Locally** (hot reload active)
3. **Test Immediately** (no rebuild needed)
4. **Iterate Quickly** (immediate feedback)
5. **Commit When Ready**

### **Example: Phase A0.1 Implementation**
```bash
# 1. Start development
docker-compose -f docker-compose.dev.yml up -d

# 2. Edit container orchestrator
code backend/src/ai_coding_agent/services/container_manager.py

# 3. Test new functionality
curl http://localhost:8000/api/v1/containers/implementation-progress

# 4. Run tests
docker exec ai-coding-agent-app-dev python -m pytest tests/test_containers.py
```

This workflow ensures efficient development while maintaining the container-per-user architecture and security requirements.

## Authentication Workflow

- All authentication (registration, login, user management) is now handled via Supabase Auth endpoints (`supabase_auth.py`).
- Do not use legacy authentication endpoints or services.
- Update any scripts, tests, or documentation to reference Supabase Auth only.
