#!/usr/bin/env bash
set -e
NAMESPACE=ai-coding-agent

# Apply manifests in order
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secrets.yaml
kubectl apply -f postgres-configmap.yaml
kubectl apply -f redis-configmap.yaml
kubectl apply -f postgres-statefulset.yaml
kubectl apply -f postgres-service.yaml
kubectl apply -f pgbouncer-deployment.yaml
kubectl apply -f pgbouncer-service.yaml
kubectl apply -f redis-deployment.yaml
kubectl apply -f redis-service.yaml
kubectl apply -f redis-pvc.yaml
kubectl apply -f backend-serviceaccount.yaml
kubectl apply -f frontend-serviceaccount.yaml
kubectl apply -f metrics-clusterrole.yaml
kubectl apply -f metrics-clusterrolebinding.yaml
kubectl apply -f backend-deployment.yaml
kubectl apply -f backend-hpa.yaml
kubectl apply -f frontend-deployment.yaml
kubectl apply -f frontend-hpa.yaml
kubectl apply -f servicemonitor-backend.yaml
kubectl apply -f servicemonitor-frontend.yaml
kubectl apply -f prometheus-adapter-config.yaml
kubectl apply -f ingress.yaml

# Wait for deployments to be ready
kubectl rollout status deployment/ai-coding-agent-backend -n $NAMESPACE
kubectl rollout status deployment/ai-coding-agent-frontend -n $NAMESPACE
kubectl rollout status deployment/ai-coding-agent-redis-k -n $NAMESPACE
kubectl rollout status deployment/ai-coding-agent-pgbouncer-k8s -n $NAMESPACE

# Health checks
kubectl get pods -n $NAMESPACE
kubectl get services -n $NAMESPACE
kubectl get ingress -n $NAMESPACE
kubectl get hpa -n $NAMESPACE
kubectl get events -n $NAMESPACE
