# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
build/
dist/

# Development
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Testing
coverage/
.nyc_output

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Git
.git/
.gitignore

# Documentation
README.md
docs/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

__pycache__
*.pyc
*.pyo
*.pyd
*.db
*.sqlite3
*.log
node_modules/
build/
dist/
.env
.env.*
.DS_Store
.vscode/
.git/
.gitignore
coverage/
frontend/node_modules/
frontend/build/
frontend/dist/
backend/__pycache__/
backend/build/
backend/dist/
backend/.pytest_cache/
backend/.mypy_cache/
backend/.coverage
backend/.env
backend/.env.*
backend/.DS_Store
backend/.vscode/
backend/.git/
backend/.gitignore
backend/coverage/
