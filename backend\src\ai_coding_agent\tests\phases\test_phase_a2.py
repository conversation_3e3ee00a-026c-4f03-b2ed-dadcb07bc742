#!/usr/bin/env python3
"""
Test script for Phase A2: Enhanced Model Configuration & Orchestrator

This script validates the enhanced orchestrator functionality including:
- Intelligent model routing and selection
- Load balancing across available models
- Health monitoring and fallback mechanisms
- Performance tracking and analytics
- Quality assessment and optimization
"""

import asyncio
import sys
import json
import time
from pathlib import Path

# Import path is handled by pyproject.toml configuration

from ai_coding_agent.orchestrator import (
    EnhancedOrchestrator, TaskContext, TaskType, TaskComplexity,
    dispatch_to_agent, route_model_by_task, get_model_analytics,
    get_load_balancing_status, optimize_model_routing
)


async def test_model_routing():
    """Test intelligent model routing."""
    print("=== Testing Model Routing ===")

    orchestrator = EnhancedOrchestrator()

    test_cases = [
        ("architect", TaskType.PLANNING, TaskComplexity.COMPLEX),
        ("frontend", TaskType.UI_COMPONENT, TaskComplexity.MODERATE),
        ("backend", TaskType.API_DESIGN, TaskComplexity.EXPERT),
        ("shell", TaskType.SCRIPTING, TaskComplexity.SIMPLE),
        ("issue_fix", TaskType.DEBUGGING, TaskComplexity.COMPLEX)
    ]

    for agent, task_type, complexity in test_cases:
        model = await orchestrator.route_model_by_task(agent, task_type, complexity)
        print(f"Agent: {agent:12} | Task: {task_type.value:20} | Complexity: {complexity.value:8} | Model: {model}")

    await orchestrator.close()


async def test_load_balancing():
    """Test load balancing functionality."""
    print("\n=== Testing Load Balancing ===")

    orchestrator = EnhancedOrchestrator()

    # Simulate multiple requests to the same agent type
    models_used = []
    for i in range(10):
        model = await orchestrator.route_model_by_task("frontend", TaskType.UI_COMPONENT, TaskComplexity.MODERATE)
        models_used.append(model)
        orchestrator.load_balancer.record_request(model)
        print(f"Request {i+1}: {model}")
        # Simulate some requests completing
        if i % 3 == 0:
            orchestrator.load_balancer.release_request(model)

    # Check load distribution
    print("\nLoad distribution:")
    status = await orchestrator.get_load_balancing_status()
    for model, load in status["current_loads"].items():
        print(f"  {model}: {load} active requests")

    await orchestrator.close()


async def test_task_complexity_assessment():
    """Test task complexity assessment."""
    print("\n=== Testing Task Complexity Assessment ===")

    orchestrator = EnhancedOrchestrator()

    test_tasks = [
        "Fix bug",
        "Create a simple React component for displaying user data",
        "Design a comprehensive authentication system with JWT tokens, password hashing, and role-based access control",
        "Implement a microservices architecture with API gateway, service discovery, load balancing, and distributed tracing for a large-scale e-commerce platform"
    ]

    for task in test_tasks:
        complexity = await orchestrator._assess_task_complexity(task, None)
        task_type = await orchestrator._classify_task_type(task)
        print(f"Task: {task[:50]:<50} | Complexity: {complexity.value:8} | Type: {task_type.value}")

    await orchestrator.close()


async def test_quality_assessment():
    """Test response quality assessment."""
    print("\n=== Testing Quality Assessment ===")

    orchestrator = EnhancedOrchestrator()

    test_responses = [
        ("Short response", "OK"),
        ("Code response", """
Here's a React component for user display:

```jsx
function UserProfile({ user }) {
    return (
        <div className="user-profile">
            <h2>{user.name}</h2>
            <p>{user.email}</p>
        </div>
    );
}
```

This component takes a user prop and displays the name and email.
        """),
        ("Detailed response", """
To implement user authentication, I recommend the following approach:

1. Use JWT tokens for session management
2. Implement password hashing with bcrypt
3. Create middleware for route protection
4. Add role-based access control

```python
from fastapi import FastAPI, Depends, HTTPException
from fastapi.security import HTTPBearer

app = FastAPI()
security = HTTPBearer()

@app.post("/login")
async def login(credentials: UserCredentials):
    # Verify user credentials
    user = authenticate_user(credentials.username, credentials.password)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    # Generate JWT token
    token = create_access_token(user.id)
    return {"access_token": token, "token_type": "bearer"}
```

This approach ensures security while maintaining good performance.
        """),
        ("Uncertain response", """
I'm not sure about this, but maybe you could try using React hooks.
Perhaps useState might work, or it could be useEffect. I'm uncertain about the best approach.
        """)
    ]

    for description, response in test_responses:
        confidence = await orchestrator._assess_confidence(response, TaskType.CODE_GENERATION)
        quality = await orchestrator._assess_quality(response, TaskType.CODE_GENERATION)
        artifacts = await orchestrator._extract_artifacts(response)

        print(f"\n{description}:")
        print(f"  Confidence: {confidence:.2f}")
        print(f"  Quality: {quality:.2f}")
        print(f"  Artifacts: {len(artifacts)}")

        if artifacts:
            for artifact in artifacts:
                print(f"    - {artifact['type']}: {artifact['name']}")

    await orchestrator.close()


async def test_full_dispatch():
    """Test full task dispatch with all features."""
    print("\n=== Testing Full Task Dispatch ===")

    orchestrator = EnhancedOrchestrator()

    # Create a test context
    context = TaskContext(
        project_id="test_project",
        user_id="test_user",
        session_id="test_session"
    )

    # Test task
    task = """
    Create a FastAPI endpoint for user registration that:
    1. Accepts email and password in JSON format
    2. Validates email format using regex
    3. Hashes password using bcrypt
    4. Stores user in database
    5. Returns success response with user ID
    """

    print("Dispatching task to backend agent...")
    start_time = time.time()

    try:
        result = await orchestrator.dispatch_to_agent(
            agent_name="backend",
            task=task,
            context=context,
            task_type=TaskType.API_DESIGN,
            complexity=TaskComplexity.MODERATE
        )

        end_time = time.time()

        print(f"✅ Task completed successfully in {end_time - start_time:.2f} seconds")
        print(f"Agent: {result.agent_name}")
        print(f"Model Used: {result.model_used}")
        print(f"Task Type: {result.task_type}")
        print(f"Complexity: {result.complexity_assessed}")
        print(f"Confidence: {result.confidence_score:.2f}")
        print(f"Quality Score: {result.quality_score:.2f}")
        print(f"Processing Time: {result.processing_time:.2f}s")
        print(f"Review Required: {result.review_required}")
        print(f"Artifacts: {len(result.artifacts)}")

        if result.artifacts:
            print("\nArtifacts found:")
            for artifact in result.artifacts:
                print(f"  - {artifact['type']}: {artifact['name']}")

        if result.next_steps:
            print(f"\nNext Steps: {', '.join(result.next_steps)}")

        if result.dependencies:
            print(f"Dependencies: {', '.join(result.dependencies)}")

    except Exception as e:
        print(f"❌ Task failed: {str(e)}")

    await orchestrator.close()


async def test_analytics():
    """Test analytics and monitoring features."""
    print("\n=== Testing Analytics ===")

    orchestrator = EnhancedOrchestrator()

    # Simulate some activity to generate metrics
    context = TaskContext()
    tasks = [
        ("frontend", "Create a login form", TaskType.UI_COMPONENT),
        ("backend", "Build user API", TaskType.API_DESIGN),
        ("shell", "Deploy application", TaskType.DEPLOYMENT)
    ]

    print("Generating some activity for metrics...")
    for agent, task, task_type in tasks:
        try:
            await orchestrator.dispatch_to_agent(agent, task, context, task_type)
        except Exception:
            pass  # Expected to fail without actual Ollama, but will generate metrics

    # Get analytics
    analytics = await orchestrator.get_model_analytics()
    print(f"\nModel Analytics:")
    print(f"Total models tracked: {analytics['summary']['total_models']}")
    print(f"Healthy models: {analytics['summary']['healthy_models']}")
    print(f"Average health score: {analytics['summary']['avg_health_score']:.2f}")

    for model, data in analytics['models'].items():
        print(f"\n{model}:")
        print(f"  Health Score: {data['health_score']:.2f}")
        print(f"  Success Rate: {data['success_rate']:.2%}")
        print(f"  Total Requests: {data['total_requests']}")

    # Get load balancing status
    lb_status = await orchestrator.get_load_balancing_status()
    print(f"\nLoad Balancing Status:")
    print(f"Strategy: {lb_status['strategy']}")
    print(f"Current loads: {lb_status['current_loads']}")

    # Get optimization suggestions
    optimizations = await orchestrator.optimize_model_routing()
    print(f"\nOptimization Suggestions:")
    for suggestion in optimizations['suggestions']:
        print(f"  {suggestion['type']}: {suggestion['message']}")

    await orchestrator.close()


async def test_health_monitoring():
    """Test health monitoring functionality."""
    print("\n=== Testing Health Monitoring ===")

    orchestrator = EnhancedOrchestrator()

    # Test with actual models from our configuration + embedding models
    test_models = [
        "llama3.2:3b",
        "starcoder2:3b",
        "deepseek-coder:6.7b-instruct",
        "qwen2.5:3b",
        "yi-coder:1.5b",
        "nomic-embed-text:v1.5",  # LTKB embedding model
        "mxbai-embed-large",      # STPM embedding model
        "nonexistent-model"       # This should fail as expected
    ]

    print("Testing model health checks...")
    healthy_count = 0
    for model in test_models:
        is_healthy = await orchestrator.health_monitor.check_model_health(model)
        status = "✅ Healthy" if is_healthy else "❌ Unhealthy"
        print(f"  {model}: {status}")
        if is_healthy:
            healthy_count += 1

    print(f"\nHealth Summary: {healthy_count}/{len(test_models)} models healthy")

    # Test getting healthy models from a list
    print(f"\nFinding healthy models from list...")
    healthy = await orchestrator.health_monitor.get_healthy_models(test_models)
    print(f"Healthy models: {healthy}")
    print(f"Health percentage: {len(healthy)/len(test_models)*100:.1f}%")

    await orchestrator.close()


async def main():
    """Run all Phase A2 tests."""
    print("🚀 Phase A2: Enhanced Model Configuration & Orchestrator Tests")
    print("=" * 80)

    tests = [
        test_model_routing,
        test_load_balancing,
        test_task_complexity_assessment,
        test_quality_assessment,
        test_health_monitoring,
        test_analytics,
        test_full_dispatch,  # Run this last as it might fail without Ollama
    ]

    for test_func in tests:
        try:
            await test_func()
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed: {str(e)}")

        print("\n" + "-" * 40)

    print("\n✅ Phase A2 testing completed!")
    print("\nNote: Some tests may show connection errors if Ollama is not running.")
    print("This is expected and doesn't indicate implementation issues.")


if __name__ == "__main__":
    asyncio.run(main())
