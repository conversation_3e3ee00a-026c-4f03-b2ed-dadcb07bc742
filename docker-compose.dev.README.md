# Development Docker Compose Setup

This document explains the development Docker Compose setup for the AI Coding Agent project.

## Overview

The development setup uses `docker-compose.dev.yml` which creates a simplified environment with fewer containers than the production setup. This prevents resource exhaustion and makes development faster.

## Services

The development setup includes:

1. **app-dev** - Consolidated application container with both frontend and backend
2. **postgres** - PostgreSQL database for development
3. **redis** - Redis cache for development

## Key Differences from Production

1. **Fewer Containers**: Only 3 containers instead of 4-5 in production
2. **Hot Reload**: Source code is mounted as volumes for immediate changes
3. **Direct Access**: Services are exposed on their standard ports for direct access
4. **Debug Mode**: Applications run in debug mode with more verbose logging
5. **Development Networks**: Uses separate development network and volume names

## Usage

### Start Development Environment

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# Start specific service
docker-compose -f docker-compose.dev.yml up -d app-dev
```

### Stop Development Environment

```bash
# Stop all services
docker-compose -f docker-compose.dev.yml down

# Stop and remove volumes
docker-compose -f docker-compose.dev.yml down -v
```

### View Logs

```bash
# View all logs
docker-compose -f docker-compose.dev.yml logs -f

# View specific service logs
docker-compose -f docker-compose.dev.yml logs -f app-dev
```

## Direct Service Access

With this setup, you can access services directly:

- **Frontend**: http://localhost:80
- **Backend API**: http://localhost:8000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## Hot Reload

The development setup supports hot reload for both frontend and backend:

- Changes to frontend code in `./frontend/src` are immediately reflected
- Changes to backend code in `./backend/src` are immediately reflected
- No need to rebuild containers for code changes

## Resource Usage

The development setup uses fewer resources than production:

- **app-dev**: 1GB RAM, 1 CPU
- **postgres**: 768MB RAM, 0.8 CPU
- **redis**: 256MB RAM, 0.3 CPU

Total: ~2GB RAM, 2.1 CPU

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Make sure no other services are using ports 80, 8000, 5432, or 6379
2. **Volume Permissions**: On some systems, you may need to adjust volume permissions
3. **Network Issues**: If services can't communicate, restart the entire setup

### Reset Development Environment

If you're experiencing persistent issues:

```bash
# Stop all services
docker-compose -f docker-compose.dev.yml down -v

# Remove development images
docker rmi ai-coding-agent-app-dev:latest

# Prune unused containers and networks
docker system prune -f

# Rebuild and start
docker-compose -f docker-compose.dev.yml up --build -d
