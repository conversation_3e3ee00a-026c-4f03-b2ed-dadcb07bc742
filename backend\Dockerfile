# AI Coding Agent Backend Container
# Multi-stage build for production optimization

# Development stage
FROM python:3.13-slim AS development

# Set environment variables for Python and application
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/src \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONIOENCODING=utf-8

# Install system dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    gcc \
    g++ \
    git \
    wget \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for development (UID 1000 for volume mount compatibility)
RUN groupadd -g 1000 appuser && \
    useradd -r -u 1000 -g appuser appuser

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-dev.txt .

# Install Python dependencies as root to avoid PEP 668 issues
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-dev.txt

# Switch to non-root user after installing dependencies
USER appuser

# Set working directory and copy code into /app
WORKDIR /app
COPY --chown=appuser:appuser src/ ./src/
COPY --chown=appuser:appuser scripts/ ./scripts/
COPY --chown=appuser:appuser config/ ./config/
COPY --chown=appuser:appuser tests/ ./tests/

# Create necessary directories and log file
RUN mkdir -p src/ai_coding_agent/logs uploads user-projects \
    && touch src/ai_coding_agent/logs/admin_audit.log

# Switch to non-root user for development
USER appuser
WORKDIR /app

# Expose port
EXPOSE 8000

# Entrypoint for FastAPI with hot reload
CMD ["uvicorn", "src.ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM python:3.13-alpine AS production

# Set environment variables for production
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/src \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONIOENCODING=utf-8

# Create non-root user for security (UID 1000 for consistency)
RUN addgroup -g 1000 appuser && adduser -D -u 1000 -G appuser appuser

WORKDIR /app

# Copy only necessary files from development stage with correct ownership
COPY --from=development /app/src ./src/
COPY --from=development /app/config ./config/
COPY --from=development /app/scripts ./scripts/
COPY --from=development /app/requirements.txt ./requirements.txt
COPY --from=development /app/tests ./tests/
COPY --from=development /app/src/ai_coding_agent/logs ./src/ai_coding_agent/logs/
COPY --chown=appuser:appuser . .

USER appuser
EXPOSE 8000
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000"]
