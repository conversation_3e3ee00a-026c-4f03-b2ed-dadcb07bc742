-- Setup pgvector extension and embeddings table for AI Coding Agent
-- Run this in your Supabase SQL editor

-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create embeddings table for storing vector embeddings
CREATE TABLE IF NOT EXISTS embeddings (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    embedding vector(1536), -- Adjust dimension based on your embedding model
    source_document_id TEXT NOT NULL,
    chunk_index INTEGER NOT NULL DEFAULT 0,
    namespace TEXT NOT NULL,
    embedding_type TEXT NOT NULL DEFAULT 'ltkb',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_embeddings_namespace ON embeddings(namespace);
CREATE INDEX IF NOT EXISTS idx_embeddings_source_document ON embeddings(source_document_id);
CREATE INDEX IF NOT EXISTS idx_embeddings_type ON embeddings(embedding_type);
CREATE INDEX IF NOT EXISTS idx_embeddings_created_at ON embeddings(created_at);

-- Create vector similarity index (HNSW for better performance)
CREATE INDEX IF NOT EXISTS idx_embeddings_vector ON embeddings 
USING hnsw (embedding vector_cosine_ops);

-- Create function for vector similarity search
CREATE OR REPLACE FUNCTION match_embeddings(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    namespace_filter text DEFAULT NULL,
    embedding_type_filter text DEFAULT NULL,
    metadata_filter jsonb DEFAULT NULL
)
RETURNS TABLE (
    id text,
    content text,
    source_document_id text,
    chunk_index int,
    namespace text,
    embedding_type text,
    metadata jsonb,
    distance float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        e.id,
        e.content,
        e.source_document_id,
        e.chunk_index,
        e.namespace,
        e.embedding_type,
        e.metadata,
        1 - (e.embedding <=> query_embedding) as distance
    FROM embeddings e
    WHERE 
        (namespace_filter IS NULL OR e.namespace = namespace_filter)
        AND (embedding_type_filter IS NULL OR e.embedding_type = embedding_type_filter)
        AND (metadata_filter IS NULL OR e.metadata @> metadata_filter)
        AND (1 - (e.embedding <=> query_embedding)) > match_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create function to enable pgvector (for use in application)
CREATE OR REPLACE FUNCTION enable_pgvector()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- This function is a no-op since we already enabled the extension
    -- It's here for compatibility with the application code
    NULL;
END;
$$;

-- Create function to create embeddings table (for use in application)
CREATE OR REPLACE FUNCTION create_embeddings_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- This function is a no-op since we already created the table
    -- It's here for compatibility with the application code
    NULL;
END;
$$;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_embeddings_updated_at 
    BEFORE UPDATE ON embeddings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions (adjust as needed for your setup)
-- These might need to be adjusted based on your Supabase configuration
-- GRANT ALL ON embeddings TO authenticated;
-- GRANT ALL ON embeddings TO service_role;

-- Create some sample data for testing (optional)
-- INSERT INTO embeddings (id, content, embedding, source_document_id, namespace, embedding_type, metadata)
-- VALUES (
--     'test-1',
--     'This is a test document for vector similarity search.',
--     '[0.1, 0.2, 0.3]'::vector, -- Replace with actual embedding
--     'test-doc-1',
--     'ltkb',
--     'ltkb',
--     '{"test": true}'::jsonb
-- );

-- Verify the setup
SELECT 'pgvector extension enabled' as status 
WHERE EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector');

SELECT 'embeddings table created' as status 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'embeddings');

SELECT 'match_embeddings function created' as status 
WHERE EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'match_embeddings');
