"""
Base AI Provider Interface

Defines the abstract interface for all AI providers to ensure consistent
behavior across different AI services (Ollama, OpenAI, Anthropic, etc.).
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Optional, AsyncGenerator, Any
from datetime import datetime

from pydantic import BaseModel, Field

from ai_coding_agent.agents import AgentRole


class ProviderType(str, Enum):
    """Types of AI providers."""
    LOCAL = "local"
    CLOUD = "cloud"


class HealthStatus(str, Enum):
    """Health status of an AI provider or model."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ModelInfo(BaseModel):
    """Information about an AI model."""
    name: str
    size: Optional[str] = None
    capabilities: List[str] = Field(default_factory=list)
    agent_roles: List[AgentRole] = Field(default_factory=list)
    context_window: Optional[int] = None
    max_tokens: Optional[int] = None


class ChatMessage(BaseModel):
    """A message in a chat conversation."""
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)


class ChatRequest(BaseModel):
    """Request for AI chat completion."""
    messages: List[ChatMessage]
    agent_role: Optional[AgentRole] = None
    model: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    stream: bool = False
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """Response from AI chat completion."""
    content: str
    model: str
    agent_role: Optional[AgentRole] = None
    tokens_used: Optional[int] = None
    finish_reason: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ModelHealthCheck(BaseModel):
    """Health check result for a specific model."""
    model: str
    status: HealthStatus
    latency_ms: Optional[float] = None
    error: Optional[str] = None
    last_checked: datetime = Field(default_factory=datetime.now)


class ProviderHealthCheck(BaseModel):
    """Overall health check for an AI provider."""
    provider_name: str
    provider_type: ProviderType
    status: HealthStatus
    models: List[ModelHealthCheck] = Field(default_factory=list)
    error: Optional[str] = None
    last_checked: datetime = Field(default_factory=datetime.now)


class AIProvider(ABC):
    """Abstract base class for AI providers."""

    def __init__(self, name: str, provider_type: ProviderType):
        self.name = name
        self.provider_type = provider_type

    @abstractmethod
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """
        Generate a chat completion response.

        Args:
            request: The chat request containing messages and parameters

        Returns:
            The chat response with generated content
        """
        pass

    @abstractmethod
    def stream_chat(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """
        Generate a streaming chat completion response.

        Args:
            request: The chat request containing messages and parameters

        Yields:
            Chunks of the generated response content
        """
        pass

    @abstractmethod
    async def health_check(self) -> ProviderHealthCheck:
        """
        Check the health status of the provider and its models.

        Returns:
            Health status information for the provider
        """
        pass

    @abstractmethod
    async def get_models(self) -> List[ModelInfo]:
        """
        Get information about available models.

        Returns:
            List of model information
        """
        pass

    async def close(self) -> None:
        """
        Close the provider and cleanup resources.

        Default implementation does nothing. Subclasses should override
        if they need to perform cleanup operations.
        """
        pass

    @abstractmethod
    async def get_model_for_agent(self, agent_role: AgentRole) -> Optional[str]:
        """
        Get the recommended model for a specific agent role.

        Args:
            agent_role: The agent role to get a model for

        Returns:
            The model name, or None if no suitable model is found
        """
        pass
