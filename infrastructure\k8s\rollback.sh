#!/usr/bin/env bash
set -e
NAMESPACE=ai-coding-agent

# Rollback deployments to previous revision
kubectl rollout undo deployment/ai-coding-agent-backend -n $NAMESPACE
kubectl rollout undo deployment/ai-coding-agent-frontend -n $NAMESPACE
kubectl rollout undo deployment/ai-coding-agent-redis-k -n $NAMESPACE
kubectl rollout undo deployment/ai-coding-agent-pgbouncer-k8s -n $NAMESPACE

# Monitor status
kubectl get pods -n $NAMESPACE
kubectl get events -n $NAMESPACE
