# Stage 1: Build React frontend
FROM node:18-alpine AS frontend-builder
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --omit=dev --no-audit
COPY frontend/ ./
RUN npm run build

# Stage 2: Setup Python backend
FROM python:3.11-alpine AS backend-builder
WORKDIR /app/backend
COPY backend/requirements.txt ./
# Install packages to a specific location we can copy from
# Install build dependencies and Python packages
RUN apk add --no-cache --virtual .build-deps gcc python3-dev musl-dev linux-headers && \
    pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt && \
    apk del .build-deps

# Stage 3: Development container with hot reload
FROM python:3.11-alpine AS development
# Install nginx, supervisor, and development tools
RUN apk add --no-cache nginx supervisor curl nodejs npm

# Create non-root user and required directories
RUN addgroup -g 1000 appuser && adduser -u 1000 -G appuser -s /bin/sh -D appuser && \
    mkdir -p /var/log/supervisor && \
    chmod 755 /var/log/supervisor && \
    mkdir -p /app/backend/src/ai_coding_agent/logs && \
    chown -R 1000:1000 /app/backend/src/ai_coding_agent/logs && \
    chmod -R 755 /app/backend/src/ai_coding_agent/logs

# Install Python dependencies
COPY backend/requirements.txt /app/backend/requirements.txt
RUN apk add --no-cache --virtual .build-deps gcc python3-dev musl-dev linux-headers && \
    pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r /app/backend/requirements.txt && \
    apk del .build-deps

# Set up frontend development environment
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci

# Copy application code (will be overridden by volume mounts in development)
COPY backend/ /app/backend/
COPY frontend/ /app/frontend/

# Set Python path
ENV PYTHONPATH="/app/backend/src:/app"
ENV NODE_ENV=development

# Copy configurations
COPY nginx/consolidated.conf /etc/nginx/nginx.conf
COPY supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Health check script
COPY scripts/health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

# Expose ports
EXPOSE 80 8000 3000

# Start supervisor to manage nginx and fastapi
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

# Stage 4: Production container with Nginx + FastAPI + React
FROM python:3.11-alpine AS production
# Install nginx and supervisor for process management
RUN apk add --no-cache nginx supervisor curl

# Create non-root user and required directories
RUN addgroup -g 1000 appuser && adduser -u 1000 -G appuser -s /bin/sh -D appuser && \
    mkdir -p /var/log/supervisor && \
    chmod 755 /var/log/supervisor && \
    mkdir -p /app/backend/src/ai_coding_agent/logs && \
    chown -R 1000:1000 /app/backend/src/ai_coding_agent/logs && \
    chmod -R 755 /app/backend/src/ai_coding_agent/logs

# Copy built frontend from stage 1
COPY --from=frontend-builder /app/frontend/build /usr/share/nginx/html

# Copy backend and dependencies from stage 2 with correct ownership (avoid slow recursive chown)
COPY --from=backend-builder --chown=appuser:appuser /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --chown=appuser:appuser backend/ /app/backend/
# Removed recursive chown; ownership is set during COPY for faster, reliable builds

# Set Python path to include the installed packages
ENV PYTHONPATH="/usr/local/lib/python3.11/site-packages:/app/backend/src:/app"

# Copy custom nginx configuration
COPY nginx/consolidated.conf /etc/nginx/nginx.conf

# Copy supervisor configuration to manage multiple processes
COPY supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Health check script
COPY scripts/health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

# Note: Keep running as root for supervisor to manage processes properly
# Individual programs will run as specified users in supervisord.conf

# Expose ports
EXPOSE 80 8000

# Start supervisor to manage nginx and fastapi
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
