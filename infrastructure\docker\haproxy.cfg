global
    daemon
    maxconn 4096
    log stdout local0
    
    # SSL/TLS Configuration
    ssl-default-bind-ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
    
    # Stats socket for monitoring
    stats socket /var/run/haproxy.sock mode 660 level admin

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    timeout http-request 10s
    timeout http-keep-alive 2s
    timeout check 3s
    
    # Logging
    option httplog
    option dontlognull
    option log-health-checks
    
    # Error handling
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

# Frontend for HTTP traffic
frontend http_frontend
    bind *:80
    
    # Redirect HTTP to HTTPS
    redirect scheme https code 301 if !{ ssl_fc }

# Frontend for HTTPS traffic
frontend https_frontend
    bind *:443 ssl crt /etc/ssl/certs/ai-coding-agent.pem
    
    # Security headers
    http-response set-header Strict-Transport-Security "max-age=********; includeSubDomains; preload"
    http-response set-header X-Frame-Options "DENY"
    http-response set-header X-Content-Type-Options "nosniff"
    http-response set-header X-XSS-Protection "1; mode=block"
    
    # Route based on path
    acl is_api path_beg /api/
    acl is_static path_beg /static/ /assets/ /favicon.ico /manifest.json
    
    # Backend routing
    use_backend api_backend if is_api
    default_backend frontend_backend

# Backend API servers
backend api_backend
    balance roundrobin
    option httpchk GET /api/v1/health
    http-check expect status 200
    
    # Backend instances
    server backend-1 backend-1:8000 check inter 10s fall 3 rise 2
    server backend-2 backend-2:8000 check inter 10s fall 3 rise 2
    server backend-3 backend-3:8000 check inter 10s fall 3 rise 2

# Frontend servers
backend frontend_backend
    balance roundrobin
    option httpchk GET /
    http-check expect status 200
    
    # Frontend instances
    server frontend-1 frontend-1:80 check inter 10s fall 3 rise 2
    server frontend-2 frontend-2:80 check inter 10s fall 3 rise 2
    server frontend-3 frontend-3:80 check inter 10s fall 3 rise 2

# Stats interface
listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    
    # Basic auth for stats (change in production)
    stats auth admin:admin
    
    # Additional stats
    stats show-legends
    stats show-node
