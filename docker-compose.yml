version: '3.8'

services:
  # Consolidated App (Frontend + Backend)
  app:
    build:
      context: .
      dockerfile: Dockerfile.consolidated
      target: production
    image: ai-coding-agent-app:latest
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-ai_coding_agent}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - DEBUG=false
      - LOG_LEVEL=info
      - USER_DATA_ISOLATION=true
    depends_on:
      - postgres
      - redis
    networks:
      - agent-net
    volumes:
      - user_data:/app/user_data
      - app_logs:/var/log
      - ./backend/src:/app/backend/src
      # Frontend hot reload enabled - changes to frontend/src will be reflected
      - ./frontend/src:/app/frontend/src
      - ./frontend/public:/app/frontend/public
    # Removed secrets - using environment variables for simplicity
    # Resource limits using deploy.resources format
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    ports:
      - "4000:80"   # Expose app's nginx on port 4000 to avoid HTTPS issues
      - "8000:8000" # Expose FastAPI directly for development
    healthcheck:
      test: ["CMD-SHELL", "/usr/local/bin/health-check.sh"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-ai_coding_agent}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres123}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    networks:
      - agent-net
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ai_coding_agent}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis Cache/Sessions/Rate Limiting
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-redis123}
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    networks:
      - agent-net
    volumes:
      - redis_data:/data
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Load Balancer (Optional - only if scaling app replicas)
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx-lb.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - agent-net
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Removed external secrets - using environment variables for development

networks:
  agent-net:
    driver: bridge
    name: ai-coding-agent-network

volumes:
  postgres_data:
    name: ai-coding-agent-postgres-data
  redis_data:
    name: ai-coding-agent-redis-data
  user_data:
    name: ai-coding-agent-user-data
  app_logs:
    name: ai-coding-agent-app-logs
  nginx_logs:
    name: ai-coding-agent-nginx-logs
