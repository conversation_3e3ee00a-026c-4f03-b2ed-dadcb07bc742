"""
Health check router for AI Coding Agent.

Provides endpoints for health monitoring and system status checks.
"""

from datetime import datetime
from typing import Any, Dict

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ai_coding_agent.config import settings


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str
    timestamp: datetime
    version: str
    environment: str
    uptime: float
    services: Dict[str, Any]


router = APIRouter()

# Store application start time for uptime calculation
_start_time = datetime.now()


@router.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """
    Health check endpoint.

    Returns the current status of the application and its services.

    Returns:
        HealthResponse: Application health status
    """
    try:
        current_time = datetime.now()
        uptime = (current_time - _start_time).total_seconds()

        # Check service health (placeholder for now)
        services = {
            "database": "unknown",  # Will be implemented in Phase 2
            "ai_service": "unknown",  # Will be implemented in Phase 4
        }

        return HealthResponse(
            status="healthy",
            timestamp=current_time,
            version=settings.version,
            environment=settings.environment,
            uptime=uptime,
            services=services,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/health/ready")
async def readiness_check() -> Dict[str, str]:
    """
    Readiness probe for Kubernetes/container orchestration.

    Returns:
        Dict[str, str]: Readiness status
    """
    return {"status": "ready"}


@router.get("/health/live")
async def liveness_check() -> Dict[str, str]:
    """
    Liveness probe for Kubernetes/container orchestration.

    Returns:
        Dict[str, str]: Liveness status
    """
    return {"status": "alive"}
