#!/usr/bin/env bash
set -e
NAMESPACE=ai-coding-agent
BACKEND_SERVICE=ai-coding-agent-backend

# Generate load to trigger scaling
kubectl run hpa-load-generator --image=busybox --restart=Never --namespace=$NAMESPACE -- /bin/sh -c "while true; do wget -qO- http://$BACKEND_SERVICE/api/; sleep 0.1; done" &

# Monitor HPA scaling events
kubectl get hpa -n $NAMESPACE --watch &

# Validate metrics collection
kubectl get --raw "/apis/custom.metrics.k8s.io/v1beta1" | jq .

# Monitor pods and logs
kubectl get pods -n $NAMESPACE
kubectl logs -l app=ai-coding-agent-backend -n $NAMESPACE --tail=50
kubectl describe hpa -n $NAMESPACE
kubectl get events -n $NAMESPACE
