apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-coding-agent-ingress
  namespace: ai-coding-agent
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/limit-rps: "20"
    nginx.ingress.kubernetes.io/limit-burst-multiplier: "2"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization, Content-Type, Accept"
    nginx.ingress.kubernetes.io/health-check-path: "/nginx-health"
spec:
  tls:
    - hosts:
        - ai-coding-agent.example.com
      secretName: ai-coding-agent-tls
  rules:
    - host: ai-coding-agent.example.com
      http:
        paths:
          - path: /api/
            pathType: Prefix
            backend:
              service:
                name: ai-coding-agent-backend
                port:
                  number: 80
          - path: /nginx-health
            pathType: Exact
            backend:
              service:
                name: ai-coding-agent-frontend
                port:
                  number: 80
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ai-coding-agent-frontend
                port:
                  number: 80
