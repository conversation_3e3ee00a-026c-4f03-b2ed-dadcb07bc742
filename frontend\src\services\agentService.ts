/**
 * AI Agent Service - Handles communication with the backend AI endpoints
 */

import axios from 'axios';
import {
  AgentInfo,
  TaskInfo,
  CollaborationEvent,
  AgentStats,
  CollaborationMetrics,
  AgentRole,
  AgentCapability,
  TaskStatus,
  CollaborationEventType
} from '../types/agents';

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api';

// API client setup
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request/Response interfaces matching backend
interface ChatRequest {
  message: string;
  agent_role?: AgentRole;
  conversation_id?: string;
  context?: any;
}

interface TaskRequest {
  task_description: string;
  preferred_agent?: AgentRole;
  required_capabilities?: AgentCapability[];
  context?: any;
  conversation_id?: string;
  verify_output?: boolean;
  auto_fix?: boolean;
}

interface CollaborationRequest {
  task_description: string;
  primary_agent: <PERSON><PERSON><PERSON>;
  supporting_agents?: AgentRole[];
  context?: any;
  conversation_id?: string;
}

interface ChatResponse {
  id: string;
  content: string;
  agent_role: AgentR<PERSON>;
  timestamp: string;
  metadata?: any;
}

interface TaskResponse {
  id: string;
  assignedAgent: AgentRole;
  result?: string;
  status: TaskStatus;
  error?: string;
  metadata?: any;
}

interface CollaborationResponse {
  id: string;
  result: string;
  participating_agents: AgentRole[];
  collaboration_events: CollaborationEvent[];
  metadata?: any;
}

// Simulation data for development
let simulationInterval: NodeJS.Timeout | null = null;
let collaborationEventListeners: ((events: CollaborationEvent[]) => void)[] = [];
let simulatedEvents: CollaborationEvent[] = [];
let simulatedAgents: AgentInfo[] = [];

// Initialize simulated agents
const initializeSimulatedAgents = (): AgentInfo[] => {
  return [
    {
      role: AgentRole.ARCHITECT,
      name: 'Architecture Agent',
      capabilities: [AgentCapability.ARCHITECTURE_DESIGN, AgentCapability.SYSTEM_INTEGRATION],
      status: 'active',
      lastActivity: new Date(),
      performance: { tasksCompleted: 15, successRate: 0.93, averageTime: 45 }
    },
    {
      role: AgentRole.DEVELOPER,
      name: 'Development Agent',
      capabilities: [AgentCapability.CODE_GENERATION, AgentCapability.REFACTORING],
      status: 'busy',
      lastActivity: new Date(),
      performance: { tasksCompleted: 28, successRate: 0.89, averageTime: 32 }
    },
    {
      role: AgentRole.SHELL,
      name: 'Shell Agent',
      capabilities: [AgentCapability.SHELL_OPERATIONS, AgentCapability.DEPENDENCY_MANAGEMENT],
      status: 'idle',
      lastActivity: new Date(Date.now() - 300000),
      performance: { tasksCompleted: 12, successRate: 0.95, averageTime: 18 }
    },
    {
      role: AgentRole.TESTER,
      name: 'Testing Agent',
      capabilities: [AgentCapability.TESTING, AgentCapability.DEBUGGING],
      status: 'active',
      lastActivity: new Date(),
      performance: { tasksCompleted: 22, successRate: 0.91, averageTime: 28 }
    },
    {
      role: AgentRole.REVIEWER,
      name: 'Review Agent',
      capabilities: [AgentCapability.CODE_REVIEW, AgentCapability.DOCUMENTATION],
      status: 'busy',
      lastActivity: new Date(),
      performance: { tasksCompleted: 18, successRate: 0.96, averageTime: 38 }
    }
  ];
};

class AgentService {
  constructor() {
    simulatedAgents = initializeSimulatedAgents();
  }

  // Chat with AI agents
  async chat(request: ChatRequest): Promise<ChatResponse> {
    try {
      const response = await apiClient.post<ChatResponse>('/agents/chat', request);
      return response.data;
    } catch (error) {
      console.error('Chat request failed:', error);

      // Fallback simulation
      return {
        id: `chat-${Date.now()}`,
        content: `Hello! I'm the ${request.agent_role || 'assistant'} agent. How can I help you with your project?`,
        agent_role: request.agent_role || AgentRole.ARCHITECT,
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Execute tasks through AI agents
  async executeTask(request: TaskRequest): Promise<TaskResponse> {
    try {
      const response = await apiClient.post<TaskResponse>('/agents/task', request);
      return response.data;
    } catch (error) {
      console.error('Task execution failed:', error);

      // Fallback simulation
      return {
        id: `task-${Date.now()}`,
        assignedAgent: request.preferred_agent || AgentRole.DEVELOPER,
        result: `Task "${request.task_description}" has been processed by the ${request.preferred_agent || 'development'} agent.`,
        status: TaskStatus.COMPLETED,
      };
    }
  }

  // Multi-agent collaboration
  async collaborate(request: CollaborationRequest): Promise<CollaborationResponse> {
    try {
      const response = await apiClient.post<CollaborationResponse>('/agents/collaborate', request);
      return response.data;
    } catch (error) {
      console.error('Collaboration request failed:', error);

      // Fallback simulation
      const events: CollaborationEvent[] = [
        {
          id: `event-${Date.now()}-1`,
          type: CollaborationEventType.TASK_STARTED,
          fromAgent: request.primary_agent,
          message: `Started collaboration on: ${request.task_description}`,
          timestamp: new Date(),
        }
      ];

      return {
        id: `collab-${Date.now()}`,
        result: `Collaboration initiated between ${request.primary_agent} and supporting agents.`,
        participating_agents: [request.primary_agent, ...(request.supporting_agents || [])],
        collaboration_events: events,
      };
    }
  }

  // Get available agents
  async getAgents(): Promise<AgentInfo[]> {
    try {
      const response = await apiClient.get<AgentInfo[]>('/agents');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch agents:', error);
      return simulatedAgents;
    }
  }

  // Get agent statistics
  async getAgentStats(agentRole?: AgentRole): Promise<AgentStats[]> {
    try {
      const endpoint = agentRole ? `/agents/${agentRole}/stats` : '/agents/stats';
      const response = await apiClient.get<AgentStats[]>(endpoint);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch agent stats:', error);

      // Fallback simulation
      return simulatedAgents.map(agent => ({
        role: agent.role,
        tasksCompleted: agent.performance?.tasksCompleted || 0,
        tasksInProgress: Math.floor(Math.random() * 3),
        tasksFailed: Math.floor(Math.random() * 2),
        averageCompletionTime: agent.performance?.averageTime || 30,
        successRate: agent.performance?.successRate || 0.9,
        collaborationCount: Math.floor(Math.random() * 10) + 5,
        lastActiveTime: agent.lastActivity || new Date(),
      }));
    }
  }

  // Get collaboration metrics
  async getCollaborationMetrics(): Promise<CollaborationMetrics> {
    try {
      const response = await apiClient.get<CollaborationMetrics>('/agents/collaboration/metrics');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch collaboration metrics:', error);

      // Fallback simulation
      const activeAgents = simulatedAgents.filter(a => a.status === 'active' || a.status === 'busy').length;
      const totalTasks = simulatedAgents.reduce((sum, a) => sum + (a.performance?.tasksCompleted || 0), 0);

      return {
        activeAgents,
        totalTasks,
        tasksInProgress: Math.floor(Math.random() * 8) + 2,
        completedTasks: totalTasks,
        averageTaskTime: 32.5,
        errorRate: 0.08,
        collaborationEvents: simulatedEvents.length,
        peakPerformanceTime: '2:00 PM - 4:00 PM',
        bottleneckAgent: Math.random() > 0.7 ? AgentRole.DEVELOPER : undefined,
      };
    }
  }

  // Subscribe to real-time collaboration events
  subscribeToCollaborationEvents(callback: (events: CollaborationEvent[]) => void): () => void {
    collaborationEventListeners.push(callback);

    // Immediately send current events
    callback(simulatedEvents);

    return () => {
      const index = collaborationEventListeners.indexOf(callback);
      if (index > -1) {
        collaborationEventListeners.splice(index, 1);
      }
    };
  }

  // Start activity simulation for development
  simulateActivity(): void {
    if (simulationInterval) {
      clearInterval(simulationInterval);
    }

    simulationInterval = setInterval(() => {
      // Generate random collaboration events
      const fromAgent = simulatedAgents[Math.floor(Math.random() * simulatedAgents.length)];
      const toAgent = simulatedAgents[Math.floor(Math.random() * simulatedAgents.length)];

      if (fromAgent.role !== toAgent.role) {
        const eventTypes = Object.values(CollaborationEventType);
        const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];

        const newEvent: CollaborationEvent = {
          id: `event-${Date.now()}-${Math.random()}`,
          type: eventType,
          fromAgent: fromAgent.role,
          toAgent: toAgent.role,
          taskId: Math.random() > 0.5 ? `task-${Math.floor(Math.random() * 1000)}` : undefined,
          message: this.generateEventMessage(eventType, fromAgent.role, toAgent.role),
          timestamp: new Date(),
        };

        simulatedEvents.push(newEvent);

        // Keep only recent events (last 50)
        if (simulatedEvents.length > 50) {
          simulatedEvents = simulatedEvents.slice(-50);
        }

        // Update agent statuses randomly
        simulatedAgents.forEach(agent => {
          if (Math.random() > 0.8) {
            const statuses = ['active', 'busy', 'idle'] as const;
            agent.status = statuses[Math.floor(Math.random() * statuses.length)];
            agent.lastActivity = new Date();
          }
        });

        // Notify all listeners
        collaborationEventListeners.forEach(callback => callback([...simulatedEvents]));
      }
    }, 3000); // Generate event every 3 seconds
  }

  // Stop activity simulation
  stopSimulation(): void {
    if (simulationInterval) {
      clearInterval(simulationInterval);
      simulationInterval = null;
    }
  }

  private generateEventMessage(eventType: CollaborationEventType, fromAgent: AgentRole, toAgent: AgentRole): string {
    const messages = {
      [CollaborationEventType.TASK_STARTED]: `${fromAgent} started a new task`,
      [CollaborationEventType.TASK_COMPLETED]: `${fromAgent} completed task and handed off to ${toAgent}`,
      [CollaborationEventType.TASK_FAILED]: `${fromAgent} encountered an error, requesting help from ${toAgent}`,
      [CollaborationEventType.VERIFICATION_STARTED]: `${toAgent} is verifying work from ${fromAgent}`,
      [CollaborationEventType.VERIFICATION_COMPLETED]: `${toAgent} verified and approved work from ${fromAgent}`,
      [CollaborationEventType.AGENT_HANDOFF]: `Task handoff from ${fromAgent} to ${toAgent}`,
      [CollaborationEventType.DEPENDENCY_INSTALL]: `${fromAgent} installed dependencies for ${toAgent}`,
      [CollaborationEventType.ERROR_DETECTED]: `${fromAgent} detected error, notifying ${toAgent}`,
      [CollaborationEventType.FIX_APPLIED]: `${fromAgent} applied fix suggested by ${toAgent}`,
    };

    return messages[eventType] || `${fromAgent} collaborated with ${toAgent}`;
  }
}

// Export singleton instance
export const agentService = new AgentService();
