# Frontend Dockerfile for AI Coding Agent React Application
# Multi-stage build for optimized production deployment

# Stage 1: Build the React application
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache git curl

# Install Node.js (latest LTS) and npm
RUN curl -fsSL https://unofficial-builds.nodejs.org/download/release/v20.10.0/node-v20.10.0-linux-x64.tar.xz -o node.tar.xz \
    && tar -xf node.tar.xz -C /usr/local --strip-components=1 \
    && rm node.tar.xz \
    && node -v \
    && npm -v

# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies with clean install for reproducible builds
RUN npm ci --omit=dev --silent

# Copy source code and set ownership for development
COPY --chown=node:node . .

# Build the application with error handling
RUN npm run build && \
    echo "Build completed successfully" && \
    ls -la /app/build/ && \
    echo "Static assets:" && \
    ls -la /app/build/static/ || (echo "Build failed!" && exit 1)

# Verify build output contains required files
RUN test -f /app/build/index.html || (echo "Missing index.html!" && exit 1) && \
    test -d /app/build/static || (echo "Missing static directory!" && exit 1) && \
    echo "Build verification passed"

# Stage 2: Development stage with hot reload
FROM node:20-alpine AS development

# Set working directory
WORKDIR /app

# Install development dependencies
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --silent

# Create non-root user for security (use existing node user)
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Expose development server port
EXPOSE 3000

# Start development server with hot reload
CMD ["npm", "start"]

# Stage 3: Production serving with NGINX
FROM nginx:alpine AS production

# Create non-root user for security (UID 1000, GID 1000)
RUN addgroup -g 1000 appuser && adduser -D -u 1000 -G appuser appuser

# Copy only built assets from build stage
COPY --from=build /app/build /usr/share/nginx/html

USER appuser
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
