import docker
import time
import json
from datetime import datetime

# Resource monitoring script for scheduled checks
# Follows project resource management and audit rules

def get_container_stats():
    client = docker.from_env()
    stats = {}
    for container in client.containers.list():
        s = container.stats(stream=False)
        stats[container.name] = {
            'cpu': s['cpu_stats']['cpu_usage']['total_usage'],
            'mem': s['memory_stats']['usage'],
            'timestamp': datetime.utcnow().isoformat()
        }
    return stats

def main():
    stats = get_container_stats()
    with open('scripts/resource_stats.json', 'w') as f:
        json.dump(stats, f, indent=2)
    print("[Resource Monitor] Stats written to scripts/resource_stats.json")

if __name__ == "__main__":
    main()
