#!/usr/bin/env python3
"""
Validation script for AI Coding Agent backend fixes.

This script verifies that all the fixes applied to the backend are working correctly:
- Checks for missing __init__.py files
- Tests critical imports
- Detects remaining circular dependencies
- Validates module structure
"""

import os
import sys
import importlib
import traceback
from pathlib import Path
from typing import List, Dict, Tuple, Set

def check_init_files() -> bool:
    """Check that all required __init__.py files exist."""
    print("🔍 Checking for missing __init__.py files...")

    required_init_files = [
        "backend/src/ai_coding_agent/__init__.py",
        "backend/src/ai_coding_agent/config/__init__.py",
        "backend/src/ai_coding_agent/middleware/__init__.py",
        "backend/src/ai_coding_agent/models/__init__.py",
        "backend/src/ai_coding_agent/routers/__init__.py",
        "backend/src/ai_coding_agent/services/__init__.py",
        "backend/src/ai_coding_agent/services/ai/__init__.py",
        "backend/src/ai_coding_agent/services/ai/providers/__init__.py",
        "backend/src/ai_coding_agent/utils/__init__.py",
        "backend/src/ai_coding_agent/tests/__init__.py",
    ]

    all_exist = True
    for init_file in required_init_files:
        if Path(init_file).exists():
            print(f"  ✅ {init_file}")
        else:
            print(f"  ❌ Missing: {init_file}")
            all_exist = False

    return all_exist

def test_critical_imports() -> bool:
    """Test that critical imports work correctly."""
    print("\n🧪 Testing critical imports...")

    # Add the backend src to Python path
    backend_src = Path("backend/src").resolve()
    if str(backend_src) not in sys.path:
        sys.path.insert(0, str(backend_src))

    critical_imports = [
        ("ai_coding_agent.config", "settings"),
        ("ai_coding_agent.main", "app"),
        ("ai_coding_agent.services.container_monitoring", "container_monitor"),
        ("ai_coding_agent.middleware", "UnifiedRateLimitMiddleware"),
        ("ai_coding_agent.models", "User"),
        ("ai_coding_agent.routers.health", "router"),
    ]

    all_successful = True

    for module_name, item_name in critical_imports:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, item_name):
                print(f"  ✅ {module_name}.{item_name} imported successfully")
            else:
                print(f"  ❌ {module_name}.{item_name} - attribute not found")
                all_successful = False
        except Exception as e:
            print(f"  ❌ {module_name}.{item_name} - import failed: {e}")
            all_successful = False

    return all_successful

def check_relative_imports() -> bool:
    """Check for any remaining relative imports."""
    print("\n🔍 Checking for remaining relative imports...")

    backend_dir = Path("backend/src/ai_coding_agent")
    relative_imports_found = []

    for py_file in backend_dir.rglob("*.py"):
        if "__pycache__" in str(py_file):
            continue

        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line.startswith("from .") or line.startswith("from .."):
                    relative_imports_found.append((py_file, i, line))
        except Exception as e:
            print(f"  ⚠️  Error reading {py_file}: {e}")

    if relative_imports_found:
        print(f"  ❌ Found {len(relative_imports_found)} remaining relative imports:")
        for file_path, line_num, line in relative_imports_found[:10]:  # Show first 10
            rel_path = file_path.relative_to(Path("backend/src"))
            print(f"    {rel_path}:{line_num} - {line}")
        if len(relative_imports_found) > 10:
            print(f"    ... and {len(relative_imports_found) - 10} more")
        return False
    else:
        print("  ✅ No relative imports found!")
        return True

def check_circular_dependencies() -> bool:
    """Check for potential circular dependencies."""
    print("\n🔍 Checking for circular dependencies...")

    # Add the backend src to Python path
    backend_src = Path("backend/src").resolve()
    if str(backend_src) not in sys.path:
        sys.path.insert(0, str(backend_src))

    # Test modules that were previously problematic
    test_modules = [
        "ai_coding_agent.config",
        "ai_coding_agent.services.container_monitoring",
        "ai_coding_agent.main",
        "ai_coding_agent.middleware.unified_rate_limiting",
    ]

    all_successful = True

    for module_name in test_modules:
        try:
            # Clear module from cache if it exists
            if module_name in sys.modules:
                del sys.modules[module_name]

            module = importlib.import_module(module_name)
            print(f"  ✅ {module_name} - no circular dependency detected")
        except ImportError as e:
            if "circular" in str(e).lower() or "cannot import" in str(e).lower():
                print(f"  ❌ {module_name} - potential circular dependency: {e}")
                all_successful = False
            else:
                print(f"  ⚠️  {module_name} - import error (may not be circular): {e}")
        except Exception as e:
            print(f"  ⚠️  {module_name} - unexpected error: {e}")

    return all_successful

def validate_module_structure() -> bool:
    """Validate the overall module structure."""
    print("\n🏗️  Validating module structure...")

    required_directories = [
        "backend/src/ai_coding_agent",
        "backend/src/ai_coding_agent/config",
        "backend/src/ai_coding_agent/middleware",
        "backend/src/ai_coding_agent/models",
        "backend/src/ai_coding_agent/routers",
        "backend/src/ai_coding_agent/services",
        "backend/src/ai_coding_agent/services/ai",
        "backend/src/ai_coding_agent/services/ai/providers",
        "backend/src/ai_coding_agent/utils",
    ]

    all_exist = True
    for directory in required_directories:
        if Path(directory).is_dir():
            print(f"  ✅ {directory}")
        else:
            print(f"  ❌ Missing directory: {directory}")
            all_exist = False

    return all_exist

def main():
    """Main validation function."""
    print("🔍 AI Coding Agent Backend - Fix Validation")
    print("=" * 50)

    results = []

    # Run all validation checks
    results.append(("__init__.py files", check_init_files()))
    results.append(("Module structure", validate_module_structure()))
    results.append(("Critical imports", test_critical_imports()))
    results.append(("Relative imports", check_relative_imports()))
    results.append(("Circular dependencies", check_circular_dependencies()))

    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)

    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} - {test_name}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("✅ Backend fixes have been successfully applied")
        print("✅ No circular imports detected")
        print("✅ All critical modules can be imported")
        print("✅ Module structure is correct")
    else:
        print("❌ SOME VALIDATIONS FAILED!")
        print("⚠️  Please review the failed checks above")
        print("⚠️  Additional fixes may be required")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
