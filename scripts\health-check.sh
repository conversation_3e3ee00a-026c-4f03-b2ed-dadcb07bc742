#!/bin/sh
# Health check script for consolidated app container

# Check if nginx is running
if ! pgrep nginx > /dev/null; then
    echo "ERROR: Nginx is not running"
    exit 1
fi

# Check if FastAPI is running
if ! pgrep -f "uvicorn" > /dev/null; then
    echo "ERROR: FastAPI is not running"
    exit 1
fi

# Check nginx HTTP response
if ! curl -f http://localhost:80/health > /dev/null 2>&1; then
    echo "ERROR: Nginx health check failed"
    exit 1
fi

# Check FastAPI HTTP response
if ! curl -f http://localhost:8000/api/v1/health > /dev/null 2>&1; then
    echo "ERROR: FastAPI health check failed"
    exit 1
fi

echo "All services healthy"
exit 0
