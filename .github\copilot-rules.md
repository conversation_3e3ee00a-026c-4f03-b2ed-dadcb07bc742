# AI Coding Agent - GitHub Copilot Development Rules

## ⚠️ IMPORTANT: This file contains legacy guidance and is being phased out
**For current architectural guidance, see: `docs/.copilot-rules.md`**

This file is maintained for historical reference and GitHub Copilot integration but should not be used for new development decisions.

## Project Context
This is a consolidated AI coding agent application featuring:
- **Consolidated App Container**: Single container running both Python/FastAPI backend and React/TypeScript frontend
- PostgreSQL database with pgvector extension (Supabase integration)
- Redis caching for sessions and rate limiting
- Nginx load balancer and reverse proxy
- Ultra-optimized resource usage and deployment

## 1. Container Naming & Architecture

### Container Naming Standards
- **Format**: `[project]-[service]-[environment]`
- **Examples**: `ai-coding-agent-app-prod`, `ai-coding-agent-postgres-dev`
- **Use lowercase**: with hyphens for separation
- **Max 63 characters**: for DNS compatibility
- **Be descriptive**: include service purpose clearly

### Consolidated Architecture Principle
- **Primary deployment**: Single consolidated app container (frontend + backend)
- **Essential services only**: app, database, cache, proxy
- **Resource optimization**: Minimal container footprint with maximum efficiency
- **Scalability**: Scale app replicas as needed, not individual services

## 2. Security Best Practices

### Non-Root Users
- Always create and switch to a non-root user inside Dockerfiles
- **Required pattern**:
```dockerfile
RUN useradd -m appuser
USER appuser
```
- Set proper file permissions for volumes
- Use `user: "1000:1000"` in docker-compose for consistency

### Secrets Management
- **Never** embed secrets in Docker images
- Use environment variables for configuration
- Store sensitive data in `.env` files (never commit)
- Use Docker secrets or external vaults for production
- Current project uses: JWT tokens, database passwords, API keys

### Resource Limits
```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.1'
      memory: 256M
```

## 3. Multi-Stage Builds & Optimization

### Dockerfile Standards
- Use multi-stage builds to separate build and runtime environments
- **Required pattern**:
```dockerfile
# Build stage
FROM node:20 AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Runtime stage
FROM node:20-slim
WORKDIR /app
COPY --from=build /app/node_modules ./node_modules
COPY . .
USER appuser
CMD ["npm", "start"]
```

### Image Optimization
- Use minimal base images (alpine, slim variants)
- Always include `.dockerignore` file
- Exclude: `.git/`, `node_modules/`, `*.log`, `.env`, `tests/`
- Enable BuildKit for faster builds with cache mounts

## 4. Health Checks & Monitoring

### Required Health Checks
Always define healthchecks for all services:

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 20s
```

### Service Dependencies
- Use `depends_on` with health conditions
- Implement proper startup ordering
- Handle graceful shutdowns

## 5. Multi-User Development

### Environment Isolation

- Separate `.env` files per environment
- User-specific data isolation (configured with `USER_DATA_ISOLATION: "true"`)
- Application-level user isolation (not container-per-user)
- Team collaboration features enabled

### Development Workflows

- Use `docker compose watch` for live reloads
- Bind mount source code for development
- Use volume mounts for persistent data
- Configure hot reloading for frontend

## 6. Data Persistence & Volumes

### Volume Management
```yaml
volumes:
  postgres-data:
    driver: local
  user-projects:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /host/path/user-projects
```

### Database Best Practices
- Always use named volumes for databases
- Configure proper backup strategies
- Use connection pooling
- Implement database migrations

## 7. Networking & Communication

### Network Configuration
- Use custom networks for service isolation
- Define explicit port mappings
- Configure proper CORS settings
- Use environment-specific URLs

### Service Communication
- Internal service communication via service names
- External API endpoints via environment variables
- Rate limiting configuration
- Proper SSL/TLS termination at nginx

## 8. Environment Management

### Docker Compose Structure
- Modern compose files start directly with services block (no version field)
- Separate development and production configurations
- Use environment-specific override files
- Current structure: `docker-compose.yml` + `docker-compose.dev.yml`

### Configuration Standards
```yaml
services:
  backend:
    build:
      context: ./backend
      target: development
    environment:
      - NODE_ENV=development
      - DEBUG=true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
    user: "appuser"
    restart: unless-stopped
```

## 9. AI Model Integration

### Ollama Configuration
- Configure proper model endpoints (`OLLAMA_HOST: http://host.docker.internal:11434`)
- Set appropriate model parameters (temperature, max_tokens)
- Use different models for different agents:
  - `CHAT_MODEL: llama3.2:3b`
  - `CODE_GENERATION_MODEL: deepseek-coder:6.7b-instruct`
  - `ARCHITECT_AGENT_MODEL: llama3.2:3b`

### Resource Allocation
- AI services need higher CPU/memory limits
- Configure GPU access if available
- Monitor model loading and inference times

## 10. Development Tools & Debugging

### Required Services
- Include pgAdmin for database management
- Redis monitoring tools
- Log aggregation and monitoring
- Debug ports for development (port 5678 for backend debugging)

### VS Code Integration
- Configure dev containers
- Include necessary extensions
- Proper Python/Node.js environment setup
- Git integration and workflows

## 11. Production Readiness

### Security Hardening
- Enable Docker Content Trust for signed images
- Regular vulnerability scanning
- Network policies and firewall rules
- Read-only filesystems where possible

### Monitoring & Logging
- Centralized logging (ELK stack or similar)
- Application performance monitoring
- Resource usage tracking
- Error reporting and alerting

## 12. Code Quality & Standards

### File Organization
- Clear separation of concerns
- Consistent directory structure
- Proper naming conventions for files and functions
- Documentation for all services

### Testing Strategy
- Unit tests in containers
- Integration testing across services
- Load testing for multi-user scenarios
- Database migration testing

## Implementation Notes

- This project already implements many best practices
- Focus on security improvements and monitoring
- Enhance multi-user isolation features
- Optimize AI model resource allocation
- Implement comprehensive testing strategies

## Quick Validation Checklist

When generating Docker configurations, ensure:
- ✅ Non-root user specified
- ✅ Health checks defined
- ✅ Resource limits set
- ✅ Secrets not embedded
- ✅ Multi-stage builds used
- ✅ .dockerignore included
- ✅ Proper networking configured
- ✅ Environment separation maintained
