apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-coding-agent-pgbouncer-k8s
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-pgbouncer-k8s
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-coding-agent-pgbouncer-k8s
  template:
    metadata:
      labels:
        app: ai-coding-agent-pgbouncer-k8s
    spec:
      containers:
        - name: pgbouncer
          image: edoburu/pgbouncer
          env:
            - name: DB_HOST
              value: ai-coding-agent-postgres-k8s
            - name: DB_PORT
              value: "5432"
            - name: DB_USER
              value: ai_agent
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: POSTGRES_PASSWORD
            - name: DB_NAME
              value: ai_coding_agent
            - name: POOL_MODE
              value: session
            - name: MAX_CLIENT_CONN
              value: "100"
            - name: DEFAULT_POOL_SIZE
              value: "20"
          ports:
            - containerPort: 6432
