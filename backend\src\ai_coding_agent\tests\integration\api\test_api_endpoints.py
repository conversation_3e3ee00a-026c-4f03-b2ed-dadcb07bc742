"""
Test script to verify the FastAPI AI endpoints are working.
"""

import asyncio
import aiohttp
import json
import sys
import os
import subprocess
import time
from typing import Optional

async def test_api_endpoints(base_url: str = "http://localhost:8000"):
    """Test the AI API endpoints."""

    async with aiohttp.ClientSession() as session:

        # Test health endpoint
        print("🔍 Testing health endpoint...")
        try:
            async with session.get(f"{base_url}/api/v1/ai/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Health check: {data.get('status', 'unknown')}")
                else:
                    print(f"❌ Health check failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False

        # Test get agents endpoint
        print("\n🔍 Testing agents endpoint...")
        try:
            async with session.get(f"{base_url}/api/v1/ai/agents") as response:
                if response.status == 200:
                    agents = await response.json()
                    print(f"✅ Found {len(agents)} agents:")
                    for agent in agents[:3]:  # Show first 3
                        print(f"  - {agent.get('role')}: {agent.get('description')[:50]}...")
                else:
                    print(f"❌ Agents endpoint failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Agents endpoint error: {e}")
            return False

        # Test models endpoint
        print("\n🔍 Testing models endpoint...")
        try:
            async with session.get(f"{base_url}/api/v1/ai/models") as response:
                if response.status == 200:
                    models = await response.json()
                    print(f"✅ Found {len(models)} models:")
                    for model in models[:3]:  # Show first 3
                        print(f"  - {model.get('name')}: {model.get('capabilities', [])}")
                else:
                    print(f"❌ Models endpoint failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Models endpoint error: {e}")
            return False

        # Test chat endpoint
        print("\n🔍 Testing chat endpoint...")
        try:
            chat_data = {
                "message": "Hello! Write a simple Python function that adds two numbers.",
                "agent_role": "backend"
            }

            async with session.post(
                f"{base_url}/api/v1/ai/chat",
                json=chat_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Chat response received:")
                    print(f"  Agent: {data.get('agent_role')}")
                    print(f"  Model: {data.get('model_used')}")
                    print(f"  Response length: {len(data.get('response', ''))}")
                    print(f"  First 100 chars: {data.get('response', '')[:100]}...")
                else:
                    print(f"❌ Chat endpoint failed: {response.status}")
                    text = await response.text()
                    print(f"Error response: {text}")
                    return False
        except Exception as e:
            print(f"❌ Chat endpoint error: {e}")
            return False

        # Test task execution endpoint
        print("\n🔍 Testing task execution endpoint...")
        try:
            task_data = {
                "task_description": "Create a Python function that calculates the factorial of a number",
                "preferred_agent": "backend"
            }

            async with session.post(
                f"{base_url}/api/v1/ai/tasks",
                json=task_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Task execution completed:")
                    print(f"  Success: {data.get('success')}")
                    print(f"  Agent: {data.get('agent_role')}")
                    print(f"  Execution time: {data.get('execution_time_ms')}ms")
                    if data.get('result'):
                        print(f"  Result length: {len(data.get('result', ''))}")
                        print(f"  First 100 chars: {data.get('result', '')[:100]}...")
                else:
                    print(f"❌ Task execution failed: {response.status}")
                    text = await response.text()
                    print(f"Error response: {text}")
                    return False
        except Exception as e:
            print(f"❌ Task execution error: {e}")
            return False

        print("\n🎉 All API endpoints are working correctly!")
        return True

def start_server() -> Optional[subprocess.Popen]:
    """Start the FastAPI server."""
    print("🚀 Starting FastAPI server...")

    try:
        # Start the server in the background
        process = subprocess.Popen(
            [sys.executable, "-m", "ai_coding_agent.main"],
            cwd=os.path.join(os.path.dirname(__file__), "src"),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Wait a bit for the server to start
        time.sleep(3)

        # Check if the process is still running
        if process.poll() is None:
            print("✅ FastAPI server started successfully")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Server failed to start:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None

    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

async def main():
    """Run the API test suite."""
    print("🚀 Starting FastAPI AI Integration Test\n")

    # Start the server
    server_process = start_server()

    if not server_process:
        print("❌ Cannot start server, exiting...")
        return False

    try:
        # Wait a bit more for the server to be fully ready
        print("⏳ Waiting for server to be ready...")
        time.sleep(5)

        # Test the endpoints
        success = await test_api_endpoints()

        return success

    finally:
        # Clean up the server process
        if server_process:
            print("\n🛑 Stopping server...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()
            print("✅ Server stopped")

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
