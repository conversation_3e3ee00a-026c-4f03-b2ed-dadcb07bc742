{"dashboard": {"id": null, "uid": null, "title": "AI Coding Agent Monitoring", "tags": ["scaling", "metrics", "ai-coding-agent"], "timezone": "browser", "schemaVersion": 38, "version": 1, "refresh": "30s", "panels": [{"type": "stat", "title": "Replica Count per Service", "datasource": "Prometheus", "targets": [{"expr": "kube_deployment_status_replicas_available{namespace=\"ai-coding-agent\"}", "legendFormat": "{{deployment}}", "refId": "A"}], "gridPos": {"x": 0, "y": 0, "w": 8, "h": 4}}, {"type": "graph", "title": "CPU/Memory Utilization Trends", "datasource": "Prometheus", "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"ai-coding-agent\"}[5m])) by (pod)", "legendFormat": "CPU {{pod}}", "refId": "A"}, {"expr": "sum(container_memory_usage_bytes{namespace=\"ai-coding-agent\"}) by (pod)", "legendFormat": "Memory {{pod}}", "refId": "B"}], "gridPos": {"x": 8, "y": 0, "w": 16, "h": 8}}, {"type": "graph", "title": "Request Rate & Response Time", "datasource": "Prometheus", "targets": [{"expr": "sum(rate(http_requests_total{namespace=\"ai-coding-agent\"}[5m])) by (service)", "legendFormat": "Requests {{service}}", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{namespace=\"ai-coding-agent\"}[5m])) by (le, service))", "legendFormat": "95th RT {{service}}", "refId": "B"}], "gridPos": {"x": 0, "y": 8, "w": 16, "h": 8}}, {"type": "stat", "title": "Database Connection Pool Status", "datasource": "Prometheus", "targets": [{"expr": "pg_stat_activity_count{namespace=\"ai-coding-agent\"}", "legendFormat": "Active Connections", "refId": "A"}], "gridPos": {"x": 16, "y": 8, "w": 8, "h": 4}}, {"type": "stat", "title": "PgBouncer Connection Statistics", "datasource": "Prometheus", "targets": [{"expr": "pgbouncer_pools_cl_active{namespace=\"ai-coding-agent\"}", "legendFormat": "Active Clients", "refId": "A"}, {"expr": "pgbouncer_pools_cl_waiting{namespace=\"ai-coding-agent\"}", "legendFormat": "Waiting Clients", "refId": "B"}], "gridPos": {"x": 16, "y": 12, "w": 8, "h": 4}}, {"type": "graph", "title": "Auto-scaling Events Timeline", "datasource": "Prometheus", "targets": [{"expr": "kube_hpa_status_current_replicas{namespace=\"ai-coding-agent\"}", "legendFormat": "HPA Replicas {{deployment}}", "refId": "A"}], "gridPos": {"x": 0, "y": 16, "w": 24, "h": 6}}], "annotations": {"list": [{"name": "Scaling Events", "datasource": "Prometheus", "enable": true, "expr": "increase(kube_hpa_status_conditions{namespace=\"ai-coding-agent\",condition=\"ScalingActive\"}[1h]) > 0", "iconColor": "#FA6400"}]}, "alert": {"conditions": [{"type": "query", "query": "sum(rate(container_cpu_usage_seconds_total{namespace=\"ai-coding-agent\"}[5m])) by (pod) > 0.9", "evaluator": ">", "threshold": 0.9, "operator": "and", "alert": "High CPU Utilization"}, {"type": "query", "query": "pgbouncer_pools_cl_waiting{namespace=\"ai-coding-agent\"} > 0", "evaluator": ">", "threshold": 0, "operator": "and", "alert": "PgBouncer Pool Exhaustion"}, {"type": "query", "query": "kube_deployment_status_replicas_unavailable{namespace=\"ai-coding-agent\"} > 0", "evaluator": ">", "threshold": 0, "operator": "and", "alert": "Service Failure"}]}}}