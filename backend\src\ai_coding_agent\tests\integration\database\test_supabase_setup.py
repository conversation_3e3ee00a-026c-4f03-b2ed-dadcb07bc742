#!/usr/bin/env python3
"""
Test Supabase connection and configuration.
This script verifies that Supabase is properly configured and accessible.
"""

import sys
import os
from typing import Dict, Any

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_supabase_config():
    """Test Supabase configuration from environment."""
    print("🧪 Testing Supabase Configuration...")

    # Test environment variables
    from ai_coding_agent.config import settings

    print(f"📍 Supabase URL: {settings.supabase.url}")
    print(f"🔑 Has Anon Key: {'Yes' if settings.supabase.anon_key else 'No'}")
    print(f"🔐 Has Service Key: {'Yes' if settings.supabase.service_role_key else 'No'}")

    if not settings.supabase.url:
        print("❌ SUPABASE_URL is not set!")
        return False

    if not settings.supabase.anon_key:
        print("❌ SUPABASE_ANON_KEY is not set!")
        return False

    print("✅ Supabase configuration looks good!")
    return True


def test_supabase_connection():
    """Test actual connection to Supabase."""
    print("\n🌐 Testing Supabase Connection...")

    try:
        # Try to import supabase
        import supabase
        print("✅ supabase-py package is installed")
    except ImportError:
        print("❌ supabase-py package is not installed")
        print("   Install with: pip install supabase")
        return False

    try:
        from src.ai_coding_agent.services.supabase import get_supabase_service

        # Get service and test connection
        service = get_supabase_service()
        status = service.get_connection_status()

        print(f"🔗 Connected: {status['connected']}")
        print(f"📊 Has Data: {status['has_data']}")

        if status['error']:
            print(f"⚠️  Error: {status['error']}")
            return False

        print("✅ Supabase connection successful!")
        return True

    except Exception as e:
        print(f"❌ Failed to connect to Supabase: {e}")
        return False


def test_hybrid_database_config():
    """Test hybrid database configuration."""
    print("\n🔄 Testing Hybrid Database Configuration...")

    from ai_coding_agent.config import settings

    print(f"🗄️  Database Mode: {settings.hybrid_db.mode}")
    print(f"📂 SQLite URL: {settings.hybrid_db.sqlite_url}")
    print(f"🏠 Local Tables: {settings.hybrid_db.local_table_list}")
    print(f"☁️  Supabase Tables: {settings.hybrid_db.supabase_table_list}")

    print("✅ Hybrid database configuration loaded!")
    return True


def install_supabase():
    """Install supabase-py package."""
    print("\n📦 Installing supabase-py package...")

    import subprocess
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "supabase"
        ], check=True, capture_output=True, text=True)

        print("✅ supabase-py installed successfully!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install supabase-py: {e}")
        print(f"Error output: {e.stderr}")
        return False


def main():
    """Main test function."""
    print("🚀 AI Coding Agent - Supabase Setup Test")
    print("=" * 50)

    # Test 1: Configuration
    config_ok = test_supabase_config()

    # Test 2: Hybrid database config
    hybrid_ok = test_hybrid_database_config()

    # Test 3: Connection (install package if needed)
    connection_ok = test_supabase_connection()

    if not connection_ok:
        print("\n📦 Attempting to install supabase-py...")
        if install_supabase():
            print("🔄 Retesting connection...")
            connection_ok = test_supabase_connection()

    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Configuration: {'✅' if config_ok else '❌'}")
    print(f"   Hybrid Setup:  {'✅' if hybrid_ok else '❌'}")
    print(f"   Connection:    {'✅' if connection_ok else '❌'}")

    if config_ok and hybrid_ok and connection_ok:
        print("\n🎉 SUCCESS: Supabase is properly configured and connected!")
        print("\n🚀 Next Steps:")
        print("   1. Create tables in Supabase dashboard or run migrations")
        print("   2. Test the hybrid database system")
        print("   3. Start implementing the knowledge base features")
        return True
    else:
        print("\n❌ ISSUES FOUND: Please fix the above issues before proceeding.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
