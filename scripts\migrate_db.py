"""
Database migration script for AI Coding Agent.

This script creates the initial database tables and can be run
to set up the database schema.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.src.ai_coding_agent import models
from backend.src.ai_coding_agent.utils import logging

# Configure logging
logging.configure_logging()
logger = logging.get_logger(__name__)


def main():
    """Run database migrations."""
    try:
        logger.info("Starting database migration...")
        models.create_tables()
        logger.info("Database migration completed successfully!")

    except Exception as e:
        logger.error("Database migration failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
