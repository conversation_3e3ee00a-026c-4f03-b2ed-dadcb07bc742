"""
Roadmap API Routes
Implements REST endpoints for roadmap management (Phase B1).
"""

from typing import Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ai_coding_agent.models import (
    get_db,
    ProjectCreate, ProjectUpdate, ProjectResponse,
    RoadmapCreate, RoadmapUpdate, RoadmapResponse,
    TaskStatus,
    AuditEntityType, AuditLogFilter, AuditLogResponse,
    StatusHistoryResponse, ConcurrencyControlResponse,
    RoadmapVersionCreate, RoadmapVersionResponse, VersionComparison,
)
from ai_coding_agent.services.roadmap import RoadmapService
from ai_coding_agent.services.audit_service import get_audit_service
from ai_coding_agent.services.versioning import VersioningService
from ai_coding_agent.services.schema_validation import schema_validator
from ai_coding_agent.services.supabase_auth import get_current_user

router = APIRouter(prefix="/api/v1", tags=["roadmap"])


@router.post("/projects", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new project with optional roadmap.

    - **project_data**: Project information including optional roadmap
    - **Returns**: Created project with full details
    """
    service = RoadmapService(db)
    return service.create_project(project_data)


@router.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get project by ID with full roadmap hierarchy.

    - **project_id**: UUID of the project
    - **Returns**: Project with full roadmap data
    """
    service = RoadmapService(db)
    return service.get_project(project_id)


@router.put("/projects/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: str,
    update_data: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Update project information.

    - **project_id**: UUID of the project
    - **update_data**: Fields to update
    - **Returns**: Updated project
    """
    service = RoadmapService(db)
    return service.update_project(project_id, update_data)


@router.delete("/projects/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Delete project and all associated data.

    - **project_id**: UUID of the project
    """
    service = RoadmapService(db)
    service.delete_project(project_id)


@router.post("/projects/{project_id}/roadmap", response_model=RoadmapResponse, status_code=status.HTTP_201_CREATED)
async def create_roadmap(
    project_id: str,
    roadmap_data: RoadmapCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a roadmap for an existing project.

    - **project_id**: UUID of the project
    - **roadmap_data**: Roadmap with phases, steps, and tasks
    - **Returns**: Created roadmap with full hierarchy
    """
    service = RoadmapService(db)
    return service.create_roadmap(project_id, roadmap_data)


@router.get("/roadmaps/{roadmap_id}", response_model=RoadmapResponse)
async def get_roadmap(
    roadmap_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get roadmap by ID with full hierarchy.

    - **roadmap_id**: UUID of the roadmap
    - **Returns**: Roadmap with all phases, steps, and tasks
    """
    service = RoadmapService(db)
    return service.get_roadmap(roadmap_id)


@router.get("/projects/{project_id}/roadmap", response_model=RoadmapResponse)
async def get_project_roadmap(
    project_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get roadmap for a specific project.

    - **project_id**: UUID of the project
    - **Returns**: Project's roadmap with full hierarchy
    """
    service = RoadmapService(db)
    return service.get_project_roadmap(project_id)


@router.put("/roadmaps/{roadmap_id}", response_model=RoadmapResponse)
async def update_roadmap(
    roadmap_id: str,
    update_data: RoadmapUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Update roadmap information.

    - **roadmap_id**: UUID of the roadmap
    - **update_data**: Fields to update
    - **Returns**: Updated roadmap
    """
    service = RoadmapService(db)
    return service.update_roadmap(roadmap_id, update_data)


@router.post("/roadmap", response_model=RoadmapResponse, status_code=status.HTTP_201_CREATED)
async def create_standalone_roadmap(
    roadmap_data: RoadmapCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a standalone roadmap (auto-generates a project).

    - **roadmap_data**: Roadmap with phases, steps, and tasks
    - **Returns**: Created roadmap with full hierarchy and auto-generated project
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    service = RoadmapService(db, user_id=user_id, user_email=user_email)
    return service.create_standalone_roadmap(roadmap_data)


@router.delete("/roadmap/{roadmap_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_roadmap(
    roadmap_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Delete roadmap and optionally its auto-generated project.

    - **roadmap_id**: UUID of the roadmap
    - **Note**: If the roadmap's project was auto-generated, it will also be deleted
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    service = RoadmapService(db, user_id=user_id, user_email=user_email)
    service.delete_roadmap(roadmap_id)


@router.patch("/tasks/{task_id}/status")
async def update_task_status(
    task_id: str,
    status: TaskStatus,
    artifacts: Optional[List[Dict]] = None,
    error_message: Optional[str] = None,
    reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Update task status and propagate changes up the hierarchy with audit logging.

    - **task_id**: UUID of the task
    - **status**: New task status
    - **artifacts**: Optional list of artifacts produced
    - **error_message**: Optional error message if task failed
    - **reason**: Optional reason for the status change (for audit trail)
    - **Returns**: Updated task with propagated status changes
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    service = RoadmapService(db, user_id=user_id, user_email=user_email)
    return service.update_task_status(task_id, status, artifacts, error_message, reason)


@router.get("/tasks/{task_id}/status")
async def get_task_status(
    task_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get detailed task status information with hierarchy context.

    - **task_id**: UUID of the task
    - **Returns**: Task status with step, phase, and roadmap context
    """
    service = RoadmapService(db)
    return service.get_task_status(task_id)


@router.get("/projects/{project_id}/progress")
async def get_project_progress(
    project_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get comprehensive project progress metrics.

    - **project_id**: UUID of the project
    - **Returns**: Detailed progress metrics and statistics
    """
    service = RoadmapService(db)
    return service.get_project_progress(project_id)


# Utility endpoints for roadmap management

@router.get("/projects/{project_id}/tasks/by-agent/{agent_type}")
async def get_tasks_by_agent(
    project_id: str,
    agent_type: str,
    status: Optional[TaskStatus] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get all tasks assigned to a specific agent in a project.

    - **project_id**: UUID of the project
    - **agent_type**: Agent type (architect, frontend, backend, shell, issue_fix, test)
    - **status**: Optional status filter
    - **Returns**: List of tasks assigned to the agent
    """
    service = RoadmapService(db)
    project = service.get_project(project_id)

    if not project.roadmap:
        return []

    tasks = []
    for phase in project.roadmap.phases:
        for step in phase.steps:
            for task in step.tasks:
                if task.assigned_agent == agent_type:
                    if status is None or task.status == status:
                        tasks.append({
                            **task.model_dump(),
                            "phase_name": phase.name,
                            "step_name": step.name,
                        })

    return tasks


@router.get("/projects/{project_id}/phases/{phase_id}/next-tasks")
async def get_next_available_tasks(
    project_id: str,
    phase_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get next available tasks in a phase (tasks with dependencies satisfied).

    - **project_id**: UUID of the project
    - **phase_id**: UUID of the phase
    - **Returns**: List of tasks ready to be executed
    """
    service = RoadmapService(db)
    project = service.get_project(project_id)

    if not project.roadmap:
        return []

    # Find the phase
    target_phase = None
    for phase in project.roadmap.phases:
        if phase.id == phase_id:
            target_phase = phase
            break

    if not target_phase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Phase {phase_id} not found"
        )

    # Get all tasks in this phase and check dependencies
    available_tasks = []
    all_tasks = {}

    # Build task lookup map
    for phase in project.roadmap.phases:
        for step in phase.steps:
            for task in step.tasks:
                all_tasks[task.id] = task

    # Check each task in target phase
    for step in target_phase.steps:
        for task in step.tasks:
            if task.status == TaskStatus.PENDING:
                # Check if all dependencies are satisfied
                dependencies_satisfied = True
                for dep_id in task.dependencies:
                    if dep_id in all_tasks:
                        dep_task = all_tasks[dep_id]
                        if dep_task.status != TaskStatus.COMPLETED:
                            dependencies_satisfied = False
                            break

                if dependencies_satisfied:
                    available_tasks.append({
                        **task.model_dump(),
                        "step_name": step.name,
                        "phase_name": target_phase.name,
                    })

    return available_tasks


# Audit Trail Endpoints

@router.get("/audit/logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    entity_type: Optional[AuditEntityType] = None,
    entity_id: Optional[str] = None,
    user_id: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get audit logs with filtering options.

    - **entity_type**: Filter by entity type (project, roadmap, phase, step, task)
    - **entity_id**: Filter by specific entity ID
    - **user_id**: Filter by user who made changes
    - **limit**: Maximum number of results (max 1000)
    - **offset**: Number of results to skip
    - **Returns**: List of audit log entries
    """
    audit_service = get_audit_service(db)
    filters = AuditLogFilter(
        entity_type=entity_type,
        entity_id=entity_id,
        user_id=user_id,
        limit=min(limit, 1000),
        offset=offset
    )
    return audit_service.get_audit_logs(filters)


@router.get("/audit/{entity_type}/{entity_id}/trail", response_model=List[AuditLogResponse])
async def get_entity_audit_trail(
    entity_type: AuditEntityType,
    entity_id: str,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get complete audit trail for a specific entity.

    - **entity_type**: Type of entity (project, roadmap, phase, step, task)
    - **entity_id**: UUID of the entity
    - **limit**: Maximum number of results
    - **Returns**: Complete audit trail for the entity
    """
    audit_service = get_audit_service(db)
    return audit_service.get_entity_audit_trail(entity_type, entity_id, limit)


@router.get("/audit/{entity_type}/{entity_id}/status-history", response_model=List[StatusHistoryResponse])
async def get_status_history(
    entity_type: AuditEntityType,
    entity_id: str,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get status change history for an entity.

    - **entity_type**: Type of entity (project, roadmap, phase, step, task)
    - **entity_id**: UUID of the entity
    - **limit**: Maximum number of results
    - **Returns**: Status change history with duration tracking
    """
    audit_service = get_audit_service(db)
    return audit_service.get_status_history(entity_type, entity_id, limit)


@router.get("/concurrency/{entity_type}/{entity_id}", response_model=ConcurrencyControlResponse)
async def get_concurrency_control(
    entity_type: AuditEntityType,
    entity_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get concurrency control information for an entity.

    - **entity_type**: Type of entity (project, roadmap, phase, step, task)
    - **entity_id**: UUID of the entity
    - **Returns**: Concurrency control information including version and lock status
    """
    audit_service = get_audit_service(db)
    return audit_service.get_or_create_concurrency_control(entity_type, entity_id)


@router.post("/concurrency/{entity_type}/{entity_id}/lock")
async def acquire_lock(
    entity_type: AuditEntityType,
    entity_id: str,
    lock_duration_minutes: int = 30,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Acquire a lock on an entity for editing.

    - **entity_type**: Type of entity (project, roadmap, phase, step, task)
    - **entity_id**: UUID of the entity
    - **lock_duration_minutes**: How long to hold the lock (default 30 minutes)
    - **Returns**: Success status and lock information
    """
    audit_service = get_audit_service(db)
    user_id = getattr(current_user, 'id', str(current_user))

    success, control = audit_service.acquire_lock(
        entity_type, entity_id, user_id, lock_duration_minutes
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Entity is locked by another user until {control.lock_expires_at}"
        )

    return {"success": True, "control": control}


@router.delete("/concurrency/{entity_type}/{entity_id}/lock")
async def release_lock(
    entity_type: AuditEntityType,
    entity_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Release a lock on an entity.

    - **entity_type**: Type of entity (project, roadmap, phase, step, task)
    - **entity_id**: UUID of the entity
    - **Returns**: Updated concurrency control information
    """
    audit_service = get_audit_service(db)
    user_id = getattr(current_user, 'id', str(current_user))

    return audit_service.release_lock(entity_type, entity_id, user_id)


@router.get("/audit/{entity_type}/{entity_id}/analytics")
async def get_status_analytics(
    entity_type: AuditEntityType,
    entity_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get comprehensive status analytics for an entity.

    - **entity_type**: Type of entity (project, roadmap, phase, step, task)
    - **entity_id**: UUID of the entity
    - **Returns**: Detailed analytics including status distribution, timing, and trends
    """
    audit_service = get_audit_service(db)
    return audit_service.get_status_analytics(entity_type, entity_id)


# Roadmap Versioning Endpoints

@router.post("/roadmaps/{roadmap_id}/versions", response_model=RoadmapVersionResponse, status_code=status.HTTP_201_CREATED)
async def create_roadmap_version(
    roadmap_id: str,
    version_data: RoadmapVersionCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new version of a roadmap.

    - **roadmap_id**: UUID of the roadmap
    - **version_data**: Version creation data including type and change details
    - **Returns**: Created version with snapshot and metadata
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    versioning_service = VersioningService(db, user_id=user_id, user_email=user_email)
    return versioning_service.create_version(roadmap_id, version_data)


@router.get("/roadmaps/{roadmap_id}/versions", response_model=List[RoadmapVersionResponse])
async def get_roadmap_versions(
    roadmap_id: str,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get all versions for a roadmap.

    - **roadmap_id**: UUID of the roadmap
    - **limit**: Maximum number of versions to return
    - **Returns**: List of roadmap versions ordered by creation date
    """
    versioning_service = VersioningService(db)
    return versioning_service.get_versions(roadmap_id, limit)


@router.get("/versions/{version_id}", response_model=RoadmapVersionResponse)
async def get_roadmap_version(
    version_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get a specific roadmap version by ID.

    - **version_id**: UUID of the version
    - **Returns**: Version details with complete snapshot
    """
    versioning_service = VersioningService(db)
    return versioning_service.get_version(version_id)


@router.patch("/versions/{version_id}/release")
async def release_roadmap_version(
    version_id: str,
    release_notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Mark a roadmap version as released.

    - **version_id**: UUID of the version
    - **release_notes**: Optional release notes
    - **Returns**: Updated version with release information
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    versioning_service = VersioningService(db, user_id=user_id, user_email=user_email)
    return versioning_service.release_version(version_id, release_notes)


@router.get("/versions/{from_version_id}/compare/{to_version_id}", response_model=VersionComparison)
async def compare_roadmap_versions(
    from_version_id: str,
    to_version_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Compare two roadmap versions and return differences.

    - **from_version_id**: UUID of the source version
    - **to_version_id**: UUID of the target version
    - **Returns**: Detailed comparison showing changes, additions, and removals
    """
    versioning_service = VersioningService(db)
    return versioning_service.compare_versions(from_version_id, to_version_id)


# Enhanced Management Endpoints

@router.get("/projects/{project_id}/analytics")
async def get_project_analytics(
    project_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get comprehensive analytics for a project including all entities.

    - **project_id**: UUID of the project
    - **Returns**: Complete project analytics with status, timing, and progress data
    """
    service = RoadmapService(db)
    project = service.get_project(project_id)

    if not project.roadmap:
        return {"error": "Project has no roadmap"}

    audit_service = get_audit_service(db)

    # Get analytics for all entities in the project
    analytics = {
        "project_id": project_id,
        "project_name": project.name,
        "roadmap_analytics": audit_service.get_status_analytics(
            AuditEntityType.ROADMAP, project.roadmap.id
        ),
        "phases": [],
        "summary": {
            "total_phases": len(project.roadmap.phases),
            "total_steps": 0,
            "total_tasks": 0,
            "completed_tasks": 0,
            "in_progress_tasks": 0
        }
    }

    for phase in project.roadmap.phases:
        phase_analytics = {
            "phase_id": phase.id,
            "phase_name": phase.name,
            "analytics": audit_service.get_status_analytics(
                AuditEntityType.PHASE, phase.id
            ),
            "steps": []
        }

        analytics["summary"]["total_steps"] += len(phase.steps)

        for step in phase.steps:
            step_analytics = {
                "step_id": step.id,
                "step_name": step.name,
                "analytics": audit_service.get_status_analytics(
                    AuditEntityType.STEP, step.id
                ),
                "tasks": []
            }

            analytics["summary"]["total_tasks"] += len(step.tasks)

            for task in step.tasks:
                task_analytics = audit_service.get_status_analytics(
                    AuditEntityType.TASK, task.id
                )
                step_analytics["tasks"].append({
                    "task_id": task.id,
                    "task_name": task.name,
                    "analytics": task_analytics
                })

                if task.status == "completed":
                    analytics["summary"]["completed_tasks"] += 1
                elif task.status == "in_progress":
                    analytics["summary"]["in_progress_tasks"] += 1

            phase_analytics["steps"].append(step_analytics)

        analytics["phases"].append(phase_analytics)

    return analytics


@router.post("/bulk-operations/status-update")
async def bulk_status_update(
    updates: List[Dict],
    reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Update status for multiple tasks in a single operation.

    - **updates**: List of updates with task_id and new status
    - **reason**: Optional reason for the bulk update
    - **Returns**: Results of all updates with success/failure status
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    service = RoadmapService(db, user_id=user_id, user_email=user_email)

    results = []

    for update in updates:
        try:
            task_id = update.get("task_id")
            new_status = update.get("status")

            if not task_id or not new_status:
                results.append({
                    "task_id": task_id,
                    "success": False,
                    "error": "Missing task_id or status"
                })
                continue

            updated_task = service.update_task_status(
                task_id=task_id,
                status=new_status,
                reason=f"Bulk update: {reason}" if reason else "Bulk status update"
            )

            results.append({
                "task_id": task_id,
                "success": True,
                "new_status": updated_task.status
            })

        except Exception as e:
            results.append({
                "task_id": update.get("task_id"),
                "success": False,
                "error": str(e)
            })

    return {
        "total_updates": len(updates),
        "successful_updates": len([r for r in results if r["success"]]),
        "failed_updates": len([r for r in results if not r["success"]]),
        "results": results
    }


# Schema Validation Endpoints

@router.post("/validate/roadmap")
async def validate_roadmap_schema(
    roadmap_data: Dict,
    current_user = Depends(get_current_user)
):
    """
    Validate roadmap data against the roadmap.json schema.

    - **roadmap_data**: Complete roadmap data to validate
    - **Returns**: Validation results with any errors found
    """
    is_valid, errors = schema_validator.validate_roadmap(roadmap_data)

    return {
        "valid": is_valid,
        "errors": errors,
        "schema_version": "1.0.0"
    }


@router.get("/schema/roadmap/info")
async def get_roadmap_schema_info(
    current_user = Depends(get_current_user)
):
    """
    Get information about the roadmap JSON schema.

    - **Returns**: Schema metadata and structure information
    """
    return schema_validator.get_schema_info("roadmap")


@router.get("/schema/list")
async def list_available_schemas(
    current_user = Depends(get_current_user)
):
    """
    List all available JSON schemas.

    - **Returns**: List of available schema names
    """
    return {
        "schemas": schema_validator.list_available_schemas()
    }


# Dependency Engine Endpoints (Phase B2)

@router.get("/tasks/{task_id}/dependencies")
async def check_task_dependencies(
    task_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Check task dependencies and determine if task can start.

    - **task_id**: UUID of the task
    - **Returns**: Dependency check result with blocking dependencies and warnings
    """
    service = RoadmapService(db)
    return service.check_task_dependencies(task_id)


@router.get("/steps/{step_id}/dependencies")
async def check_step_dependencies(
    step_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Check step dependencies and determine if step can start.

    - **step_id**: UUID of the step
    - **Returns**: Dependency check result with blocking dependencies and warnings
    """
    service = RoadmapService(db)
    return service.check_step_dependencies(step_id)


@router.get("/phases/{phase_id}/dependencies")
async def check_phase_dependencies(
    phase_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Check phase dependencies and determine if phase can start.

    - **phase_id**: UUID of the phase
    - **Returns**: Dependency check result with blocking dependencies and warnings
    """
    service = RoadmapService(db)
    return service.check_phase_dependencies(phase_id)


@router.get("/phases/{phase_id}/progression")
async def check_phase_progression(
    phase_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Check phase progression status and completion.

    - **phase_id**: UUID of the phase
    - **Returns**: Phase progression result with completion statistics
    """
    service = RoadmapService(db)
    return service.get_phase_progression_status(phase_id)


@router.post("/tasks/{task_id}/start")
async def start_task_with_validation(
    task_id: str,
    force_override: bool = False,
    override_reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Start a task with dependency validation.

    - **task_id**: UUID of the task
    - **force_override**: Whether to force start despite dependencies
    - **override_reason**: Reason for override if force_override is True
    - **Returns**: Updated task with validation results
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    service = RoadmapService(db, user_id=user_id, user_email=user_email)
    return service.start_task(task_id, force_override, override_reason)


@router.post("/tasks/{task_id}/complete")
async def complete_task_with_validation(
    task_id: str,
    artifacts: Optional[List[Dict]] = None,
    force_override: bool = False,
    override_reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Complete a task with dependency validation.

    - **task_id**: UUID of the task
    - **artifacts**: Optional list of artifacts produced
    - **force_override**: Whether to force completion despite dependencies
    - **override_reason**: Reason for override if force_override is True
    - **Returns**: Updated task with validation results
    """
    user_id = getattr(current_user, 'id', str(current_user))
    user_email = getattr(current_user, 'email', None)
    service = RoadmapService(db, user_id=user_id, user_email=user_email)
    return service.complete_task(task_id, artifacts, force_override, override_reason)


@router.get("/validate-execution-order/{entity_type}/{entity_id}")
async def validate_execution_order(
    entity_type: str,
    entity_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Validate execution order for an entity (task, step, or phase).

    - **entity_type**: Type of entity (task, step, phase)
    - **entity_id**: UUID of the entity
    - **Returns**: Validation result with warnings
    """
    service = RoadmapService(db)
    return service.validate_execution_order(entity_id, entity_type)


@router.get("/roadmaps/{roadmap_id}/real-time-status")
async def get_real_time_status(
    roadmap_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get real-time status summary for a roadmap.

    - **roadmap_id**: UUID of the roadmap
    - **Returns**: Real-time status summary with progress and task details
    """
    user_id = getattr(current_user, 'id', str(current_user))
    service = RoadmapService(db, user_id=user_id)
    return service.dependency_engine.get_real_time_status_summary(roadmap_id)


@router.get("/status-impact/{entity_type}/{entity_id}")
async def get_status_change_impact(
    entity_type: str,
    entity_id: str,
    new_status: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Analyze the potential impact of a status change.

    - **entity_type**: Type of entity (task, step, phase)
    - **entity_id**: UUID of the entity
    - **new_status**: The new status to analyze
    - **Returns**: Impact analysis with affected entities and automatic progressions
    """
    from ai_coding_agent.models import DependencyType

    # Convert string to enum
    if entity_type.lower() == "task":
        dep_type = DependencyType.TASK
    elif entity_type.lower() == "step":
        dep_type = DependencyType.STEP
    elif entity_type.lower() == "phase":
        dep_type = DependencyType.PHASE
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid entity type: {entity_type}"
        )

    user_id = getattr(current_user, 'id', str(current_user))
    service = RoadmapService(db, user_id=user_id)
    return service.dependency_engine.get_status_propagation_impact(entity_id, dep_type, new_status)
