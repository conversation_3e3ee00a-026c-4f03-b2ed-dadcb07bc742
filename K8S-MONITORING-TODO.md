# Kubernetes Monitoring Stack TODO

## 1. Prometheus Operator / kube-prometheus-stack Deployment
- [ ] Install Helm (if not installed)
- [ ] Add Prometheus Helm repo
- [ ] Install kube-prometheus-stack via Helm
- [ ] Confirm Prometheus, Grafana, AlertManager, and CRDs are deployed

## 2. ServiceMonitor CRDs
- [ ] Apply ServiceMonitor manifests for backend and frontend
- [ ] Confirm Prometheus scrapes custom application metrics

## 3. Grafana Dashboards
- [ ] Access Grafana (default admin password: admin)
- [ ] Find Grafana service (NodePort/LoadBalancer)
- [ ] Open Grafana in browser
- [ ] Add Prometheus as a data source
- [ ] Import built-in/community dashboards for Kubernetes, HPA, FastAPI, PostgreSQL, Redis
- [ ] Create custom panels for:
    - [ ] HPA scaling events (`kube_hpa_status_current_replicas`, `kube_hpa_status_desired_replicas`)
    - [ ] Application metrics (request rate, response time, active sessions)
    - [ ] Resource utilization (CPU, memory, pod count)
    - [ ] Database and Redis metrics (via exporters)

## 4. AlertManager Setup
- [ ] Configure alert rules in Prometheus for:
    - [ ] High error rate
    - [ ] High response time
    - [ ] HPA scaling events
    - [ ] Resource exhaustion
- [ ] Add alert rules via ConfigMap or UI

## 5. Exporters for Database and Redis
- [ ] Deploy prometheus-postgres-exporter in your namespace
- [ ] Add ServiceMonitor for PostgreSQL exporter
- [ ] Deploy oliver006/redis_exporter in your namespace
- [ ] Add ServiceMonitor for Redis exporter

## 6. Example Grafana Panel Queries
- [ ] HPA Scaling: `kube_hpa_status_current_replicas`, `kube_hpa_status_desired_replicas`
- [ ] Request Rate: `rate(http_requests_total[1m])`
- [ ] Response Time: `histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))`
- [ ] Resource Utilization: `node_cpu_seconds_total`, `node_memory_MemAvailable_bytes`
- [ ] DB/Redis: Use metrics from exporters

## 7. Custom AI Coding Agent Dashboard Import
- [ ] Import the Grafana dashboard JSON file:
      - Open Grafana web UI (http://localhost:3000)
      - Log in as admin
      - Go to “+” (Create) > “Import”
      - Upload `infrastructure/monitoring/grafana_dashboard_ai_coding_agent.json`
      - Select Prometheus as the data source
      - Click “Import”
- [ ] Verify all panels and alerts are working
- [ ] Customize panels/alerts as needed for your environment
- [ ] Document any changes to the dashboard for version control

## 8. Apply Everything
- [ ] Use `deploy.sh` script to apply all manifests in order
- [ ] Validate with `test-hpa.sh` and monitor with Grafana

## 9. Performance Testing for Scaling
- [ ] Create load testing scripts (Locust, Apache Bench, wrk)
- [ ] Test scenarios:
      - Gradual load increase to trigger scale-up
      - Sudden load spikes
      - Database connection pooling under load
      - Multi-user concurrent sessions
      - Resource cleanup during scale-down
- [ ] Measure:
      - Response time percentiles during scaling
      - Database connection pool efficiency
      - Memory usage patterns
      - Error rates during scaling events
- [ ] Automate testing in CI/CD pipeline

## 10. Configuration Management for Scaling
- [ ] Create environment-specific configuration files (dev, staging, prod)
- [ ] Set replica counts and auto-scaling rules per environment
- [ ] Use environment variables/config files for:
      - Scaling thresholds and limits
      - PgBouncer pool sizes
      - Monitoring intervals
      - Resource allocations
- [ ] Document scaling configuration in README
- [ ] Add validation logic for scaling parameters

---
If you want ready-to-import Grafana dashboard JSONs, exporter manifests, or alert rule examples, specify which area to start with and they will be generated for you!
