"""
AI Agent Orchestrator - Enhanced dispatch and routing system.

This module provides intelligent dispatching of tasks to specialized AI agents
with model routing, fallback handling, quality assessment, load balancing,
and adaptive model selection.
"""

import json
import logging
import asyncio
import re
import statistics
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict, field
from datetime import datetime, timedelta
from collections import defaultdict, deque

import httpx
from pydantic import BaseModel, Field, ConfigDict

from ai_coding_agent.config import settings
from ai_coding_agent.services.universal_llm import get_universal_llm, LLMRequest

logger = logging.getLogger(__name__)


class TaskComplexity(str, Enum):
    """Task complexity levels for model routing."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    EXPERT = "expert"


class TaskType(str, Enum):
    """Types of tasks that can be dispatched."""
    # General
    PLANNING = "planning"
    COORDINATION = "coordination"
    EXPLANATION = "explanation"

    # Code-related
    CODE_COMPLETION = "code_completion"
    CODE_GENERATION = "code_generation"
    CODE_ANALYSIS = "code_analysis"
    CODE_REVIEW = "code_review"
    REFACTORING = "refactoring"

    # Architecture
    SYSTEM_DESIGN = "system_design"
    ARCHITECTURE = "architecture"
    API_DESIGN = "api_design"

    # Frontend
    UI_COMPONENT = "ui_component"
    STYLING = "styling"
    RESPONSIVE_DESIGN = "responsive_design"

    # Backend
    BUSINESS_LOGIC = "business_logic"
    DATABASE_DESIGN = "database_design"
    AUTHENTICATION = "authentication"

    # System
    DEPLOYMENT = "deployment"
    SCRIPTING = "scripting"
    SYSTEM_COMMANDS = "system_commands"

    # Testing & Debugging
    DEBUGGING = "debugging"
    ERROR_ANALYSIS = "error_analysis"
    OPTIMIZATION = "optimization"
    UNIT_TESTING = "unit_testing"


class AgentOutput(BaseModel):
    """Standardized output format for all AI agents."""

    agent_name: str = Field(description="Name of the agent that generated this output")
    task_id: str = Field(description="Unique identifier for the task")
    task_type: TaskType = Field(description="Type of task performed")
    status: str = Field(description="Status: success, error, partial, requires_input")

    # Core output
    content: str = Field(description="Main content/response from the agent")
    artifacts: List[Dict[str, Any]] = Field(default_factory=list, description="Generated files, code, configs")

    # Metadata
    model_used: str = Field(description="AI model used for this task")
    complexity_assessed: TaskComplexity = Field(description="Assessed complexity level")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Confidence in the output")
    processing_time: float = Field(description="Time taken to process in seconds")

    # Context and reasoning
    reasoning: str = Field(description="Agent's reasoning process")
    next_steps: List[str] = Field(default_factory=list, description="Suggested next steps")
    dependencies: List[str] = Field(default_factory=list, description="Dependencies identified")

    # Quality metrics
    quality_score: float = Field(ge=0.0, le=1.0, description="Self-assessed quality score")
    review_required: bool = Field(default=False, description="Whether human review is recommended")    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)

    model_config = ConfigDict(use_enum_values=True, protected_namespaces=())


@dataclass
class TaskContext:
    """Context information for task execution."""
    project_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    previous_outputs: List[AgentOutput] = field(default_factory=list)
    project_rules: Optional[Dict[str, Any]] = None
    ltkb_context: Optional[str] = None
    stpm_context: Optional[str] = None


class OrchestratorConfig:
    """Configuration loader for the orchestrator."""

    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            config_path = str(Path(__file__).parent / "models_config.json")

        with open(config_path, 'r') as f:
            self.config = json.load(f)

    def get_routing_config(self) -> Dict[str, Any]:
        """Get agent routing configuration."""
        return self.config.get("routing", {})

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get information about a specific model."""
        models = self.config.get("providers", {}).get("ollama", {}).get("models", {})
        return models.get(model_name, {})

    def get_performance_settings(self) -> Dict[str, Any]:
        """Get performance configuration."""
        return self.config.get("performance_settings", {})

    def get_quality_thresholds(self) -> Dict[str, Any]:
        """Get quality threshold settings."""
        return self.config.get("quality_thresholds", {})


class EnhancedOrchestrator:
    """Enhanced AI Agent Orchestrator with intelligent routing and dispatch."""

    def __init__(self, config_path: Optional[str] = None):
        self.config = OrchestratorConfig(config_path)
        self.routing_config = self.config.get_routing_config()
        self.performance_settings = self.config.get_performance_settings()
        self.quality_thresholds = self.config.get_quality_thresholds()

        # Load new configuration sections
        self.load_balancing_config = self.config.config.get("load_balancing", {})
        self.adaptive_routing_config = self.config.config.get("adaptive_routing", {})
        self.analytics_config = self.config.config.get("model_analytics", {})

        # Initialize HTTP client for Ollama
        self.client = httpx.AsyncClient(
            timeout=self.performance_settings.get("request_timeout", 120)
        )

        # Initialize monitoring and balancing
        self.health_monitor = ModelHealthMonitor(self.client)
        self.load_balancer = LoadBalancingState()
        self.model_metrics: Dict[str, ModelPerformanceMetrics] = {}

        # Initialize analytics if enabled
        if self.analytics_config.get("track_performance", False):
            self._initialize_analytics()

    def _initialize_analytics(self):
        """Initialize analytics tracking."""
        logger.info("Initializing model analytics tracking")
        # Initialize metrics for all configured models
        for provider_config in self.config.config.get("providers", {}).values():
            for model_name in provider_config.get("models", {}):
                self.model_metrics[model_name] = ModelPerformanceMetrics(model_name)

    async def dispatch_to_agent(
        self,
        agent_name: str,
        task: str,
        context: TaskContext,
        task_type: Optional[TaskType] = None,
        complexity: Optional[TaskComplexity] = None
    ) -> AgentOutput:
        """
        Dispatch a task to a specific agent with intelligent model routing.

        Args:
            agent_name: The target agent (architect, frontend, backend, shell, issue_fix)
            task: The task description
            context: Task context and history
            task_type: Optional task type hint for better routing
            complexity: Optional complexity hint for model selection

        Returns:
            AgentOutput: Standardized agent response
        """
        start_time = datetime.now()
        selected_model = None

        try:
            # Assess task if not provided
            if complexity is None:
                complexity = await self._assess_task_complexity(task, task_type)

            if task_type is None:
                task_type = await self._classify_task_type(task)

            # Route to optimal model
            selected_model = await self.route_model_by_task(agent_name, task_type, complexity)

            # Execute task with retry logic
            response = await self._execute_task_with_retry(selected_model, agent_name, task, context, task_type)

            # Normalize output
            output = await self.normalize_agent_output(
                response, agent_name, task, task_type, complexity, selected_model, start_time
            )

            # Quality check
            await self._perform_quality_check(output)

            # Record performance metrics
            if self.analytics_config.get("track_performance", False):
                await self._record_performance(selected_model, output, True)

            return output

        except Exception as e:
            logger.error(f"Error dispatching task to {agent_name} with model {selected_model}: {str(e)}")

            # Record failure if we had a selected model
            if selected_model and self.analytics_config.get("track_performance", False):
                processing_time = (datetime.now() - start_time).total_seconds()
                await self._record_performance_failure(selected_model, processing_time)

            return self._create_error_output(agent_name, task, str(e), start_time)

        finally:
            # Release load balancing request
            if selected_model:
                self.load_balancer.release_request(selected_model)

    async def _execute_task_with_retry(
        self,
        model_name: str,
        agent_name: str,
        task: str,
        context: TaskContext,
        task_type: TaskType
    ) -> str:
        """Execute task with retry logic and fallback mechanisms."""
        max_retries = self.performance_settings.get("retry_attempts", 3)
        backoff_factor = self.performance_settings.get("backoff_factor", 1.5)

        last_exception = None

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # Wait with exponential backoff
                    await asyncio.sleep(backoff_factor ** attempt)
                    logger.info(f"Retrying task execution with {model_name}, attempt {attempt + 1}")

                return await self._execute_task(model_name, agent_name, task, context, task_type)

            except Exception as e:
                last_exception = e
                logger.warning(f"Attempt {attempt + 1} failed for {model_name}: {str(e)}")

                # Mark model as potentially unhealthy after multiple failures
                if attempt >= 2:
                    self.health_monitor.model_status[model_name] = False

        # All retries failed
        raise last_exception or Exception("Task execution failed after retries")

    async def _record_performance(self, model_name: str, output: AgentOutput, success: bool):
        """Record performance metrics for a model."""
        if model_name not in self.model_metrics:
            self.model_metrics[model_name] = ModelPerformanceMetrics(model_name)

        metrics = self.model_metrics[model_name]
        metrics.add_performance_data(
            response_time=output.processing_time,
            quality_score=output.quality_score,
            success=success
        )

        logger.debug(f"Recorded performance for {model_name}: "
                    f"response_time={output.processing_time:.2f}s, "
                    f"quality={output.quality_score:.2f}, "
                    f"health={metrics.health_score:.2f}")

    async def _record_performance_failure(self, model_name: str, processing_time: float):
        """Record a performance failure for a model."""
        if model_name not in self.model_metrics:
            self.model_metrics[model_name] = ModelPerformanceMetrics(model_name)

        metrics = self.model_metrics[model_name]
        metrics.add_performance_data(
            response_time=processing_time,
            quality_score=0.0,
            success=False
        )

    async def route_model_by_task(
        self,
        agent_name: str,
        task_type: TaskType,
        complexity: TaskComplexity
    ) -> str:
        """
        Route to the optimal model based on agent, task type, complexity, and real-time performance.

        Args:
            agent_name: Target agent name
            task_type: Type of task to be performed
            complexity: Assessed complexity level

        Returns:
            str: Selected model name
        """
        if agent_name not in self.routing_config:
            logger.warning(f"Unknown agent {agent_name}, using default routing")
            return settings.ai.default_model

        agent_config = self.routing_config[agent_name]

        # Get candidate models in priority order
        candidates = []

        # Check for task-specific routing first
        task_routing = agent_config.get("task_routing", {})
        if task_type.value in task_routing:
            candidates.append(task_routing[task_type.value])

        # Add complexity-based routing
        if complexity in [TaskComplexity.COMPLEX, TaskComplexity.EXPERT]:
            candidates.extend([
                agent_config.get("secondary"),
                agent_config.get("primary")
            ])
        else:
            candidates.extend([
                agent_config.get("primary"),
                agent_config.get("secondary")
            ])

        # Add fallback
        if agent_config.get("fallback"):
            candidates.append(agent_config["fallback"])

        # Remove None values and duplicates while preserving order
        candidates = list(dict.fromkeys([c for c in candidates if c]))

        if not candidates:
            logger.error(f"No candidate models for agent {agent_name}")
            return settings.ai.default_model

        # Apply intelligent routing with health and performance considerations
        return await self._select_optimal_model(candidates, task_type, complexity)

    async def _select_optimal_model(
        self,
        candidate_models: List[str],
        task_type: TaskType,
        complexity: TaskComplexity
    ) -> str:
        """
        Select the optimal model from candidates based on health, performance, and load balancing.
        """
        # Filter to healthy models
        healthy_models = await self.health_monitor.get_healthy_models(candidate_models)

        if not healthy_models:
            logger.warning(f"No healthy models available from {candidate_models}")
            # Fall back to first candidate and hope for the best
            return candidate_models[0]

        # If only one healthy model, use it
        if len(healthy_models) == 1:
            return healthy_models[0]

        # Apply load balancing strategy
        strategy = self.load_balancing_config.get("strategy", "round_robin")

        if strategy == "round_robin":
            selected = self.load_balancer.get_round_robin_model(healthy_models)
        elif strategy == "least_loaded":
            selected = self.load_balancer.get_least_loaded_model(healthy_models)
        elif strategy == "performance_weighted":
            selected = await self._select_by_performance(healthy_models, task_type, complexity)
        else:
            # Default to round robin
            selected = self.load_balancer.get_round_robin_model(healthy_models)

        # Record the selection for load balancing
        self.load_balancer.record_request(selected)

        logger.debug(f"Selected model {selected} from candidates {candidate_models} (healthy: {healthy_models})")
        return selected

    async def _select_by_performance(
        self,
        healthy_models: List[str],
        task_type: TaskType,
        complexity: TaskComplexity
    ) -> str:
        """
        Select model based on performance metrics and adaptive routing.
        """
        if not self.adaptive_routing_config.get("enabled", False):
            return healthy_models[0]

        best_model = healthy_models[0]
        best_score = 0.0

        for model in healthy_models:
            metrics = self.model_metrics.get(model)
            if not metrics:
                # New model, give it a chance
                score = 0.5
            else:
                # Calculate composite score
                health_weight = self.load_balancing_config.get("health_weight", 0.4)
                performance_weight = self.load_balancing_config.get("performance_weight", 0.3)
                availability_weight = self.load_balancing_config.get("availability_weight", 0.3)

                # Normalize response time (lower is better)
                response_score = min(1.0, 10.0 / (metrics.avg_response_time + 1.0))

                score = (
                    metrics.health_score * health_weight +
                    response_score * performance_weight +
                    metrics.avg_quality_score * availability_weight
                )

            if score > best_score:
                best_score = score
                best_model = model

        return best_model

    async def normalize_agent_output(
        self,
        raw_response: str,
        agent_name: str,
        task: str,
        task_type: TaskType,
        complexity: TaskComplexity,
        model_used: str,
        start_time: datetime
    ) -> AgentOutput:
        """
        Normalize raw agent response into standardized AgentOutput format.

        Args:
            raw_response: Raw response from the AI model
            agent_name: Name of the agent
            task: Original task description
            task_type: Type of task
            complexity: Assessed complexity
            model_used: Model that generated the response
            start_time: Task start time

        Returns:
            AgentOutput: Standardized output
        """
        processing_time = (datetime.now() - start_time).total_seconds()

        # Parse response for artifacts, reasoning, etc.
        artifacts = await self._extract_artifacts(raw_response)
        reasoning = await self._extract_reasoning(raw_response)
        next_steps = await self._extract_next_steps(raw_response)
        dependencies = await self._extract_dependencies(raw_response)

        # Assess quality and confidence
        confidence_score = await self._assess_confidence(raw_response, task_type)
        quality_score = await self._assess_quality(raw_response, task_type)
        review_required = quality_score < self.quality_thresholds.get("minimum_confidence", 0.7)

        return AgentOutput(
            agent_name=agent_name,
            task_id=f"{agent_name}_{int(start_time.timestamp())}",
            task_type=task_type,
            status="success",
            content=raw_response,
            artifacts=artifacts,
            model_used=model_used,
            complexity_assessed=complexity,
            confidence_score=confidence_score,
            processing_time=processing_time,
            reasoning=reasoning,
            next_steps=next_steps,
            dependencies=dependencies,
            quality_score=quality_score,
            review_required=review_required
        )

    async def _execute_task(
        self,
        model_name: str,
        agent_name: str,
        task: str,
        context: TaskContext,
        task_type: TaskType
    ) -> str:
        """Execute the task using the Universal LLM service with rule enforcement."""
        # Build context-aware prompt
        prompt = await self._build_agent_prompt(agent_name, task, context, task_type)

        # Create LLM request with rule enforcement
        llm_request = LLMRequest(
            prompt=prompt,
            agent_name=agent_name,
            task_type=task_type.value,
            model_name=model_name,
            temperature=getattr(settings.ai, 'temperature', 0.7),
            max_tokens=getattr(settings.ai, 'max_tokens', 4096),
            user_id=getattr(context, 'user_id', None),
            session_id=getattr(context, 'session_id', None)
        )

        # Execute through Universal LLM service (with mandatory rule enforcement)
        universal_llm = get_universal_llm()
        llm_response = await universal_llm.generate(llm_request)

        # Check if response is rule-compliant
        if not llm_response.rule_compliant:
            violation_details = [v.description for v in llm_response.violations]
            logger.error(f"LLM response blocked due to rule violations: {violation_details}")
            raise Exception(f"Task execution blocked due to rule violations: {violation_details}")

        # Log successful rule-compliant execution
        logger.info(f"Rule-compliant response from {llm_response.model} ({llm_response.provider}) for agent {agent_name}")

        return llm_response.content

    async def _build_agent_prompt(
        self,
        agent_name: str,
        task: str,
        context: TaskContext,
        task_type: TaskType
    ) -> str:
        """Build a context-aware prompt for the agent."""
        # Get base agent prompt from agents.py config
        # This would integrate with the existing AGENT_CONFIGS

        base_prompt = f"""You are the {agent_name.title()} Agent in the AI Coding Agent system.
Task Type: {task_type.value}
Task: {task}

"""

        # Add context if available
        if context.ltkb_context:
            base_prompt += f"LTKB Context:\n{context.ltkb_context}\n\n"

        if context.stpm_context:
            base_prompt += f"Project Context:\n{context.stpm_context}\n\n"

        if context.previous_outputs:
            base_prompt += "Previous Outputs:\n"
            for output in context.previous_outputs[-3:]:  # Last 3 outputs
                base_prompt += f"- {output.agent_name}: {output.content[:200]}...\n"
            base_prompt += "\n"

        base_prompt += """Please provide your response in a clear, structured format.
Include reasoning for your decisions and any next steps or dependencies."""

        return base_prompt

    # Enhanced assessment and extraction methods
    async def _assess_task_complexity(self, task: str, task_type: Optional[TaskType]) -> TaskComplexity:
        """Assess the complexity of a task using multiple heuristics."""
        # Initialize complexity score
        complexity_score = 0

        # Length-based assessment
        if len(task) > 500:
            complexity_score += 3
        elif len(task) > 200:
            complexity_score += 2
        elif len(task) > 50:
            complexity_score += 1

        # Keyword-based assessment
        complex_keywords = [
            'architecture', 'design', 'integration', 'optimization', 'security',
            'authentication', 'authorization', 'database', 'performance', 'scalability'
        ]
        moderate_keywords = [
            'api', 'endpoint', 'component', 'service', 'module', 'class', 'function'
        ]

        task_lower = task.lower()
        complexity_score += sum(2 for keyword in complex_keywords if keyword in task_lower)
        complexity_score += sum(1 for keyword in moderate_keywords if keyword in task_lower)

        # Task type specific assessment
        if task_type:
            complex_task_types = [
                TaskType.SYSTEM_DESIGN, TaskType.ARCHITECTURE, TaskType.AUTHENTICATION,
                TaskType.DATABASE_DESIGN, TaskType.OPTIMIZATION
            ]
            moderate_task_types = [
                TaskType.API_DESIGN, TaskType.CODE_REVIEW, TaskType.REFACTORING,
                TaskType.BUSINESS_LOGIC
            ]

            if task_type in complex_task_types:
                complexity_score += 2
            elif task_type in moderate_task_types:
                complexity_score += 1

        # Map score to complexity level
        if complexity_score >= 6:
            return TaskComplexity.EXPERT
        elif complexity_score >= 4:
            return TaskComplexity.COMPLEX
        elif complexity_score >= 2:
            return TaskComplexity.MODERATE
        else:
            return TaskComplexity.SIMPLE

    async def _classify_task_type(self, task: str) -> TaskType:
        """Classify the type of task based on keywords and patterns."""
        task_lower = task.lower()

        # Define keyword patterns for each task type
        task_patterns = {
            TaskType.DEBUGGING: ['debug', 'fix', 'error', 'bug', 'issue', 'troubleshoot'],
            TaskType.SYSTEM_DESIGN: ['design', 'architecture', 'system', 'structure', 'pattern'],
            TaskType.UI_COMPONENT: ['ui', 'component', 'frontend', 'react', 'interface', 'html', 'css'],
            TaskType.API_DESIGN: ['api', 'endpoint', 'rest', 'graphql', 'service', 'backend'],
            TaskType.PLANNING: ['plan', 'roadmap', 'organize', 'strategy', 'outline'],
            TaskType.CODE_GENERATION: ['create', 'generate', 'implement', 'build', 'develop'],
            TaskType.CODE_REVIEW: ['review', 'analyze', 'examine', 'check', 'audit'],
            TaskType.AUTHENTICATION: ['auth', 'login', 'user', 'session', 'token', 'security'],
            TaskType.DATABASE_DESIGN: ['database', 'db', 'table', 'schema', 'model', 'entity'],
            TaskType.DEPLOYMENT: ['deploy', 'deployment', 'production', 'release', 'publish'],
            TaskType.SCRIPTING: ['script', 'automation', 'batch', 'command'],
            TaskType.UNIT_TESTING: ['test', 'testing', 'unittest', 'spec', 'coverage'],
            TaskType.OPTIMIZATION: ['optimize', 'performance', 'speed', 'efficient', 'fast'],
            TaskType.REFACTORING: ['refactor', 'cleanup', 'improve', 'restructure']
        }

        # Score each task type based on keyword matches
        type_scores = {}
        for task_type, keywords in task_patterns.items():
            score = sum(1 for keyword in keywords if keyword in task_lower)
            if score > 0:
                type_scores[task_type] = score

        # Return the highest scoring task type
        if type_scores:
            return max(type_scores.items(), key=lambda x: x[1])[0]

        # Default to code generation if no clear pattern
        return TaskType.CODE_GENERATION

    async def _extract_artifacts(self, response: str) -> List[Dict[str, Any]]:
        """Extract code artifacts and files from response."""
        artifacts = []

        # Extract code blocks
        code_pattern = r'```(\w+)?\s*\n(.*?)\n```'
        code_matches = re.findall(code_pattern, response, re.DOTALL)

        for i, (language, code) in enumerate(code_matches):
            artifacts.append({
                "type": "code",
                "name": f"code_block_{i}",
                "language": language or "text",
                "content": code.strip(),
                "size": len(code.strip())
            })

        # Extract file references
        file_pattern = r'(?:file|create|save):\s*([^\s\n]+\.[a-zA-Z0-9]+)'
        file_matches = re.findall(file_pattern, response, re.IGNORECASE)

        for filename in file_matches:
            artifacts.append({
                "type": "file_reference",
                "name": filename,
                "content": "",
                "description": f"Referenced file: {filename}"
            })

        # Extract configuration blocks
        config_pattern = r'(?:config|configuration|settings):\s*\n(.*?)(?=\n\n|\Z)'
        config_matches = re.findall(config_pattern, response, re.DOTALL | re.IGNORECASE)

        for i, config in enumerate(config_matches):
            artifacts.append({
                "type": "configuration",
                "name": f"config_{i}",
                "content": config.strip(),
                "description": "Configuration settings"
            })

        return artifacts

    async def _extract_reasoning(self, response: str) -> str:
        """Extract reasoning and explanation from response."""
        # Look for reasoning keywords and extract surrounding text
        reasoning_patterns = [
            r'(?:because|since|due to|reason|rationale|explanation)[:.]?\s*(.*?)(?=\n\n|\.|$)',
            r'(?:approach|strategy|methodology)[:.]?\s*(.*?)(?=\n\n|\.|$)',
            r'(?:considerations|factors|requirements)[:.]?\s*(.*?)(?=\n\n|\.|$)'
        ]

        reasoning_parts = []
        for pattern in reasoning_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
            reasoning_parts.extend(matches)

        if reasoning_parts:
            # Combine and clean up reasoning
            reasoning = ' '.join(reasoning_parts[:3])  # Limit to first 3 matches
            return reasoning[:500]  # Limit length

        # Fallback: extract first explanatory sentence
        sentences = response.split('.')
        for sentence in sentences:
            if any(word in sentence.lower() for word in ['this', 'approach', 'solution', 'implementation']):
                return sentence.strip()[:200]

        return "Reasoning not explicitly provided in response"

    async def _extract_next_steps(self, response: str) -> List[str]:
        """Extract next steps and recommendations from response."""
        next_steps = []

        # Look for explicit next steps
        steps_patterns = [
            r'(?:next steps?|todo|next|following)[:.]?\s*\n?((?:\d+\.|\-|\*)\s*.*?)(?=\n\n|\Z)',
            r'(?:recommendations?|suggestions?)[:.]?\s*\n?((?:\d+\.|\-|\*)\s*.*?)(?=\n\n|\Z)',
            r'(?:action items?|tasks?)[:.]?\s*\n?((?:\d+\.|\-|\*)\s*.*?)(?=\n\n|\Z)'
        ]

        for pattern in steps_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
            for match in matches:
                # Split on list markers and clean up
                steps = re.split(r'\n(?=\d+\.|\-|\*)', match)
                next_steps.extend([step.strip() for step in steps if step.strip()])

        # Remove duplicates while preserving order
        seen = set()
        unique_steps = []
        for step in next_steps:
            step_clean = re.sub(r'^\d+\.|\-|\*\s*', '', step).strip()
            if step_clean and step_clean not in seen:
                seen.add(step_clean)
                unique_steps.append(step_clean)

        return unique_steps[:5]  # Limit to 5 steps

    async def _extract_dependencies(self, response: str) -> List[str]:
        """Extract dependencies and requirements from response."""
        dependencies = []

        # Look for dependency keywords
        dep_patterns = [
            r'(?:requires?|needs?|depends on|dependencies?)[:.]?\s*(.*?)(?=\n|\.|$)',
            r'(?:install|import|include)[:.]?\s*([\w\-\.]+)',
            r'npm install\s+([\w\-\@\/\s]+)',
            r'pip install\s+([\w\-\[\]\s]+)'
        ]

        for pattern in dep_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match else ''

                # Clean and split dependencies
                deps = re.split(r'[,\s]+', match.strip())
                dependencies.extend([dep.strip() for dep in deps if dep.strip()])

        # Clean up and deduplicate
        unique_deps = list(dict.fromkeys([
            dep for dep in dependencies
            if dep and len(dep) > 1 and not dep.isspace()
        ]))

        return unique_deps[:10]  # Limit to 10 dependencies

    async def _assess_confidence(self, response: str, task_type: TaskType) -> float:
        """Assess confidence in the response based on content analysis."""
        confidence_score = 0.5  # Base confidence

        # Length and completeness
        if len(response) > 200:
            confidence_score += 0.1
        if len(response) > 500:
            confidence_score += 0.1

        # Code quality indicators for code-related tasks
        if task_type in [TaskType.CODE_GENERATION, TaskType.API_DESIGN, TaskType.UI_COMPONENT]:
            if '```' in response:
                confidence_score += 0.15  # Contains code blocks
            if any(word in response.lower() for word in ['function', 'class', 'const', 'let']):
                confidence_score += 0.1   # Contains code keywords

        # Structure and organization
        if response.count('\n') > 5:
            confidence_score += 0.05  # Well-structured

        # Presence of explanations
        explanation_words = ['because', 'since', 'this', 'approach', 'solution', 'implementation']
        if any(word in response.lower() for word in explanation_words):
            confidence_score += 0.1

        # Uncertainty indicators (negative)
        uncertainty_words = ['maybe', 'perhaps', 'might', 'could be', 'not sure', 'uncertain']
        uncertainty_count = sum(1 for word in uncertainty_words if word in response.lower())
        confidence_score -= uncertainty_count * 0.05

        return max(0.0, min(1.0, confidence_score))

    async def _assess_quality(self, response: str, task_type: TaskType) -> float:
        """Assess quality of the response based on multiple factors."""
        quality_score = 0.5  # Base quality

        # Completeness
        if len(response) > 100:
            quality_score += 0.1
        if len(response) > 300:
            quality_score += 0.1

        # Specific quality checks by task type
        if task_type in [TaskType.CODE_GENERATION, TaskType.API_DESIGN]:
            # Check for code structure
            if '```' in response:
                quality_score += 0.15
            if any(pattern in response for pattern in ['function', 'class', 'def ', 'const ']):
                quality_score += 0.1

        elif task_type == TaskType.PLANNING:
            # Check for structured planning
            if any(marker in response for marker in ['1.', '2.', '-', '*']):
                quality_score += 0.1
            if 'phase' in response.lower() or 'step' in response.lower():
                quality_score += 0.1

        # General quality indicators
        sentences = response.split('.')
        if len(sentences) > 3:
            quality_score += 0.05  # Multiple sentences

        # Check for actionable content
        action_words = ['create', 'implement', 'build', 'design', 'configure', 'install']
        if any(word in response.lower() for word in action_words):
            quality_score += 0.1

        return max(0.0, min(1.0, quality_score))

    async def _perform_quality_check(self, output: AgentOutput) -> None:
        """Perform quality checks on the output."""
        min_confidence = self.quality_thresholds.get("minimum_confidence", 0.7)
        if output.confidence_score < min_confidence:
            output.review_required = True
            logger.warning(f"Low confidence output from {output.agent_name}: {output.confidence_score}")

    def _create_error_output(
        self,
        agent_name: str,
        task: str,
        error_msg: str,
        start_time: datetime
    ) -> AgentOutput:
        """Create an error output when task execution fails."""
        return AgentOutput(
            agent_name=agent_name,
            task_id=f"{agent_name}_error_{int(start_time.timestamp())}",
            task_type=TaskType.EXPLANATION,
            status="error",
            content=f"Error executing task: {error_msg}",
            artifacts=[],
            model_used="error",
            complexity_assessed=TaskComplexity.SIMPLE,
            confidence_score=0.0,
            processing_time=(datetime.now() - start_time).total_seconds(),
            reasoning=f"Task failed due to: {error_msg}",
            next_steps=["Review error and retry", "Check model availability"],
            dependencies=[],
            quality_score=0.0,
            review_required=True
        )

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()

    # Analytics and reporting methods
    async def get_model_analytics(self) -> Dict[str, Any]:
        """Get comprehensive analytics for all models."""
        analytics = {
            "timestamp": datetime.now().isoformat(),
            "models": {},
            "summary": {
                "total_models": len(self.model_metrics),
                "healthy_models": 0,
                "avg_health_score": 0.0,
                "total_requests": 0
            }
        }

        total_health = 0.0
        total_requests = 0

        for model_name, metrics in self.model_metrics.items():
            model_data = {
                "health_score": metrics.health_score,
                "avg_response_time": metrics.avg_response_time,
                "avg_quality_score": metrics.avg_quality_score,
                "success_rate": metrics.success_rate,
                "total_requests": metrics.success_count + metrics.error_count,
                "last_used": metrics.last_used.isoformat() if metrics.last_used else None,
                "consecutive_failures": metrics.consecutive_failures
            }

            analytics["models"][model_name] = model_data
            total_health += metrics.health_score
            total_requests += model_data["total_requests"]

            if metrics.health_score > 0.8:
                analytics["summary"]["healthy_models"] += 1

        if self.model_metrics:
            analytics["summary"]["avg_health_score"] = total_health / len(self.model_metrics)
        analytics["summary"]["total_requests"] = total_requests

        return analytics

    async def get_load_balancing_status(self) -> Dict[str, Any]:
        """Get current load balancing status."""
        return {
            "strategy": self.load_balancing_config.get("strategy", "round_robin"),
            "current_loads": dict(self.load_balancer.current_loads),
            "request_counts": dict(self.load_balancer.request_counts),
            "last_used_model": self.load_balancer.last_used_model,
            "round_robin_index": self.load_balancer.round_robin_index
        }

    async def optimize_model_routing(self) -> Dict[str, Any]:
        """Analyze performance and suggest routing optimizations."""
        if not self.analytics_config.get("track_performance", False):
            return {"message": "Analytics not enabled"}

        optimizations = {
            "timestamp": datetime.now().isoformat(),
            "suggestions": [],
            "performance_insights": {}
        }

        # Analyze each model's performance
        for model_name, metrics in self.model_metrics.items():
            if metrics.success_count + metrics.error_count < 5:
                continue  # Skip models with insufficient data

            insights = {
                "model": model_name,
                "health_score": metrics.health_score,
                "avg_response_time": metrics.avg_response_time,
                "success_rate": metrics.success_rate
            }

            # Generate suggestions
            if metrics.health_score < 0.5:
                optimizations["suggestions"].append({
                    "type": "health_warning",
                    "model": model_name,
                    "message": f"Model {model_name} has low health score ({metrics.health_score:.2f})"
                })

            if metrics.avg_response_time > 30.0:
                optimizations["suggestions"].append({
                    "type": "performance_warning",
                    "model": model_name,
                    "message": f"Model {model_name} has slow response time ({metrics.avg_response_time:.1f}s)"
                })

            if metrics.success_rate < 0.8:
                optimizations["suggestions"].append({
                    "type": "reliability_warning",
                    "model": model_name,
                    "message": f"Model {model_name} has low success rate ({metrics.success_rate:.1%})"
                })

            optimizations["performance_insights"][model_name] = insights

        return optimizations

    async def reset_model_metrics(self, model_name: Optional[str] = None):
        """Reset performance metrics for a specific model or all models."""
        if model_name:
            if model_name in self.model_metrics:
                self.model_metrics[model_name] = ModelPerformanceMetrics(model_name)
                logger.info(f"Reset metrics for model {model_name}")
        else:
            for model in self.model_metrics:
                self.model_metrics[model] = ModelPerformanceMetrics(model)
            logger.info("Reset metrics for all models")
@dataclass
class ModelPerformanceMetrics:
    """Performance metrics for a model."""
    model_name: str
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    quality_scores: deque = field(default_factory=lambda: deque(maxlen=100))
    error_count: int = 0
    success_count: int = 0
    last_used: Optional[datetime] = None
    health_score: float = 1.0
    consecutive_failures: int = 0

    def add_performance_data(self, response_time: float, quality_score: float, success: bool):
        """Add performance data point."""
        self.response_times.append(response_time)
        self.quality_scores.append(quality_score)
        self.last_used = datetime.now()

        if success:
            self.success_count += 1
            self.consecutive_failures = 0
        else:
            self.error_count += 1
            self.consecutive_failures += 1

        self._update_health_score()

    def _update_health_score(self):
        """Update health score based on recent performance."""
        if not self.response_times or not self.quality_scores:
            return

        # Calculate success rate
        total_requests = self.success_count + self.error_count
        success_rate = self.success_count / total_requests if total_requests > 0 else 1.0

        # Calculate average quality
        avg_quality = statistics.mean(self.quality_scores) if self.quality_scores else 0.5

        # Calculate response time factor (lower is better)
        avg_response_time = statistics.mean(self.response_times) if self.response_times else 1.0
        response_factor = min(1.0, 10.0 / avg_response_time)  # Cap at 1.0, prefer <10s responses

        # Penalty for consecutive failures
        failure_penalty = max(0.1, 1.0 - (self.consecutive_failures * 0.2))

        # Combine factors
        self.health_score = (success_rate * 0.4 + avg_quality * 0.3 + response_factor * 0.2) * failure_penalty
        self.health_score = max(0.0, min(1.0, self.health_score))

    @property
    def avg_response_time(self) -> float:
        """Get average response time."""
        return statistics.mean(self.response_times) if self.response_times else 0.0

    @property
    def avg_quality_score(self) -> float:
        """Get average quality score."""
        return statistics.mean(self.quality_scores) if self.quality_scores else 0.0

    @property
    def success_rate(self) -> float:
        """Get success rate."""
        total = self.success_count + self.error_count
        return self.success_count / total if total > 0 else 1.0


@dataclass
class LoadBalancingState:
    """State for load balancing across models."""
    current_loads: Dict[str, int] = field(default_factory=dict)
    request_counts: Dict[str, int] = field(default_factory=dict)
    last_used_model: Optional[str] = None
    round_robin_index: int = 0

    def record_request(self, model_name: str):
        """Record a request to a model."""
        self.current_loads[model_name] = self.current_loads.get(model_name, 0) + 1
        self.request_counts[model_name] = self.request_counts.get(model_name, 0) + 1
        self.last_used_model = model_name

    def release_request(self, model_name: str):
        """Release a request from a model."""
        if model_name in self.current_loads:
            self.current_loads[model_name] = max(0, self.current_loads[model_name] - 1)

    def get_least_loaded_model(self, available_models: List[str]) -> str:
        """Get the model with the least current load."""
        if not available_models:
            raise ValueError("No available models")

        loads = [(self.current_loads.get(model, 0), model) for model in available_models]
        loads.sort()
        return loads[0][1]

    def get_round_robin_model(self, available_models: List[str]) -> str:
        """Get next model using round robin."""
        if not available_models:
            raise ValueError("No available models")

        model = available_models[self.round_robin_index % len(available_models)]
        self.round_robin_index += 1
        return model


class ModelHealthMonitor:
    """Monitor model health and availability."""

    def __init__(self, client: httpx.AsyncClient):
        self.client = client
        self.model_status: Dict[str, bool] = {}
        self.last_health_check: Dict[str, datetime] = {}
        self.health_check_interval = timedelta(minutes=2)  # Reduced for more frequent checks
        self.model_load_timeout = 60.0  # Increased timeout for model loading

    async def check_model_health(self, model_name: str, force_check: bool = False) -> bool:
        """Check if a model is healthy and available."""
        now = datetime.now()
        last_check = self.last_health_check.get(model_name)

        # Use cached result if recent (unless forced)
        if (not force_check and last_check and
            (now - last_check) < self.health_check_interval):
            return self.model_status.get(model_name, False)

        try:
            # Test with a simple prompt - increased timeout for model loading
            ollama_url = settings.ai.ollama_host
            logger.debug(f"Health checking {model_name} at {ollama_url}")

            response = await self.client.post(
                f"{ollama_url}/api/generate",
                json={
                    "model": model_name,
                    "prompt": "Hi",
                    "stream": False,
                    "options": {
                        "num_predict": 1,  # Only generate 1 token
                        "temperature": 0.1,
                        "top_p": 0.1
                    }
                },
                timeout=self.model_load_timeout
            )

            is_healthy = response.status_code == 200

            if is_healthy:
                # Verify we got a valid response
                result = response.json()
                if 'response' in result:
                    logger.debug(f"✅ {model_name} is healthy")
                else:
                    is_healthy = False
                    logger.warning(f"❌ {model_name} returned invalid response format")
            else:
                logger.warning(f"❌ {model_name} health check failed with status {response.status_code}")

            self.model_status[model_name] = is_healthy
            self.last_health_check[model_name] = now

            return is_healthy

        except asyncio.TimeoutError:
            logger.warning(f"❌ Health check timeout for {model_name} (>{self.model_load_timeout}s)")
            self.model_status[model_name] = False
            self.last_health_check[model_name] = now
            return False
        except Exception as e:
            logger.warning(f"❌ Health check failed for {model_name}: {str(e)}")
            self.model_status[model_name] = False
            self.last_health_check[model_name] = now
            return False

    async def get_healthy_models(self, model_list: List[str], force_check: bool = False) -> List[str]:
        """Get list of healthy models from the provided list."""
        healthy_models = []

        # Check models in parallel for faster health checking
        tasks = [self.check_model_health(model, force_check) for model in model_list]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        for model, result in zip(model_list, results):
            if isinstance(result, bool) and result:
                healthy_models.append(model)
            elif isinstance(result, Exception):
                logger.error(f"Error checking {model}: {result}")

        logger.info(f"Health check complete: {len(healthy_models)}/{len(model_list)} models healthy: {healthy_models}")
        return healthy_models

    async def warm_up_models(self, model_list: List[str]) -> Dict[str, bool]:
        """Warm up models by sending simple requests to load them into memory."""
        logger.info(f"Warming up {len(model_list)} models...")
        warmup_results = {}

        for model in model_list:
            try:
                logger.info(f"Warming up {model}...")
                success = await self.check_model_health(model, force_check=True)
                warmup_results[model] = success
                if success:
                    logger.info(f"✅ {model} warmed up successfully")
                else:
                    logger.warning(f"❌ {model} failed to warm up")
            except Exception as e:
                logger.error(f"❌ Error warming up {model}: {e}")
                warmup_results[model] = False

        return warmup_results


# Global orchestrator instance
orchestrator = EnhancedOrchestrator()

# Enhanced convenience functions for direct usage
async def dispatch_to_agent(
    agent_name: str,
    task: str,
    context: Optional[TaskContext] = None,
    task_type: Optional[TaskType] = None,
    complexity: Optional[TaskComplexity] = None
) -> AgentOutput:
    """Convenience function to dispatch a task to an agent."""
    if context is None:
        context = TaskContext()

    return await orchestrator.dispatch_to_agent(
        agent_name, task, context, task_type, complexity
    )


async def route_model_by_task(
    agent_name: str,
    task_type: TaskType,
    complexity_level: TaskComplexity
) -> str:
    """Convenience function to route model by task parameters."""
    return await orchestrator.route_model_by_task(agent_name, task_type, complexity_level)


async def normalize_agent_output(
    agent_response: str,
    agent_name: str,
    task: str,
    output_format: str = "standard"
) -> AgentOutput:
    """Convenience function to normalize agent output."""
    start_time = datetime.now()
    task_type = await orchestrator._classify_task_type(task)
    complexity = await orchestrator._assess_task_complexity(task, task_type)

    return await orchestrator.normalize_agent_output(
        agent_response, agent_name, task, task_type, complexity, "unknown", start_time
    )


# Analytics convenience functions
async def get_model_analytics() -> Dict[str, Any]:
    """Get comprehensive model analytics."""
    return await orchestrator.get_model_analytics()


async def get_load_balancing_status() -> Dict[str, Any]:
    """Get current load balancing status."""
    return await orchestrator.get_load_balancing_status()


async def optimize_model_routing() -> Dict[str, Any]:
    """Get routing optimization suggestions."""
    return await orchestrator.optimize_model_routing()


async def reset_model_metrics(model_name: Optional[str] = None):
    """Reset performance metrics."""
    await orchestrator.reset_model_metrics(model_name)
