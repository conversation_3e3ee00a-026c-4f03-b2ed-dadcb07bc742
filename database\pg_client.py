"""
Optional: Thin wrapper for local Postgres (DATABASE_URL).
"""
import os
from typing import Any, Optional
DATABASE_URL = os.getenv("DATABASE_URL")

_pg_conn = None

def get_pg(user_id: Optional[str] = None):
    global _pg_conn
    if _pg_conn is None and DATABASE_URL:
        import psycopg2
        _pg_conn = psycopg2.connect(DATABASE_URL)
    if user_id and _pg_conn:
        with _pg_conn.cursor() as cur:
            cur.execute("SET app.current_user_id = %s", (user_id,))
    return _pg_conn

def run_sql(sql: str, *params, user_id: Optional[str] = None) -> Any:
    conn = get_pg(user_id)
    if conn is None:
        raise ValueError("DATABASE_URL is not set or connection failed.")
    with conn.cursor() as cur:
        cur.execute(sql, params)
        if cur.description:
            return cur.fetchall()
        conn.commit()
        return None

# Async version
async def async_run_sql(sql: str, *params) -> Any:
    import asyncpg
    conn = await asyncpg.connect(DATABASE_URL)
    result = await conn.fetch(sql, *params)
    await conn.close()
    return result
