#!/usr/bin/env python3
"""
Test runner for Phase A2: Enhanced Model Configuration & Orchestrator

This script runs all orchestrator tests and provides comprehensive validation
of the Phase A2 implementation.
"""

import sys
import subprocess
import time
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def run_tests():
    """Run all orchestrator tests with proper reporting."""
    print("🚀 Running Phase A2: Enhanced Orchestrator Tests")
    print("=" * 80)

    test_files = [
        "tests/test_orchestrator.py",
        "tests/test_orchestrator_integration.py",
        "tests/test_orchestrator_performance.py"
    ]

    results = {}
    total_start_time = time.time()

    for test_file in test_files:
        print(f"\n📋 Running {test_file}")
        print("-" * 40)

        start_time = time.time()

        try:
            # Run pytest with verbose output
            result = subprocess.run([
                sys.executable, "-m", "pytest",
                test_file,
                "-v",
                "--tb=short",
                "--disable-warnings"
            ], capture_output=True, text=True, timeout=300)

            end_time = time.time()
            duration = end_time - start_time

            if result.returncode == 0:
                print(f"✅ {test_file} PASSED ({duration:.2f}s)")
                results[test_file] = {"status": "PASSED", "duration": duration}
            else:
                print(f"❌ {test_file} FAILED ({duration:.2f}s)")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                results[test_file] = {"status": "FAILED", "duration": duration}

        except subprocess.TimeoutExpired:
            print(f"⏰ {test_file} TIMEOUT (exceeded 300s)")
            results[test_file] = {"status": "TIMEOUT", "duration": 300}
        except Exception as e:
            print(f"💥 {test_file} ERROR: {str(e)}")
            results[test_file] = {"status": "ERROR", "duration": 0}

    total_end_time = time.time()
    total_duration = total_end_time - total_start_time

    # Print summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)

    passed = sum(1 for r in results.values() if r["status"] == "PASSED")
    failed = sum(1 for r in results.values() if r["status"] == "FAILED")
    errors = sum(1 for r in results.values() if r["status"] in ["TIMEOUT", "ERROR"])

    print(f"Total Tests: {len(test_files)}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"💥 Errors: {errors}")
    print(f"⏱️  Total Time: {total_duration:.2f}s")

    print("\nDetailed Results:")
    for test_file, result in results.items():
        status_icon = "✅" if result["status"] == "PASSED" else "❌"
        print(f"  {status_icon} {test_file}: {result['status']} ({result['duration']:.2f}s)")

    if failed == 0 and errors == 0:
        print("\n🎉 All tests passed! Phase A2 implementation is validated.")
        return True
    else:
        print(f"\n⚠️  {failed + errors} test(s) failed. Review the output above.")
        return False


def run_quick_validation():
    """Run a quick validation test of core functionality."""
    print("\n🔍 Running Quick Validation")
    print("-" * 40)

    try:
        # Import and test basic functionality
        from ai_coding_agent.orchestrator import (
            EnhancedOrchestrator, TaskType, TaskComplexity, TaskContext
        )

        print("✅ Orchestrator imports successful")

        # Test configuration loading
        try:
            orchestrator = EnhancedOrchestrator()
            print("✅ Orchestrator initialization successful")

            # Test basic routing (without actual model calls)
            routing_config = orchestrator.routing_config
            if routing_config:
                print("✅ Routing configuration loaded")
            else:
                print("⚠️  Routing configuration empty")

            # Test load balancing state
            lb_state = orchestrator.load_balancer
            print("✅ Load balancing state initialized")

            # Test health monitor
            health_monitor = orchestrator.health_monitor
            print("✅ Health monitor initialized")

            print("✅ Quick validation completed successfully")
            return True

        except Exception as e:
            print(f"❌ Orchestrator initialization failed: {str(e)}")
            return False

    except ImportError as e:
        print(f"❌ Import failed: {str(e)}")
        return False


def main():
    """Main test runner function."""
    print("🧪 Phase A2: Enhanced Orchestrator Test Suite")
    print("Testing comprehensive orchestrator functionality including:")
    print("  • Intelligent model routing and selection")
    print("  • Load balancing across available models")
    print("  • Health monitoring and fallback mechanisms")
    print("  • Performance tracking and analytics")
    print("  • Quality assessment and optimization")
    print()

    # Run quick validation first
    validation_success = run_quick_validation()

    if not validation_success:
        print("\n❌ Quick validation failed. Check your environment setup.")
        return False

    # Run full test suite
    test_success = run_tests()

    if test_success:
        print("\n🏆 Phase A2 Implementation Successfully Validated!")
        print("\nNext Steps:")
        print("  • Phase A3: Vector DB & Embedding Infrastructure")
        print("  • Integration with existing AI agent framework")
        print("  • Performance optimization based on real-world usage")
    else:
        print("\n🔧 Some tests failed. Please review and fix issues before proceeding.")

    return test_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
