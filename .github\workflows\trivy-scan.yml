name: Trivy Vulnerability Scan

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

jobs:
  trivy-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build Docker images
        run: |
          docker build -t ai-coding-agent-api-dev -f backend/Dockerfile ./backend
          docker build -t ai-coding-agent-frontend-dev -f frontend/Dockerfile.dev ./frontend
      - name: Run Trivy scan on API image
        uses: aquasecurity/trivy-action@v0.14.0
        with:
          image-ref: ai-coding-agent-api-dev
          ignore-unfixed: true
          format: table
          exit-code: 1
          severity: CRITICAL,HIGH
          trivyignores: .trivyignore
      - name: Run Trivy scan on Frontend image
        uses: aquasecurity/trivy-action@v0.14.0
        with:
          image-ref: ai-coding-agent-frontend-dev
          ignore-unfixed: true
          format: table
          exit-code: 1
          severity: CRITICAL,HIGH
          trivyignores: .trivyignore
      - name: <PERSON>ert on vulnerabilities
        if: failure()
        run: echo "Critical vulnerabilities found! Please review Trivy scan results."
