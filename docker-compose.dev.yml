# Development Docker Compose for AI Coding Agent
# Provides hot reload for both frontend and backend development

services:
  # Development App (Frontend + Backend with hot reload)
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.consolidated
      target: development
    container_name: ai-coding-agent-app-dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-ai_coding_agent}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-changeme123}@redis:6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - DEBUG=true
      - LOG_LEVEL=debug
      - USER_DATA_ISOLATION=true
      - PYTHONPATH=/app/backend/src
      - REACT_APP_API_URL=/api
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ai-coding-agent-dev-network
    volumes:
      - user_data:/app/user_data
      - app_logs:/var/log
      - ./backend/src:/app/backend/src:rw
      - ./frontend/src:/app/frontend/src:rw
      - ./frontend/public:/app/frontend/public:rw
    # Development resource limits (higher than production for debugging)
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/v1/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 45s
    ports:
      - "8000:8000"  # Direct access to backend API
      - "80:80"      # Access to frontend through nginx
      - "3000:3000"  # React dev server (if needed)

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-ai_coding_agent}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres123}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    networks:
      - agent-net
    volumes:
      - postgres_data:/var/lib/postgresql/data
    mem_limit: 768m
    cpus: 0.8
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ai_coding_agent}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    ports:
      - "5432:5432"  # Direct access to PostgreSQL

  # Redis Cache/Sessions/Rate Limiting
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-changeme123}
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    networks:
      - agent-net
    volumes:
      - redis_data:/data
    mem_limit: 256m
    cpus: 0.3
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ports:
      - "6379:6379"  # Direct access to Redis

# Networks and Volumes
networks:
  agent-net:
    driver: bridge
    name: ai-coding-agent-dev-network

volumes:
  postgres_data:
    name: ai-coding-agent-dev-postgres-data
  redis_data:
    name: ai-coding-agent-dev-redis-data
  user_data:
    name: ai-coding-agent-dev-user-data
  app_logs:
    name: ai-coding-agent-dev-app-logs
