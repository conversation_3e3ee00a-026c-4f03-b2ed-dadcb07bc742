"""
Admin router for model configuration and system management.

Provides endpoints for:
- Model configuration management (local vs cloud)
- Available model detection
- Agent role assignment
- System health monitoring

SECURITY: All endpoints require admin authentication and are audited.
"""

from typing import Dict, List, Optional, Any
import json
import httpx
import logging
from pathlib import Path
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field, field_validator

from ai_coding_agent.config import get_settings
from ai_coding_agent.orchestrator import OrchestratorConfig
from ai_coding_agent.middleware.admin_auth import get_current_admin_user
from ai_coding_agent.middleware.unified_rate_limiting import check_admin_rate_limit, check_auth_rate_limit
from ai_coding_agent.services.secure_config import get_secure_config
from ai_coding_agent.services.audit_service import log_audit_event, AuditCategory, AuditLevel
from ai_coding_agent.services.rule_enforcement import rule_engine
from ai_coding_agent.services.universal_llm import get_universal_llm
from ai_coding_agent.services.container_monitoring import container_monitor
from ai_coding_agent.services.container_manager import get_container_manager
from ai_coding_agent.services.user_data_manager import get_user_data_manager
from ai_coding_agent.models import User

router = APIRouter(prefix="/api/v1/admin", tags=["admin"])
logger = logging.getLogger(__name__)

# Note: Admin audit logging now uses the consolidated log_audit_event function


@router.get("/auth/check")
async def check_admin_auth(
    request: Request,
    _: None = Depends(check_auth_rate_limit),
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Check if current user has admin privileges.

    This endpoint is used by the frontend to verify admin access.
    """
    return {
        "is_superuser": getattr(current_admin, 'is_superuser', False),
        "permissions": ["admin"],  # In future, this could be more granular
        "user_id": current_admin.id,
        "username": current_admin.username
    }

class ModelProvider(BaseModel):
    """Model provider configuration."""
    name: str
    type: str  # "local" or "cloud"
    host: Optional[str] = None
    api_key: Optional[str] = None
    models: List[str] = []
    status: str = "unknown"  # "online", "offline", "error"

class AgentModelConfig(BaseModel):
    """Agent model assignment configuration."""
    agent_name: str
    primary_model: str
    secondary_model: Optional[str] = None
    fallback_model: Optional[str] = None
    provider: str  # "ollama", "openai", "anthropic", etc.

class ModelConfigUpdate(BaseModel):
    """Model configuration update request."""
    agent_configs: List[AgentModelConfig]
    provider_configs: Dict[str, Dict[str, Any]]

class CloudProviderConfig(BaseModel):
    """Cloud provider configuration with validation."""
    provider_name: str = Field(..., pattern="^(openai|anthropic|google)$")
    api_key: str = Field(..., min_length=10, max_length=200)
    base_url: Optional[str] = Field(None, pattern="^https?://.*")
    enabled: bool = True

    @field_validator('api_key')
    @classmethod
    def validate_api_key_format(cls, v, info):
        """Validate API key format based on provider."""
        # Get provider_name from the data being validated
        data = info.data if hasattr(info, 'data') else {}
        provider = data.get('provider_name')

        if provider == 'openai' and not v.startswith('sk-'):
            raise ValueError('OpenAI API keys must start with "sk-"')
        elif provider == 'anthropic' and not v.startswith('sk-ant-'):
            raise ValueError('Anthropic API keys must start with "sk-ant-"')
        elif provider == 'google' and len(v) < 20:
            raise ValueError('Google API keys must be at least 20 characters')

        return v

@router.get("/models/available", response_model=Dict[str, ModelProvider])
async def get_available_models(
    request: Request,
    _: None = Depends(check_admin_rate_limit),
    current_admin: User = Depends(get_current_admin_user)
):
    """Get all available models from local and cloud providers."""
    providers = {}

    # Check Ollama (local models)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
            if response.status_code == 200:
                ollama_data = response.json()
                ollama_models = [model["name"] for model in ollama_data.get("models", [])]
                providers["ollama"] = ModelProvider(
                    name="Ollama",
                    type="local",
                    host="http://localhost:11434",
                    models=ollama_models,
                    status="online" if ollama_models else "offline"
                )
            else:
                providers["ollama"] = ModelProvider(
                    name="Ollama",
                    type="local",
                    host="http://localhost:11434",
                    models=[],
                    status="offline"
                )
    except Exception:
        providers["ollama"] = ModelProvider(
            name="Ollama",
            type="local",
            host="http://localhost:11434",
            models=[],
            status="offline"
        )

    # Add cloud providers (with placeholder models)
    providers["openai"] = ModelProvider(
        name="OpenAI",
        type="cloud",
        models=["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
        status="unknown"  # Will be checked when API key is provided
    )

    providers["anthropic"] = ModelProvider(
        name="Anthropic",
        type="cloud",
        models=["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-2.1"],
        status="unknown"
    )

    providers["google"] = ModelProvider(
        name="Google",
        type="cloud",
        models=["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro"],
        status="unknown"
    )

    return providers

@router.get("/models/current-config")
async def get_current_model_config(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get current model configuration for all agents."""
    config_path = Path(__file__).parent.parent / "models_config.json"

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Extract current agent configurations
        routing = config.get("routing", {})
        agent_configs = []

        for agent_name, agent_config in routing.items():
            agent_configs.append(AgentModelConfig(
                agent_name=agent_name,
                primary_model=agent_config.get("primary", ""),
                secondary_model=agent_config.get("secondary"),
                fallback_model=agent_config.get("fallback"),
                provider="ollama"  # Default for now
            ))

        return {
            "agent_configs": agent_configs,
            "providers": config.get("providers", {}),
            "last_updated": "2024-01-01T00:00:00Z"  # TODO: Add timestamp tracking
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load configuration: {str(e)}")

@router.post("/models/update-config")
async def update_model_config(
    config_update: ModelConfigUpdate,
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Update model configuration for agents."""
    config_path = Path(__file__).parent.parent / "models_config.json"

    try:
        # Load current configuration for audit logging
        secure_config = get_secure_config()
        old_config = secure_config.load_config()

        # Load current configuration
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Update routing configuration
        routing = config.get("routing", {})

        for agent_config in config_update.agent_configs:
            routing[agent_config.agent_name] = {
                "primary": agent_config.primary_model,
                "secondary": agent_config.secondary_model,
                "fallback": agent_config.fallback_model or agent_config.primary_model,
                "task_routing": routing.get(agent_config.agent_name, {}).get("task_routing", {})
            }

        config["routing"] = routing

        # Update provider configurations
        if config_update.provider_configs:
            config["providers"].update(config_update.provider_configs)

        # Save updated configuration
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)

        # Audit log the configuration change (temporarily disabled)
        # admin_audit_logger.log_model_config_change(
        #     user=current_admin,
        #     old_config=old_config,
        #     new_config=config,
        #     ip_address=request.client.host if request.client else None
        # )

        return {"success": True, "message": "Configuration updated successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update configuration: {str(e)}")

@router.post("/providers/cloud/configure")
async def configure_cloud_provider(
    provider_config: CloudProviderConfig,
    request: Request,
    _: None = Depends(check_admin_rate_limit),
    current_admin: User = Depends(get_current_admin_user)
):
    """Configure a cloud provider with API key (securely encrypted)."""
    try:
        # Use secure config manager to encrypt and store API key
        secure_config = get_secure_config()
        success = secure_config.save_provider_config(
            provider_name=provider_config.provider_name,
            api_key=provider_config.api_key,
            base_url=provider_config.base_url,
            enabled=provider_config.enabled
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to save provider configuration")

        # Audit log the provider configuration (without exposing API key)
        masked_details = {
            "provider_name": provider_config.provider_name,
            "base_url": provider_config.base_url,
            "enabled": provider_config.enabled,
            "api_key_length": len(provider_config.api_key),
            "api_key_prefix": provider_config.api_key[:8] + "..." if len(provider_config.api_key) > 8 else "***"
        }

        # admin_audit_logger.log_provider_config_change(
        #     user=current_admin,
        #     provider_name=provider_config.provider_name,
        #     action_type="add",
        #     masked_details=masked_details,
        #     ip_address=request.client.host if request.client else None
        # )

        return {"success": True, "message": f"{provider_config.provider_name} configured successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to configure provider: {str(e)}")

@router.get("/system/health")
async def get_system_health(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get overall system health including model availability."""
    health_status = {
        "ollama": {"status": "unknown", "models_count": 0},
        "cloud_providers": {},
        "agents": {},
        "overall_status": "unknown"
    }

    # Check Ollama
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
            if response.status_code == 200:
                models = response.json().get("models", [])
                health_status["ollama"] = {
                    "status": "online",
                    "models_count": len(models),
                    "models": [m["name"] for m in models]
                }
            else:
                health_status["ollama"]["status"] = "offline"
    except Exception:
        health_status["ollama"]["status"] = "offline"

    # Check agent configurations
    try:
        config = OrchestratorConfig()
        routing = config.get_routing_config()

        for agent_name, agent_config in routing.items():
            primary_model = agent_config.get("primary")
            health_status["agents"][agent_name] = {
                "primary_model": primary_model,
                "configured": bool(primary_model),
                "provider": "ollama"  # Default assumption
            }
    except Exception as e:
        health_status["agents"] = {"error": str(e)}

    # Determine overall status
    ollama_ok = health_status["ollama"]["status"] == "online"
    agents_configured = len(health_status["agents"]) > 0

    if ollama_ok and agents_configured:
        health_status["overall_status"] = "healthy"
    elif agents_configured:
        health_status["overall_status"] = "degraded"
    else:
        health_status["overall_status"] = "unhealthy"

    return health_status

@router.post("/models/test-connection")
async def test_model_connection(
    model_name: str,
    request: Request,
    provider: str = "ollama",
    current_admin: User = Depends(get_current_admin_user)
):
    """Test connection to a specific model."""
    if provider == "ollama":
        try:
            async with httpx.AsyncClient() as client:
                # Test if model is available
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json={"model": model_name, "prompt": "test", "stream": False},
                    timeout=10.0
                )

                if response.status_code == 200:
                    return {"success": True, "message": f"Model {model_name} is available"}
                else:
                    return {"success": False, "message": f"Model {model_name} not available"}
        except Exception as e:
            return {"success": False, "message": f"Connection failed: {str(e)}"}

@router.get("/security/rule-violations")
async def get_rule_violations(
    request: Request,
    _: None = Depends(check_admin_rate_limit),
    current_admin: User = Depends(get_current_admin_user)
):
    """Get summary of rule violations for security monitoring."""
    try:
        violation_summary = rule_engine.get_violation_summary()

        # Audit log the security monitoring access (temporarily disabled)
        # admin_audit_logger.log_security_event(
        #     user=current_admin,
        #     event_type="rule_violation_monitoring",
        #     details={
        #         "total_violations": violation_summary.get("total_violations", 0),
        #         "access_time": datetime.now(timezone.utc).isoformat()
        #     },
        #     ip_address=request.client.host if request.client else None
        # )

        return {
            "success": True,
            "data": violation_summary,
            "message": "Rule violation summary retrieved"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get rule violations: {str(e)}")


@router.get("/monitoring/overview")
async def get_monitoring_overview(
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    Get system monitoring overview.

    Returns overall system health and resource usage statistics.
    """
    try:
        overview = container_monitor.get_system_overview()

        # Log admin monitoring access
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} accessed monitoring overview",
            user_id=str(current_admin.id),
            action="monitoring_overview_accessed",
            total_containers=overview.get("total_containers", 0),
            total_alerts=overview.get("total_active_alerts", 0)
        )

        return overview

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get monitoring overview: {str(e)}")


@router.get("/monitoring/container/{container_id}")
async def get_container_monitoring(
    container_id: str,
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    Get detailed monitoring data for a specific container.

    Returns resource usage, alerts, and status for the specified container.
    """
    try:
        container_status = container_monitor.get_container_status(container_id)

        # Log admin container monitoring access
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} accessed container monitoring for {container_id}",
            user_id=str(current_admin.id),
            action="container_monitoring_accessed",
            container_id=container_id,
            active_alerts=container_status.get("active_alerts", 0)
        )

        return container_status

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get container monitoring: {str(e)}")


@router.post("/monitoring/start")
async def start_monitoring(
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    Start the container monitoring service.

    Begins monitoring all containers for resource usage and alerts.
    """
    try:
        if container_monitor.monitoring_active:
            return {"message": "Monitoring is already active", "status": "active"}

        # Start monitoring in background
        import asyncio
        asyncio.create_task(container_monitor.start_monitoring())

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} started container monitoring",
            user_id=str(current_admin.id),
            action="monitoring_started",
            service="container_monitoring"
        )

        return {"message": "Container monitoring started", "status": "started"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")


@router.post("/monitoring/stop")
async def stop_monitoring(
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    Stop the container monitoring service.

    Stops monitoring all containers and clears active monitoring tasks.
    """
    try:
        await container_monitor.stop_monitoring()

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.WARNING,
            message=f"Admin {current_admin.email} stopped container monitoring",
            user_id=str(current_admin.id),
            action="monitoring_stopped",
            service="container_monitoring"
        )

        return {"message": "Container monitoring stopped", "status": "stopped"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")


# Container Management Endpoints
@router.get("/containers/list")
async def list_all_containers(
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    List all user containers with detailed information.

    Returns comprehensive information about all active user containers
    including resource usage, status, and metadata.
    """
    try:
        container_manager = get_container_manager()
        containers = container_manager.get_all_user_containers()

        # Enhance container data with resource usage
        enhanced_containers = []
        for container in containers:
            container_data = {
                "id": container.container_id,
                "name": container.container_name,
                "userId": container.user_id,
                "status": container.status.value,
                "projectType": container.project_type.value,
                "createdAt": container.created_at.isoformat(),
                "lastAccessed": container.last_accessed.isoformat(),
                "port": container.port,
                "previewUrl": container.preview_url,
                "resourceUsage": {
                    "cpuPercent": 0,
                    "memoryPercent": 0,
                    "memoryUsageMB": 0,
                    "memoryLimitMB": 512,  # Default limit
                    "diskUsageMB": 0
                }
            }

            # Try to get real-time resource usage
            try:
                resource_usage = container_monitor.get_container_status(container.container_id)
                if resource_usage and "current_usage" in resource_usage:
                    current_usage = resource_usage["current_usage"]
                    container_data["resourceUsage"] = {
                        "cpuPercent": current_usage.get("cpu_percent", 0),
                        "memoryPercent": current_usage.get("memory_percent", 0),
                        "memoryUsageMB": current_usage.get("memory_usage_mb", 0),
                        "memoryLimitMB": 512,  # Default limit
                        "diskUsageMB": current_usage.get("disk_usage_mb", 0)
                    }
            except Exception as e:
                logger.warning(f"Failed to get resource usage for container {container.container_id}: {e}")

            enhanced_containers.append(container_data)

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} listed containers",
            user_id=str(current_admin.id),
            action="containers_listed",
            container_count=len(enhanced_containers)
        )

        return {"containers": enhanced_containers}

    except Exception as e:
        logger.error(f"Failed to list containers: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list containers: {str(e)}")


@router.post("/containers/{container_id}/start")
async def start_container(
    container_id: str,
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """Start a stopped container."""
    try:
        container_manager = get_container_manager()

        # Find the container by ID
        user_containers = container_manager.get_all_user_containers()
        target_container = None
        for container in user_containers:
            if container.container_id == container_id:
                target_container = container
                break

        if not target_container:
            raise HTTPException(status_code=404, detail="Container not found")

        # Start the container (implementation depends on container manager)
        # This is a placeholder - actual implementation would depend on Docker SDK

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} started container {container_id}",
            user_id=str(current_admin.id),
            action="container_started",
            container_id=container_id,
            target_user_id=target_container.user_id
        )

        return {"message": "Container start initiated", "container_id": container_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start container {container_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start container: {str(e)}")


@router.post("/containers/{container_id}/stop")
async def stop_container(
    container_id: str,
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """Stop a running container."""
    try:
        container_manager = get_container_manager()

        # Find the container by ID
        user_containers = container_manager.get_all_user_containers()
        target_container = None
        for container in user_containers:
            if container.container_id == container_id:
                target_container = container
                break

        if not target_container:
            raise HTTPException(status_code=404, detail="Container not found")

        # Stop the container
        # Using cleanup_user_container as stop_user_container doesn't exist
        success = await container_manager.cleanup_user_container(target_container.user_id)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to stop container")

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} stopped container {container_id}",
            user_id=str(current_admin.id),
            action="container_stopped",
            container_id=container_id,
            target_user_id=target_container.user_id
        )

        return {"message": "Container stopped successfully", "container_id": container_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop container {container_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop container: {str(e)}")


@router.post("/containers/{container_id}/restart")
async def restart_container(
    container_id: str,
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """Restart a container."""
    try:
        container_manager = get_container_manager()

        # Find the container by ID
        user_containers = container_manager.get_all_user_containers()
        target_container = None
        for container in user_containers:
            if container.container_id == container_id:
                target_container = container
                break

        if not target_container:
            raise HTTPException(status_code=404, detail="Container not found")

        # Restart the container (stop then start)
        # Using cleanup_user_container as stop_user_container doesn't exist
        await container_manager.cleanup_user_container(target_container.user_id)
        # Note: Actual restart implementation would depend on container manager capabilities

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} restarted container {container_id}",
            user_id=str(current_admin.id),
            action="container_restarted",
            container_id=container_id,
            target_user_id=target_container.user_id
        )

        return {"message": "Container restart initiated", "container_id": container_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to restart container {container_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to restart container: {str(e)}")


@router.post("/containers/{container_id}/remove")
async def remove_container(
    container_id: str,
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """Remove a container and its data."""
    try:
        container_manager = get_container_manager()

        # Find the container by ID
        user_containers = container_manager.get_all_user_containers()
        target_container = None
        for container in user_containers:
            if container.container_id == container_id:
                target_container = container
                break

        if not target_container:
            raise HTTPException(status_code=404, detail="Container not found")

        # Remove the container
        success = await container_manager.cleanup_user_container(target_container.user_id)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to remove container")

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.WARNING,
            message=f"Admin {current_admin.email} removed container {container_id}",
            user_id=str(current_admin.id),
            action="container_removed",
            container_id=container_id,
            target_user_id=target_container.user_id
        )

        return {"message": "Container removed successfully", "container_id": container_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove container {container_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to remove container: {str(e)}")


# User Data Management Endpoints
@router.get("/user-data/directories")
async def list_user_directories(
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    List all user data directories for monitoring.

    Returns information about user data storage usage and organization.
    """
    try:
        user_data_manager = get_user_data_manager()
        directories = user_data_manager.list_user_directories()

        # Convert to serializable format
        directory_data = []
        for dir_info in directories:
            directory_data.append({
                "userId": dir_info.user_id,
                "directoryPath": dir_info.directory_path,
                "createdAt": dir_info.created_at.isoformat(),
                "lastAccessed": dir_info.last_accessed.isoformat(),
                "sizeBytes": dir_info.size_bytes,
                "fileCount": dir_info.file_count,
                "permissions": dir_info.permissions,
                "isIsolated": dir_info.is_isolated
            })

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} listed user directories",
            user_id=str(current_admin.id),
            action="user_directories_listed",
            directory_count=len(directory_data)
        )

        return {"directories": directory_data}

    except Exception as e:
        logger.error(f"Failed to list user directories: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list user directories: {str(e)}")


@router.post("/user-data/{user_id}/create-directory")
async def create_user_directory(
    user_id: str,
    current_admin: User = Depends(get_current_admin_user),
    _: None = Depends(check_admin_rate_limit)
):
    """
    Create a user data directory (admin only).

    Creates an isolated directory structure for the specified user.
    """
    try:
        user_data_manager = get_user_data_manager()
        directory_info = user_data_manager.create_user_directory(user_id)

        # Log admin action
        log_audit_event(
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO,
            message=f"Admin {current_admin.email} created directory for user {user_id}",
            user_id=str(current_admin.id),
            action="user_directory_created_by_admin",
            target_user_id=user_id,
            directory_path=directory_info.directory_path
        )

        return {
            "message": "User directory created successfully",
            "directory": {
                "userId": directory_info.user_id,
                "directoryPath": directory_info.directory_path,
                "createdAt": directory_info.created_at.isoformat(),
                "sizeBytes": directory_info.size_bytes,
                "fileCount": directory_info.file_count,
                "isIsolated": directory_info.is_isolated
            }
        }

    except Exception as e:
        logger.error(f"Failed to create user directory for {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create user directory: {str(e)}")

@router.get("/models/cloud/available")
async def get_available_cloud_models(
    request: Request,
    _: None = Depends(check_admin_rate_limit),
    current_admin: User = Depends(get_current_admin_user)
):
    """Get available models from all providers (admin only)."""
    try:
        universal_llm = get_universal_llm()
        available_models = await universal_llm.get_available_models()

        # Audit log the model access (temporarily disabled)
        # admin_audit_logger.log_admin_action(
        #     action=AuditAction.MODEL_CONFIG_UPDATE,
        #     user=current_admin,
        #     details={
        #         "action_type": "view_available_models",
        #         "local_models_count": len(available_models.get("ollama", [])),
        #         "cloud_providers_count": len(available_models.get("cloud", {}))
        #     },
        #     severity=AuditSeverity.INFO,
        #     ip_address=request.client.host if request.client else None
        # )

        return {
            "success": True,
            "data": available_models,
            "message": "Available models retrieved"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get available models: {str(e)}")

@router.post("/models/cloud/routing/update")
async def update_cloud_model_routing(
    routing_config: Dict[str, Any],
    request: Request,
    _: None = Depends(check_admin_rate_limit),
    current_admin: User = Depends(get_current_admin_user)
):
    """Update model routing to use cloud models (admin only)."""
    try:
        config_path = Path(__file__).parent.parent / "models_config.json"

        # Load current config
        with open(config_path, 'r') as f:
            current_config = json.load(f)

        # Backup current routing
        old_routing = current_config.get("routing", {})

        # Update routing with cloud models
        current_config["routing"] = routing_config

        # Save updated config
        with open(config_path, 'w') as f:
            json.dump(current_config, f, indent=2)

        # Audit log the critical configuration change (temporarily disabled)
        # admin_audit_logger.log_admin_action(
        #     action=AuditAction.MODEL_CONFIG_UPDATE,
        #     user=current_admin,
        #     details={
        #         "action_type": "cloud_model_routing_update",
        #         "old_routing": old_routing,
        #         "new_routing": routing_config,
        #         "affected_agents": list(routing_config.keys())
        #     },
        #     severity=AuditSeverity.CRITICAL,
        #     ip_address=request.client.host if request.client else None
        # )

        return {
            "success": True,
            "message": "Cloud model routing updated successfully",
            "affected_agents": list(routing_config.keys())
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update routing: {str(e)}")

    else:
        # TODO: Implement cloud provider testing
        return {"success": False, "message": f"Testing for {provider} not implemented yet"}
