"""
Quality Comparison Test for Top 3 Models

Compare the actual response quality between starcoder2:3b, llama3.2:3b,
and mistral:7b-instruct-q4_0 for Architect Agent tasks. Focus on content quality, not speed.
"""

import asyncio
import time
import subprocess
import sys
import os
from typing import Dict, Any

# Top 3 models for quality comparison
TOP_MODELS = [
    "starcoder2:3b",             # Winner by performance metrics
    "llama3.2:3b",               # Second best, good balance
    "mistral:7b-instruct-q4_0",  # Current model baseline
]

# Detailed Architect Agent test scenarios
QUALITY_TESTS = [
    {
        "name": "Project Roadmap Creation",
        "prompt": """Create a detailed 4-phase roadmap for building a task management application with the following requirements:
- User authentication with email/password and Google OAuth
- Real-time collaboration (multiple users editing tasks)
- Mobile responsive design
- File attachments for tasks
- Role-based permissions (admin, manager, member)
- Integration with calendar systems

For each phase, specify:
1. Technical components to implement
2. Estimated timeline
3. Dependencies between phases
4. Risk mitigation strategies""",
        "quality_criteria": [
            "Clear phase breakdown",
            "Technical depth",
            "Realistic timelines",
            "Risk awareness",
            "Dependencies mapping"
        ]
    },
    {
        "name": "Architecture Decision Making",
        "prompt": """We're building a chat application that needs to handle 10,000 concurrent users with real-time messaging.

Compare these architecture options and recommend the best approach:

Option A: Monolithic Node.js + Socket.io + MongoDB
Option B: Microservices (Auth, Chat, Notifications) + Redis + PostgreSQL + WebSockets
Option C: Event-driven architecture with Kafka + Multiple databases

Consider:
- Scalability requirements
- Development complexity
- Operational overhead
- Cost implications
- Team expertise requirements

Provide a detailed technical recommendation with justification.""",
        "quality_criteria": [
            "Technical analysis depth",
            "Scalability considerations",
            "Trade-off evaluation",
            "Clear recommendation",
            "Practical justification"
        ]
    },
    {
        "name": "Agent Coordination Strategy",
        "prompt": """Design a coordination strategy for 5 AI agents working together on a web application project:

1. Frontend Agent (React/UI development)
2. Backend Agent (API/business logic)
3. Database Agent (schema design/optimization)
4. DevOps Agent (deployment/infrastructure)
5. QA Agent (testing/quality assurance)

The project is: "Build an e-commerce platform with inventory management"

Specify:
- How agents should hand off work between each other
- What information each agent needs from others
- Conflict resolution when agents disagree
- Quality gates and review processes
- Timeline coordination and dependencies""",
        "quality_criteria": [
            "Clear coordination workflow",
            "Inter-agent communication",
            "Conflict resolution",
            "Quality assurance",
            "Practical implementation"
        ]
    }
]

def safe_subprocess_run(cmd: list, timeout: int = 120) -> Dict[str, Any]:
    """Safely run subprocess with proper Unicode handling."""
    try:
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=timeout,
            env=env,
            shell=False
        )

        def clean_text(text: str) -> str:
            if not text:
                return ""
            return ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')

        return {
            "success": result.returncode == 0,
            "stdout": clean_text(result.stdout),
            "stderr": clean_text(result.stderr),
            "returncode": result.returncode
        }

    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"Command timed out after {timeout} seconds",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"Error: {str(e)}",
            "returncode": -4
        }

async def test_model_quality(model: str, test: Dict[str, Any]) -> Dict[str, Any]:
    """Test model quality with detailed response capture."""
    print(f"\n🤖 Testing {model} - {test['name']}")
    print("=" * 80)

    start_time = time.time()

    cmd = [
        "ollama", "run", model,
        f"You are an expert AI Architect Agent. {test['prompt']}\n\nProvide a comprehensive, well-structured response."
    ]

    result = safe_subprocess_run(cmd, timeout=120)
    end_time = time.time()

    response_time = end_time - start_time

    if result["success"]:
        response = result["stdout"].strip()

        print(f"⏱️  Response Time: {response_time:.1f}s")
        print(f"📝 Response Length: {len(response)} characters")
        print(f"📊 Word Count: {len(response.split())} words")
        print("\n📋 FULL RESPONSE:")
        print("-" * 80)
        print(response)
        print("-" * 80)

        return {
            "success": True,
            "response": response,
            "response_time": response_time,
            "word_count": len(response.split()),
            "char_count": len(response)
        }
    else:
        print(f"❌ Failed: {result['stderr']}")
        return {
            "success": False,
            "error": result["stderr"],
            "response_time": response_time
        }

async def compare_quality():
    """Compare response quality between top models."""
    print("🎯 QUALITY COMPARISON: Top 2 Models")
    print("🔍 Focus: Architect Agent Response Quality")
    print("=" * 80)

    all_results = {}

    for test in QUALITY_TESTS:
        print(f"\n\n🧪 TEST: {test['name']}")
        print("🎯 Quality Criteria:", ", ".join(test['quality_criteria']))
        print("=" * 80)

        test_results = {}

        for model in TOP_MODELS:
            result = await test_model_quality(model, test)
            test_results[model] = result

            # Wait between models to avoid overloading
            if model != TOP_MODELS[-1]:
                print("\n⏳ Waiting 5 seconds before next model...")
                await asyncio.sleep(5)

        all_results[test['name']] = test_results

    # Summary comparison
    print("\n\n🏆 QUALITY COMPARISON SUMMARY")
    print("=" * 80)

    for test_name, results in all_results.items():
        print(f"\n📝 {test_name}:")

        for model, result in results.items():
            if result["success"]:
                print(f"  {model:15} | {result['response_time']:5.1f}s | {result['word_count']:4d} words | {result['char_count']:5d} chars")
            else:
                print(f"  {model:15} | FAILED: {result.get('error', 'Unknown error')}")

    print(f"\n💡 QUALITY EVALUATION INSTRUCTIONS:")
    print("Compare the responses above based on:")
    print("- Technical depth and accuracy")
    print("- Structured thinking and organization")
    print("- Practical applicability")
    print("- Completeness of requirements coverage")
    print("- Professional clarity and communication")

    return all_results

if __name__ == "__main__":
    print("🎯 Starting Quality Comparison Test")
    print("📊 Models: starcoder2:3b vs llama3.2:3b")
    print("⚖️  Evaluating: Architect Agent Response Quality")
    print()

    try:
        results = asyncio.run(compare_quality())
        print(f"\n✅ Quality comparison completed!")
        print("👀 Review the responses above to determine which model provides better Architect Agent quality.")

    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Error during quality test: {str(e)}")
        sys.exit(1)
