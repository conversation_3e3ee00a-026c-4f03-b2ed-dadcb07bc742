# Docker Container Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Issue 1: Missing Scripts Directory in Backend Container

**Symptoms:**
- ❌ Missing Scripts directory: /app/scripts
- Import errors when running migration scripts

**Solution:**
```bash
# 1. Verify scripts directory exists locally
ls -la backend/scripts/

# 2. Check Dockerfile COPY command
grep "COPY scripts" backend/Dockerfile

# 3. Rebuild backend container
docker-compose build --no-cache app
```

### Issue 2: Missing Tests Directory

**Symptoms:**
- ❌ Missing Tests directory: /app/tests
- Cannot run test migration scripts

**Solution:**
```bash
# 1. Verify tests directory exists
ls -la backend/tests/

# 2. Ensure Dockerfile copies tests
# Should have: COPY tests/ ./tests/

# 3. Rebuild if missing
docker-compose build --no-cache app
```

### Issue 3: Import Errors in Backend

**Symptoms:**
- `ModuleNotFoundError: No module named 'ai_coding_agent'`
- Python import failures

**Solution:**
```bash
# 1. Check PYTHONPATH in container
docker-compose exec app env | findstr PYTHONPATH

# 2. Verify file structure in container
docker-compose exec app ls -la /app/

# 3. Check if main module exists
docker-compose exec app ls -la /app/ai_coding_agent/

# 4. Test Python imports
docker-compose exec app python -c "import ai_coding_agent; print('✅ Import successful')"
```

### Issue 4: Frontend Assets Missing

**Symptoms:**
- `❌ No JavaScript files found`
- `❌ No CSS files found`
- Blank page when accessing frontend

**Solution:**
```bash
# 1. Check if build stage completed
docker-compose logs frontend | grep "build"

# 2. Verify build directory exists
docker-compose exec frontend ls -la /usr/share/nginx/html/

# 3. Check for static assets
docker-compose exec frontend find /usr/share/nginx/html -name "*.js" -o -name "*.css"

# 4. Rebuild frontend with verbose output
docker-compose build --no-cache frontend --progress=plain
```

### Issue 5: Environment Variables Missing

**Symptoms:**
- `❌ SECRET_KEY missing in .env`
- Configuration errors on startup

**Solution:**
```bash
# 1. Create .env file with required variables
cat > .env << EOF
SECRET_KEY=your-secret-key-here-minimum-32-characters-long
CONFIG_ENCRYPTION_KEY=your-encryption-key-minimum-32-characters
DB_PASSWORD=your-secure-database-password
EOF

# 2. Restart containers to pick up new environment
docker-compose down
docker-compose up -d
```

### Issue 6: Volume Mount Permissions

**Symptoms:**
- Permission denied errors
- Cannot write to mounted volumes

**Solution:**
```bash
# 1. Check volume permissions
docker-compose exec backend ls -la /app/ | grep -E "(logs|user-projects|uploads)"

# 2. Fix permissions if needed
sudo chown -R 1000:1000 ./user-projects
sudo chown -R 1000:1000 ./data/logs

# 3. Restart backend container
docker-compose restart backend
```

## 🔧 Debugging Commands

### Container Inspection
```bash
# List all containers
docker-compose ps

# Check container logs
docker-compose logs backend
docker-compose logs frontend

# Execute commands in container
docker-compose exec backend bash
docker-compose exec frontend sh

# Check container resource usage
docker stats
```

### File Structure Verification
```bash
# Backend file structure
docker-compose exec backend find /app -type f -name "*.py" | head -10

# Frontend file structure
docker-compose exec frontend ls -la /usr/share/nginx/html/

# Check specific files
docker-compose exec backend test -f /app/scripts/test_migration.py && echo "✅ Found" || echo "❌ Missing"
```

### Network and Connectivity
```bash
# Test internal network connectivity
docker-compose exec backend ping postgres
docker-compose exec backend ping redis

# Test external connectivity
curl http://localhost:8000/api/v1/health
curl http://localhost:3000
```

## 🚀 Complete Rebuild Process

If you encounter multiple issues, perform a complete rebuild:

```bash
# 1. Stop and remove everything
docker-compose down --volumes --remove-orphans

# 2. Remove all images
docker-compose build --no-cache

# 3. Clean Docker system
docker system prune -af --volumes

# 4. Rebuild and start
docker-compose up -d --build

# 5. Verify everything
bash scripts/verify_container_files.sh
```

## 📊 Success Criteria Checklist

Your containers are properly set up when:

- [ ] ✅ All verification script checks pass
- [ ] ✅ `python scripts/test_migration.py` works in backend container
- [ ] ✅ Frontend serves React app on port 3000
- [ ] ✅ Backend API responds on port 8000
- [ ] ✅ Redis connection works from backend
- [ ] ✅ PostgreSQL connection works from backend
- [ ] ✅ Volume mounts persist data
- [ ] ✅ All health checks pass
- [ ] ✅ No permission errors in logs

## 🆘 Emergency Recovery

If containers are completely broken:

```bash
# Nuclear option - remove everything and start fresh
docker-compose down --volumes --remove-orphans
docker system prune -af --volumes
docker volume prune -f

# Rebuild from scratch
bash scripts/build_and_verify.sh
```
