# AI Coding Agent - GitHub Configuration Documentation

This directory contains GitHub-specific configuration files and prompts for the AI Coding Agent project.

## 📋 Documentation Hierarchy

### 🏗️ Primary Architecture Reference
- **[docs/.copilot-rules.md](../docs/.copilot-rules.md)** - **AUTHORITATIVE** architectural rules and development guidelines
  - **Hybrid container architecture**: Consolidated platform + container-per-user projects
  - LTKB integration guidelines
  - Multi-agent orchestration rules
  - Security and performance requirements

### 🤖 AI Agent Configuration
- **[prompts/ai-agent-config.prompt.md](prompts/ai-agent-config.prompt.md)** - Comprehensive AI agent configuration
  - Specialized agent configurations (Architect, Frontend, Backend, Shell, Issue Fix)
  - Model assignments and parameters
  - Multi-user settings and resource limits
  - LTKB and STPM integration

### 🔒 Security Guidelines
- **[prompts/docker-security.prompt.md](prompts/docker-security.prompt.md)** - Docker and AI security review assistant
  - Container security best practices
  - AI-specific security requirements
  - Multi-user security considerations
  - Security review checklists

### 🏢 Multi-User Architecture
- **[prompts/multiuser-architecture.prompt.md](prompts/multiuser-architecture.prompt.md)** - Multi-user architecture patterns
  - **Hybrid deployment strategies**: Platform consolidation + user container isolation
  - User isolation and resource management
  - Scalability and performance optimization
  - Database and caching strategies

### 🐳 Container Standards
- **[copilot-instructions.md](copilot-instructions.md)** - Docker container naming and deployment standards
  - Container naming conventions
  - Docker Compose best practices
  - Replica management guidelines

### 📚 Legacy Reference
- **[copilot-rules.md](copilot-rules.md)** - Legacy development rules (being phased out)
  - Historical reference only
  - Redirects to current documentation
  - GitHub Copilot integration compatibility

## 🔄 File Relationships

```mermaid
graph TD
    A[docs/.copilot-rules.md] --> B[Primary Architecture]
    A --> C[Development Guidelines]
    A --> D[Security Rules]

    E[prompts/ai-agent-config.prompt.md] --> F[Agent Configuration]
    E --> G[Model Management]
    E --> H[Multi-User Settings]

    I[prompts/docker-security.prompt.md] --> J[Security Review]
    I --> K[Container Security]
    I --> L[AI Security]

    M[prompts/multiuser-architecture.prompt.md] --> N[Architecture Patterns]
    M --> O[Scalability]
    M --> P[User Isolation]

    Q[copilot-instructions.md] --> R[Naming Standards]
    Q --> S[Deployment Practices]

    T[copilot-rules.md] --> U[Legacy Reference]

    B --> F
    B --> J
    B --> N
    C --> G
    D --> K
```

## 🎯 Usage Guidelines

### For New Development
1. **Start with**: `docs/.copilot-rules.md` for architectural guidance
2. **Configure AI agents**: Use `prompts/ai-agent-config.prompt.md`
3. **Security review**: Apply `prompts/docker-security.prompt.md`
4. **Multi-user features**: Reference `prompts/multiuser-architecture.prompt.md`
5. **Container deployment**: Follow `copilot-instructions.md`

### For GitHub Copilot Integration
- Primary rules: `docs/.copilot-rules.md`
- Legacy compatibility: `copilot-rules.md` (redirects to current docs)
- Naming standards: `copilot-instructions.md`

### For Security Reviews
- Use `prompts/docker-security.prompt.md` as review checklist
- Apply AI-specific security requirements
- Validate multi-user isolation

### For Architecture Decisions
- Reference `docs/.copilot-rules.md` for current architecture
- Use `prompts/multiuser-architecture.prompt.md` for scaling patterns
- Apply consolidated container principles

## 🔧 Maintenance

### File Precedence (Highest to Lowest)
1. `docs/.copilot-rules.md` - Current authoritative source
2. `prompts/ai-agent-config.prompt.md` - AI configuration authority
3. `prompts/docker-security.prompt.md` - Security authority
4. `prompts/multiuser-architecture.prompt.md` - Architecture patterns
5. `copilot-instructions.md` - Naming and deployment standards
6. `copilot-rules.md` - Legacy reference only

### Update Process
1. **Primary changes**: Update `docs/.copilot-rules.md` first
2. **Propagate changes**: Update related prompt files
3. **Validate consistency**: Ensure no conflicts between files
4. **Test integration**: Verify GitHub Copilot compatibility

### Version Control
- **Last Updated**: 2025-01-18
- **Architecture Version**: Consolidated Container v2.0
- **LTKB Integration**: Phase A1 (Core Plumbing)

## 🚨 Important Notes

- **Never use conflicting guidance** between files
- **Always prioritize** `docs/.copilot-rules.md` for architectural decisions
- **Maintain consistency** across all configuration files
- **Update cross-references** when modifying any file
- **Test changes** with actual GitHub Copilot integration

For questions or conflicts, refer to the primary architecture document: `docs/.copilot-rules.md`
