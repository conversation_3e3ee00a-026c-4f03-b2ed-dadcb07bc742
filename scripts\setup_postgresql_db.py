#!/usr/bin/env python3
"""
PostgreSQL database setup for roadmap system.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError, ProgrammingError

from backend.src.ai_coding_agent import models

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def setup_postgresql_database():
    """Set up PostgreSQL database for the roadmap system."""
    print("🐘 Setting up PostgreSQL database for roadmap system...")

    # Database connection parameters
    db_params = {
        "host": os.getenv("DB_HOST", "localhost"),
        "port": os.getenv("DB_PORT", "5432"),
        "user": os.getenv("DB_USER", "postgres"),
        "password": os.getenv("DB_PASSWORD", ""),
        "name": os.getenv("DB_NAME", "ai_coding_agent")
    }

    print(f"📊 Connecting to PostgreSQL at {db_params['host']}:{db_params['port']}")

    # First, connect to the default 'postgres' database to create our target database
    admin_db_url = f"postgresql://{db_params['user']}:{db_params['password']}@{db_params['host']}:{db_params['port']}/postgres"

    try:
        print("🔧 Connecting to default postgres database...")
        admin_engine = create_engine(admin_db_url, echo=False, isolation_level="AUTOCOMMIT")

        with admin_engine.connect() as conn:
            # Check if database exists
            result = conn.execute(text("SELECT 1 FROM pg_database WHERE datname = :dbname"), {"dbname": db_params['name']})
            if result.fetchone():
                print(f"📋 Database '{db_params['name']}' already exists")
            else:
                print(f"🏗️ Creating database '{db_params['name']}'...")
                conn.execute(text(f'CREATE DATABASE "{db_params["name"]}"'))
                print(f"✅ Database '{db_params['name']}' created successfully!")

        admin_engine.dispose()

        # Now connect to our target database
        target_db_url = f"postgresql://{db_params['user']}:{db_params['password']}@{db_params['host']}:{db_params['port']}/{db_params['name']}"
        print(f"🔗 Connecting to target database '{db_params['name']}'...")
        engine = create_engine(target_db_url, echo=True)

        # Create all tables
        print("📋 Creating tables...")
        models.Base.metadata.create_all(engine)

        print("✅ PostgreSQL database setup successful!")
        print("📋 Tables created:")
        print("  - projects")
        print("  - roadmaps")
        print("  - phases")
        print("  - steps")
        print("  - tasks")

        return engine

    except OperationalError as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("\n💡 To fix this, you need to:")
        print("1. Install PostgreSQL: https://www.postgresql.org/download/")
        print("2. Start PostgreSQL service")
        print("3. Create database:")
        print(f"   createdb {db_params['name']}")
        print("4. Set environment variables (optional):")
        print("   DB_HOST=localhost")
        print("   DB_PORT=5432")
        print("   DB_USER=postgres")
        print("   DB_PASSWORD=your_password")
        print("   DB_NAME=ai_coding_agent")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None


if __name__ == "__main__":
    engine = setup_postgresql_database()
    if engine:
        print("\n🎉 PostgreSQL database is ready!")
    else:
        print("\n❌ PostgreSQL setup failed. Try SQLite instead: python setup_sqlite_db.py")
