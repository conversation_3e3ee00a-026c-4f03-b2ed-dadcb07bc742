apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-coding-agent-backend
  namespace: ai-coding-agent
  labels:
    app: ai-coding-agent-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-coding-agent-backend
  template:
    metadata:
      labels:
        app: ai-coding-agent-backend
    spec:
      serviceAccountName: backend-sa
      containers:
        - name: backend
          image: ai-coding-agent-backend:latest
          resources:
            requests:
              cpu: "500m"
              memory: "512Mi"
            limits:
              cpu: "1"
              memory: "1Gi"
          envFrom:
            - configMapRef:
                name: backend-config
            - secretRef:
                name: backend-secrets
          ports:
            - containerPort: 8000
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: ai-coding-agent-backend
  namespace: ai-coding-agent
spec:
  selector:
    app: ai-coding-agent-backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: LoadBalancer
