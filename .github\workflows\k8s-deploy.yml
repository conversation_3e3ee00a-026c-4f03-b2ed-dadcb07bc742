name: Kubernetes CI/CD Pipeline

on:
  push:
    branches:
      - main
      - staging
  pull_request:
    branches:
      - main
      - staging

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Log in to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/ai-coding-agent-backend:${{ github.sha }}
      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/ai-coding-agent-frontend:${{ github.sha }}

  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-push
    steps:
      - name: Run Trivy security scan on backend image
        uses: aquasecurity/trivy-action@v0.14.0
        with:
          image-ref: ${{ secrets.DOCKERHUB_USERNAME }}/ai-coding-agent-backend:${{ github.sha }}
      - name: Run Trivy security scan on frontend image
        uses: aquasecurity/trivy-action@v0.14.0
        with:
          image-ref: ${{ secrets.DOCKERHUB_USERNAME }}/ai-coding-agent-frontend:${{ github.sha }}

  deploy-staging:
    runs-on: ubuntu-latest
    needs: security-scan
    environment: staging
    steps:
      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: 'latest'
      - name: Deploy to staging cluster
        run: |
          helm upgrade --install ai-coding-agent ./infrastructure/k8s/helm-chart \
            --namespace ai-coding-agent --create-namespace \
            --set backend.image.tag=${{ github.sha }} \
            --set frontend.image.tag=${{ github.sha }}
      - name: Run integration tests
        run: |
          kubectl run integration-test --image=${{ secrets.DOCKERHUB_USERNAME }}/ai-coding-agent-backend:${{ github.sha }} --namespace ai-coding-agent -- /bin/sh -c "pytest tests/integration"
      - name: Monitor deployment health
        run: |
          kubectl get pods -n ai-coding-agent
          kubectl get hpa -n ai-coding-agent
          kubectl get events -n ai-coding-agent

  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    environment:
      name: production
      url: ${{ secrets.PRODUCTION_CLUSTER_URL }}
    steps:
      - name: Wait for approval
        uses: peter-evans/workflow-dispatch@v2
        with:
          inputs: {}
      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: 'latest'
      - name: Deploy to production cluster
        run: |
          helm upgrade --install ai-coding-agent ./infrastructure/k8s/helm-chart \
            --namespace ai-coding-agent --create-namespace \
            --set backend.image.tag=${{ github.sha }} \
            --set frontend.image.tag=${{ github.sha }}
      - name: Monitor deployment health
        run: |
          kubectl get pods -n ai-coding-agent
          kubectl get hpa -n ai-coding-agent
          kubectl get events -n ai-coding-agent
      - name: Rollback on failure
        if: failure()
        run: |
          helm rollback ai-coding-agent
      - name: Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      - name: Email notification
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: ${{ secrets.SMTP_PORT }}
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: "Kubernetes Deployment Status"
          to: ${{ secrets.NOTIFY_EMAIL }}
          body: "Deployment status: ${{ job.status }}"
