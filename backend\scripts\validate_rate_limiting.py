#!/usr/bin/env python3
"""
Rate Limiting Configuration Validation and Testing Script.

This script validates the unified rate limiting system configuration
and provides comprehensive testing capabilities.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, Any, List
import time
import json

# Add the backend source to Python path
backend_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(backend_path))

try:
    from ai_coding_agent.config import settings
    from ai_coding_agent.services.rate_limit_service import (
        get_rate_limit_service,
        RateLimitType,
        RateLimitScope,
        RateLimitConfig
    )
    from ai_coding_agent.middleware.unified_rate_limiting import UnifiedRateLimitMiddleware
    from fastapi import Request
    from fastapi.testclient import TestClient
    from unittest.mock import Mock
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running this from the backend directory with the virtual environment activated.")
    sys.exit(1)


class RateLimitValidator:
    """Validates and tests the unified rate limiting system."""
    
    def __init__(self):
        self.service = get_rate_limit_service()
        self.results: List[Dict[str, Any]] = []
    
    def log_result(self, test_name: str, success: bool, message: str, details: Dict[str, Any] = None):
        """Log a test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "details": details or {}
        })
    
    def validate_configuration(self):
        """Validate the rate limiting configuration."""
        print("\n🔧 Validating Rate Limiting Configuration...")
        
        try:
            # Check if settings are loaded
            rate_settings = settings.rate_limit
            self.log_result(
                "Settings Loading",
                True,
                f"Rate limit settings loaded successfully"
            )
            
            # Validate each rate limit type configuration
            configs = {
                "ADMIN": {
                    "limit": rate_settings.admin_limit,
                    "window": rate_settings.admin_window_seconds,
                    "block_duration": rate_settings.admin_block_duration
                },
                "AI": {
                    "limit": rate_settings.ai_limit,
                    "window": rate_settings.window_seconds,
                    "burst": rate_settings.ai_burst_limit
                },
                "API": {
                    "limit": rate_settings.api_limit,
                    "window": rate_settings.window_seconds,
                    "burst": rate_settings.api_burst_limit
                },
                "AUTH": {
                    "limit": rate_settings.auth_limit,
                    "window": rate_settings.auth_window_seconds,
                    "block_duration": rate_settings.auth_block_duration
                },
                "UPLOAD": {
                    "limit": rate_settings.upload_limit,
                    "window": rate_settings.upload_window_seconds,
                    "burst": rate_settings.upload_burst_limit
                }
            }
            
            for config_type, config_values in configs.items():
                # Validate positive values
                all_positive = all(v > 0 for v in config_values.values())
                self.log_result(
                    f"{config_type} Config Validation",
                    all_positive,
                    f"All values positive: {config_values}",
                    {"config": config_values}
                )
            
        except Exception as e:
            self.log_result(
                "Configuration Validation",
                False,
                f"Configuration error: {str(e)}"
            )
    
    def validate_service_initialization(self):
        """Validate that the rate limiting service initializes correctly."""
        print("\n🚀 Validating Service Initialization...")
        
        try:
            # Test service creation
            service = get_rate_limit_service()
            self.log_result(
                "Service Creation",
                service is not None,
                "Rate limiting service created successfully"
            )
            
            # Test configuration loading
            configs_loaded = len(service.configs) == 5
            self.log_result(
                "Configuration Loading",
                configs_loaded,
                f"Loaded {len(service.configs)} rate limit configurations"
            )
            
            # Test Redis client creation (without connecting)
            redis_client_method = hasattr(service, 'get_redis_client')
            self.log_result(
                "Redis Client Method",
                redis_client_method,
                "Redis client method available"
            )
            
        except Exception as e:
            self.log_result(
                "Service Initialization",
                False,
                f"Service initialization error: {str(e)}"
            )
    
    async def test_rate_limiting_logic(self):
        """Test the rate limiting logic with mock requests."""
        print("\n🧪 Testing Rate Limiting Logic...")
        
        try:
            # Create mock request
            mock_request = Mock()
            mock_request.client = Mock()
            mock_request.client.host = "127.0.0.1"
            mock_request.headers = {"user-agent": "test-agent"}
            mock_request.url = Mock()
            mock_request.url.path = "/api/v1/test"
            
            # Test different rate limit types
            test_cases = [
                (RateLimitType.API, "anonymous", "API rate limiting"),
                (RateLimitType.AI, "test_user_123", "AI rate limiting"),
                (RateLimitType.ADMIN, "admin_user", "Admin rate limiting"),
            ]
            
            for limit_type, user_id, description in test_cases:
                try:
                    # Test rate limit check
                    result = await self.service.check_rate_limit(
                        request=mock_request,
                        limit_type=limit_type,
                        user_id=user_id
                    )
                    
                    # Should be allowed on first request
                    self.log_result(
                        f"{description} - First Request",
                        result.allowed,
                        f"First request allowed: {result.remaining} remaining"
                    )
                    
                    # Check headers are present
                    has_headers = result.headers and "X-RateLimit-Limit" in result.headers
                    self.log_result(
                        f"{description} - Headers",
                        has_headers,
                        f"Rate limit headers present: {list(result.headers.keys()) if result.headers else 'None'}"
                    )
                    
                except Exception as e:
                    self.log_result(
                        f"{description} - Logic Test",
                        False,
                        f"Rate limiting logic error: {str(e)}"
                    )
            
        except Exception as e:
            self.log_result(
                "Rate Limiting Logic",
                False,
                f"Logic testing error: {str(e)}"
            )
    
    def validate_middleware_integration(self):
        """Validate middleware integration."""
        print("\n🔗 Validating Middleware Integration...")
        
        try:
            # Test middleware creation
            mock_app = Mock()
            middleware = UnifiedRateLimitMiddleware(mock_app)
            
            self.log_result(
                "Middleware Creation",
                middleware is not None,
                "Unified rate limiting middleware created successfully"
            )
            
            # Test endpoint pattern matching
            test_paths = [
                ("/api/v1/admin/users", RateLimitType.ADMIN),
                ("/api/v1/ai/chat", RateLimitType.AI),
                ("/api/v1/auth/login", RateLimitType.AUTH),
                ("/api/v1/upload/file", RateLimitType.UPLOAD),
                ("/api/v1/general", RateLimitType.API),
            ]
            
            for path, expected_type in test_paths:
                detected_type = middleware._determine_rate_limit_type(path)
                self.log_result(
                    f"Path Mapping - {path}",
                    detected_type == expected_type,
                    f"Expected {expected_type.value}, got {detected_type.value}"
                )
            
        except Exception as e:
            self.log_result(
                "Middleware Integration",
                False,
                f"Middleware integration error: {str(e)}"
            )
    
    def validate_environment_variables(self):
        """Validate environment variable configuration."""
        print("\n🌍 Validating Environment Variables...")
        
        required_vars = [
            "RATE_LIMIT_ADMIN_LIMIT",
            "RATE_LIMIT_AI_LIMIT",
            "RATE_LIMIT_API_LIMIT",
            "RATE_LIMIT_AUTH_LIMIT",
            "RATE_LIMIT_UPLOAD_LIMIT",
            "REDIS_HOST",
            "REDIS_PORT",
        ]
        
        for var in required_vars:
            value = os.getenv(var)
            self.log_result(
                f"Environment Variable - {var}",
                value is not None,
                f"Value: {value}" if value else "Not set"
            )
    
    def generate_report(self):
        """Generate a comprehensive test report."""
        print("\n📊 Test Report Summary")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        # Save detailed report
        report_file = Path(__file__).parent / "rate_limiting_validation_report.json"
        with open(report_file, "w") as f:
            json.dump({
                "summary": {
                    "total_tests": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "success_rate": (passed_tests/total_tests)*100
                },
                "results": self.results,
                "timestamp": time.time()
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return failed_tests == 0


async def main():
    """Main validation function."""
    print("🔍 AI Coding Agent - Rate Limiting System Validation")
    print("=" * 60)
    
    validator = RateLimitValidator()
    
    # Run all validation tests
    validator.validate_environment_variables()
    validator.validate_configuration()
    validator.validate_service_initialization()
    await validator.test_rate_limiting_logic()
    validator.validate_middleware_integration()
    
    # Generate report
    success = validator.generate_report()
    
    if success:
        print("\n🎉 All tests passed! Rate limiting system is properly configured.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
