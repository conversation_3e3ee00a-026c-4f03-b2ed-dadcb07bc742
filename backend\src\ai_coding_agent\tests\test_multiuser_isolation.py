import pytest
from fastapi.testclient import Test<PERSON>lient
from backend.src.ai_coding_agent.main import create_app
from ai_coding_agent.routers.auth import create_jwt_token

@pytest.fixture(scope="module")
def client():
    app = create_app()
    with TestClient(app) as c:
        yield c

@pytest.fixture(scope="module")
def user_tokens():
    # Mock two users with different IDs
    user_a = {"user_id": "user-a-uuid", "username": "user_a"}
    user_b = {"user_id": "user-b-uuid", "username": "user_b"}
    token_a = create_jwt_token(user_a)
    token_b = create_jwt_token(user_b)
    return {"A": token_a, "B": token_b}

@pytest.mark.asyncio
def test_user_isolation(client, user_tokens):
    # User A creates data
    headers_a = {"Authorization": f"Bearer {user_tokens['A']}"}
    resp = client.post("/api/v1/projects", json={"name": "A's Project"}, headers=headers_a)
    assert resp.status_code == 200
    project_id = resp.json()["id"]

    # User B tries to access User A's project
    headers_b = {"Authorization": f"Bearer {user_tokens['B']}"}
    resp = client.get(f"/api/v1/projects/{project_id}", headers=headers_b)
    assert resp.status_code == 403  # Forbidden

@pytest.mark.asyncio
def test_jwt_token_validation(client, user_tokens):
    # Invalid token
    headers = {"Authorization": "Bearer invalidtoken"}
    resp = client.get("/api/v1/projects", headers=headers)
    assert resp.status_code == 401

@pytest.mark.asyncio
def test_concurrent_sessions(client, user_tokens):
    # Simulate concurrent access
    headers_a = {"Authorization": f"Bearer {user_tokens['A']}"}
    headers_b = {"Authorization": f"Bearer {user_tokens['B']}"}
    resp_a = client.get("/api/v1/projects", headers=headers_a)
    resp_b = client.get("/api/v1/projects", headers=headers_b)
    assert resp_a.status_code == 200
    assert resp_b.status_code == 200
    assert resp_a.json() != resp_b.json()

@pytest.mark.asyncio
def test_privilege_escalation_attempt(client, user_tokens):
    # User B tries to update User A's project
    headers_b = {"Authorization": f"Bearer {user_tokens['B']}"}
    project_id = "user-a-project-id"
    resp = client.put(f"/api/v1/projects/{project_id}", json={"name": "Hacked"}, headers=headers_b)
    assert resp.status_code == 403

@pytest.mark.asyncio
def test_session_cleanup_and_timeout(client, user_tokens):
    # Simulate session timeout
    headers_a = {"Authorization": f"Bearer {user_tokens['A']}"}
    # Assume endpoint /api/v1/session/expire simulates session expiration
    resp = client.post("/api/v1/session/expire", headers=headers_a)
    assert resp.status_code == 200
    # Try to access after session expired
    resp = client.get("/api/v1/projects", headers=headers_a)
    assert resp.status_code == 401
