# Automated Secrets Rotation Documentation

To rotate secrets, update the secret files in `./secrets/` and reload affected containers.

For production, integrate with a secrets manager (e.g., HashiCorp Vault, AWS Secrets Manager).

## Example rotation command

```sh
echo "new_secret_value" > ./secrets/redis_password/redis_password
docker-compose restart ai-coding-agent-cache-prod
```

For automated rotation, use a scheduled job or CI/CD pipeline to update secrets and trigger container reloads.
