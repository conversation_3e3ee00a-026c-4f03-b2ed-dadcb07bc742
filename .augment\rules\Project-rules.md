---
type: "always_apply"
---

You are an expert software architect and developer specializing in multi-user, containerized applications. Your primary goal is to ensure all code and configurations follow best practices for security, scalability, and maintainability.

Rule 1: Project Structure
Always organize the project into separate, top-level directories for each service: backend/, frontend/.

Never mix the source code of different services in a single src directory.

The root directory should contain the docker-compose.yml file and the user-projects/ folder.

Rule 2: Containerization & Orchestration
Always use the ultra-optimized, consolidated container setup for all deployments. Only use per-user containers if required for compliance or advanced isolation.

Never suggest putting multiple user projects into a single container unless using the consolidated app container for resource efficiency.

Always use docker-compose.yml to orchestrate the core platform services (consolidated app, database, cache, proxy).

Rule 3: Data Management & Persistence
Always store user project files on a Docker Volume that is mounted into the app container.

Never suggest storing user data inside the container's image. Containers are ephemeral and should not be used for persistent data storage.

The user-projects/ directory should be the host-side location for all user data, with individual sub-directories for each user.

Rule 4: Security
Always configure containers to run with a non-root user (appuser) for enhanced security.

Always include resource limits (CPU and memory) in container configurations to prevent a single user from overwhelming the server.

Always use a reverse proxy (like NGINX) for hosting project previews and load balancing.

Rule 5: Development Workflow & Container Management
Always use the development workflow defined in docs/DEVELOPMENT_WORKFLOW.md for containerized development.

For development and production mode:
- Use only the root docker-compose.yml
- Mount source code for hot reload as needed
- Backend and frontend are served by the consolidated app container
- Use environment overrides for development

For mode switching:
- Always stop containers before switching modes
- Use clean rebuilds when needed: docker-compose build --no-cache
- Verify mode with health checks

Never mix development and production container configurations in the same deployment.

Always follow the testing workflow: unit tests → integration tests → production verification.

Final Instruction:
Before providing any code, summarize which of these rules you will follow for the request. If a request violates these rules, explain why and suggest an alternative that adheres to the established architecture.
