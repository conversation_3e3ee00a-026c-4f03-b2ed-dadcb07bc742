"""
Consolidated Audit Service for AI Coding Agent.

This module provides a unified audit system that combines:
- Structured logging (from utils/audit.py)
- Database audit trails (from services/audit.py)
- Admin audit logging (from services/admin_audit.py)
- Configuration-based audit utilities (from config/audit_config.py)
"""

import json
import structlog
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable
from uuid import uuid4, UUID

from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, desc
from fastapi import HTTPException, Request
from fastapi import status as http_status

from ai_coding_agent.models import (
    AuditLog, StatusHistory, ConcurrencyControl,
    AuditAction, AuditEntityType,
    AuditLogResponse, StatusHistoryResponse, ConcurrencyControlResponse,
    AuditLogFilter, ConflictResolution
)


class AuditCategory(str, Enum):
    """Categories for audit events."""
    SYSTEM = "system"
    USER = "user"
    SECURITY = "security"
    ADMIN = "admin"
    CONTAINER = "container"
    AI_MODEL = "ai_model"
    AGENT_ACTION = "agent_action"
    KNOWLEDGE_ACCESS = "knowledge_access"


class AuditLevel(str, Enum):
    """Severity levels for audit events."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AdminAuditAction(str, Enum):
    """Types of admin actions that can be audited."""
    MODEL_CONFIG_UPDATE = "model_config_update"
    PROVIDER_CONFIG_ADD = "provider_config_add"
    PROVIDER_CONFIG_UPDATE = "provider_config_update"
    PROVIDER_CONFIG_DELETE = "provider_config_delete"
    SYSTEM_HEALTH_CHECK = "system_health_check"
    MODEL_CONNECTION_TEST = "model_connection_test"
    USER_PERMISSION_CHANGE = "user_permission_change"
    ADMIN_LOGIN = "admin_login"
    ADMIN_LOGOUT = "admin_logout"
    CONFIG_EXPORT = "config_export"
    CONFIG_IMPORT = "config_import"
    SYSTEM_MONITORING = "system_monitoring"
    SYSTEM_CONFIG_UPDATE = "system_config_update"
    CONTAINER_MANAGEMENT = "container_management"
    USER_DATA_ACCESS = "user_data_access"
    SECURITY_VIOLATION = "security_violation"


class AuditSeverity(str, Enum):
    """Severity levels for admin audit events."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


def serialize_for_json(obj: Any) -> Any:
    """
    Recursively serialize objects for JSON storage.
    Handles datetime, Decimal, UUID, and other non-JSON-serializable types.
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, UUID):
        return str(obj)
    elif isinstance(obj, dict):
        return {key: serialize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [serialize_for_json(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        return serialize_for_json(obj.__dict__)
    else:
        return obj


class AuditLogEntry:
    """Simple audit log entry for structured logging."""

    def __init__(
        self,
        user_id: str,
        action: str,
        resource: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        model: Optional[str] = None,
        prompt: Optional[str] = None,
        response: Optional[str] = None,
        execution_time: Optional[float] = None,
        document: Optional[str] = None,
        event_type: Optional[str] = None,
        correlation_id: Optional[str] = None,
        category: Optional[AuditCategory] = None,
        level: Optional[AuditLevel] = None
    ):
        self.timestamp = datetime.now(timezone.utc).isoformat()
        self.user_id = user_id
        self.action = action
        self.resource = resource
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.model = model
        self.prompt = prompt
        self.response = response
        self.execution_time = execution_time
        self.document = document
        self.event_type = event_type
        self.correlation_id = correlation_id
        self.category = category.value if category else None
        self.level = level.value if level else AuditLevel.INFO.value

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return serialize_for_json(self.__dict__)


class ConsolidatedAuditService:
    """
    Consolidated audit service that provides both structured logging and database audit trails.
    """

    def __init__(self, db: Session):
        self.db = db
        self.logger = structlog.get_logger("audit")

    def _extract_request_info(self, request: Optional[Request]) -> Dict[str, Optional[str]]:
        """Extract IP address and user agent from request."""
        if not request:
            return {"ip_address": None, "user_agent": None}

        return {
            "ip_address": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent")
        }

    def _serialize_for_json(self, data: Any) -> Any:
        """Serialize data for JSON storage."""
        return serialize_for_json(data)

    # Structured Logging Methods (from utils/audit.py)

    def log_agent_action(
        self,
        user_id: str,
        action: str,
        resource: str,
        model: Optional[str] = None,
        prompt: Optional[str] = None,
        response: Optional[str] = None,
        execution_time: Optional[float] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        correlation_id: Optional[str] = None,
        request: Optional[Request] = None
    ) -> None:
        """Log an AI agent action."""
        request_info = self._extract_request_info(request)
        entry = AuditLogEntry(
            user_id=user_id,
            action=action,
            resource=resource,
            ip_address=ip_address or request_info["ip_address"],
            user_agent=user_agent or request_info["user_agent"],
            model=model,
            prompt=prompt,
            response=response,
            execution_time=execution_time,
            event_type="agent_action",
            correlation_id=correlation_id,
            category=AuditCategory.AGENT_ACTION
        )
        self.logger.info("agent_action", **entry.to_dict())

    def log_knowledge_access(
        self,
        user_id: str,
        document: str,
        action: str = "access",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        correlation_id: Optional[str] = None,
        request: Optional[Request] = None
    ) -> None:
        """Log knowledge base access."""
        request_info = self._extract_request_info(request)
        entry = AuditLogEntry(
            user_id=user_id,
            action=action,
            resource=document,
            ip_address=ip_address or request_info["ip_address"],
            user_agent=user_agent or request_info["user_agent"],
            document=document,
            event_type="knowledge_access",
            correlation_id=correlation_id,
            category=AuditCategory.KNOWLEDGE_ACCESS
        )
        self.logger.info("knowledge_access", **entry.to_dict())

    def log_security_event(
        self,
        user_id: str,
        action: str,
        resource: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        correlation_id: Optional[str] = None,
        level: AuditLevel = AuditLevel.WARNING,
        request: Optional[Request] = None
    ) -> None:
        """Log a security event."""
        request_info = self._extract_request_info(request)
        entry = AuditLogEntry(
            user_id=user_id,
            action=action,
            resource=resource,
            ip_address=ip_address or request_info["ip_address"],
            user_agent=user_agent or request_info["user_agent"],
            event_type="security_event",
            correlation_id=correlation_id,
            category=AuditCategory.SECURITY,
            level=level
        )
        log_method = getattr(self.logger, level.value, self.logger.warning)
        log_method("security_event", **entry.to_dict())

    def log_admin_action(
        self,
        admin_user_id: str,
        action: AdminAuditAction,
        resource: str,
        details: Optional[Dict[str, Any]] = None,
        severity: AuditSeverity = AuditSeverity.MEDIUM,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request: Optional[Request] = None
    ) -> None:
        """Log an admin action."""
        request_info = self._extract_request_info(request)
        entry = AuditLogEntry(
            user_id=admin_user_id,
            action=action.value,
            resource=resource,
            ip_address=ip_address or request_info["ip_address"],
            user_agent=user_agent or request_info["user_agent"],
            event_type="admin_action",
            category=AuditCategory.ADMIN,
            level=AuditLevel.INFO if severity in [AuditSeverity.LOW, AuditSeverity.MEDIUM] else AuditLevel.WARNING
        )

        # Add details to the log entry
        if details:
            entry_dict = entry.to_dict()
            entry_dict.update({"details": details, "severity": severity.value})
        else:
            entry_dict = entry.to_dict()
            entry_dict["severity"] = severity.value

        self.logger.info("admin_action", **entry_dict)

    def log_audit_event(
        self,
        category: AuditCategory,
        level: AuditLevel,
        message: str,
        user_id: Optional[str] = None,
        **kwargs
    ) -> None:
        """Log a general audit event."""
        log_method = getattr(self.logger, level.value, self.logger.info)
        log_method(
            message,
            category=category.value,
            level=level.value,
            user_id=user_id,
            **kwargs
        )

    # Database Audit Methods (from services/audit.py)

    def log_action(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        action: AuditAction,
        user_id: Optional[str] = None,
        user_email: Optional[str] = None,
        old_values: Optional[Dict] = None,
        new_values: Optional[Dict] = None,
        changed_fields: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict] = None,
        request: Optional[Request] = None
    ) -> AuditLogResponse:
        """Log an audit action to the database."""
        try:
            # Extract request info if request provided
            if request:
                request_info = self._extract_request_info(request)
                ip_address = ip_address or request_info["ip_address"]
                user_agent = user_agent or request_info["user_agent"]

            # Convert datetime objects to strings for JSON serialization
            if old_values:
                old_values = self._serialize_for_json(old_values)
            if new_values:
                new_values = self._serialize_for_json(new_values)
            if metadata:
                metadata = self._serialize_for_json(metadata)

            # Determine changed fields if not provided
            if changed_fields is None and old_values and new_values:
                changed_fields = []
                for key, new_value in new_values.items():
                    old_value = old_values.get(key)
                    if old_value != new_value:
                        changed_fields.append(key)

            audit_log = AuditLog(
                id=str(uuid4()),
                entity_type=entity_type.value,
                entity_id=entity_id,
                action=action.value,
                user_id=user_id,
                user_email=user_email,
                old_values=old_values,
                new_values=new_values,
                changed_fields=changed_fields or [],
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                reason=reason,
                audit_metadata=metadata
            )

            self.db.add(audit_log)
            self.db.commit()
            self.db.refresh(audit_log)

            return AuditLogResponse.model_validate(audit_log)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to log audit action: {str(e)}"
            )

    def get_audit_trail(
        self,
        entity_type: Optional[AuditEntityType] = None,
        entity_id: Optional[str] = None,
        user_id: Optional[str] = None,
        action: Optional[AuditAction] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[AuditLogResponse]:
        """Get audit trail with filtering."""
        try:
            query = select(AuditLog)

            # Apply filters
            conditions = []
            if entity_type:
                conditions.append(AuditLog.entity_type == entity_type.value)
            if entity_id:
                conditions.append(AuditLog.entity_id == entity_id)
            if user_id:
                conditions.append(AuditLog.user_id == user_id)
            if action:
                conditions.append(AuditLog.action == action.value)
            if start_date:
                conditions.append(AuditLog.created_at >= start_date)
            if end_date:
                conditions.append(AuditLog.created_at <= end_date)

            if conditions:
                query = query.where(and_(*conditions))

            # Order by creation date (newest first) and apply pagination
            query = query.order_by(desc(AuditLog.created_at)).offset(offset).limit(limit)

            result = self.db.execute(query)
            audit_logs = result.scalars().all()

            return [AuditLogResponse.model_validate(log) for log in audit_logs]

        except Exception as e:
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve audit trail: {str(e)}"
            )


# Global audit service instance
_audit_service: Optional[ConsolidatedAuditService] = None


def get_audit_service(db: Session) -> ConsolidatedAuditService:
    """Get or create the global audit service instance."""
    global _audit_service
    if _audit_service is None or _audit_service.db != db:
        _audit_service = ConsolidatedAuditService(db)
    return _audit_service


# Convenience functions for backward compatibility
def log_agent_action(
    user_id: str,
    action: str,
    resource: str,
    model: Optional[str] = None,
    prompt: Optional[str] = None,
    response: Optional[str] = None,
    execution_time: Optional[float] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    correlation_id: Optional[str] = None
) -> None:
    """Convenience function for logging agent actions (structured logging only)."""
    logger = structlog.get_logger("audit")
    entry = AuditLogEntry(
        user_id=user_id,
        action=action,
        resource=resource,
        ip_address=ip_address,
        user_agent=user_agent,
        model=model,
        prompt=prompt,
        response=response,
        execution_time=execution_time,
        event_type="agent_action",
        correlation_id=correlation_id,
        category=AuditCategory.AGENT_ACTION
    )
    logger.info("agent_action", **entry.to_dict())


def log_knowledge_access(
    user_id: str,
    document: str,
    action: str = "access",
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    correlation_id: Optional[str] = None
) -> None:
    """Convenience function for logging knowledge access (structured logging only)."""
    logger = structlog.get_logger("audit")
    entry = AuditLogEntry(
        user_id=user_id,
        action=action,
        resource=document,
        ip_address=ip_address,
        user_agent=user_agent,
        document=document,
        event_type="knowledge_access",
        correlation_id=correlation_id,
        category=AuditCategory.KNOWLEDGE_ACCESS
    )
    logger.info("knowledge_access", **entry.to_dict())


def log_security_event(
    user_id: str,
    action: str,
    resource: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    correlation_id: Optional[str] = None
) -> None:
    """Convenience function for logging security events (structured logging only)."""
    logger = structlog.get_logger("audit")
    entry = AuditLogEntry(
        user_id=user_id,
        action=action,
        resource=resource,
        ip_address=ip_address,
        user_agent=user_agent,
        event_type="security_event",
        correlation_id=correlation_id,
        category=AuditCategory.SECURITY,
        level=AuditLevel.WARNING
    )
    logger.warning("security_event", **entry.to_dict())


def log_audit_event(
    category: AuditCategory,
    level: AuditLevel,
    message: str,
    **kwargs
) -> None:
    """Convenience function for logging general audit events."""
    logger = structlog.get_logger("audit")
    log_method = getattr(logger, level.value, logger.info)
    log_method(
        message,
        category=category.value,
        level=level.value,
        **kwargs
    )


# Export all the important classes and functions
__all__ = [
    "ConsolidatedAuditService",
    "AuditCategory",
    "AuditLevel",
    "AdminAuditAction",
    "AuditSeverity",
    "AuditLogEntry",
    "get_audit_service",
    "log_agent_action",
    "log_knowledge_access",
    "log_security_event",
    "log_audit_event",
    "serialize_for_json"
]
