"""
Agent Orchestrator

Coordinates multiple AI agents and handles intelligent task routing and delegation.
Includes verification system and dependency management.
"""

import asyncio
import re
import subprocess
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import logging

from pydantic import BaseModel, Field

from ai_coding_agent.agents import Agent<PERSON><PERSON>, AgentCapability, AGENT_CONFIGS, get_agent_by_capability
from ai_coding_agent.services.ai.base import AIProvider, ChatRequest, ChatResponse, ChatMessage, HealthStatus
from ai_coding_agent.services.ai.conversation import ConversationManager, Conversation
from ai_coding_agent.services.ai.providers.ollama import OllamaProvider

logger = logging.getLogger(__name__)


class TaskRequest(BaseModel):
    """Request for task execution by agents."""
    task_description: str
    preferred_agent: Optional[AgentRole] = None
    required_capabilities: List[AgentCapability] = Field(default_factory=list)
    context: Optional[Dict[str, Any]] = None
    user_id: Optional[str] = None
    conversation_id: Optional[str] = None
    verify_output: bool = True  # Enable verification by default
    auto_fix: bool = True  # Enable auto-fixing by default


class TaskResult(BaseModel):
    """Result of task execution."""
    task_id: str
    agent_role: AgentRole
    success: bool
    result: Optional[str] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    execution_time_ms: Optional[float] = None
    verification_status: Optional[str] = None
    dependencies_installed: Optional[List[str]] = None


class VerificationResult(BaseModel):
    """Result of output verification."""
    is_valid: bool
    issues: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)
    confidence_score: float = 0.0  # 0.0 to 1.0


class DependencyInstallResult(BaseModel):
    """Result of dependency installation."""
    success: bool
    installed_packages: List[str] = Field(default_factory=list)
    failed_packages: List[str] = Field(default_factory=list)
    error_messages: List[str] = Field(default_factory=list)


class AgentCollaboration(BaseModel):
    """Configuration for multi-agent collaboration."""
    primary_agent: AgentRole
    supporting_agents: List[AgentRole] = Field(default_factory=list)
    coordination_strategy: str = "sequential"  # "sequential", "parallel", "hierarchical"
    verification_enabled: bool = True
    auto_dependency_management: bool = True


class AgentOrchestrator:
    """Orchestrates AI agents for complex task execution with verification and dependency management."""

    def __init__(self):
        self.providers: Dict[str, AIProvider] = {}
        self.conversation_manager = ConversationManager()
        self.task_counter = 0

        # Initialize providers
        self._init_providers()

    def _init_providers(self):
        """Initialize AI providers."""
        # Add Ollama provider
        self.providers["ollama"] = OllamaProvider()

    async def close(self):
        """Close all providers and cleanup resources."""
        for provider in self.providers.values():
            if hasattr(provider, 'close') and callable(getattr(provider, 'close')):
                await provider.close()

    async def execute_task(self, request: TaskRequest) -> TaskResult:
        """
        Execute a task using the most appropriate agent with verification and dependency management.

        Args:
            request: The task request

        Returns:
            The task result
        """
        self.task_counter += 1
        task_id = f"task_{self.task_counter}_{int(datetime.now().timestamp())}"
        start_time = datetime.now()

        try:
            # Auto-install dependencies if requested
            dependencies_installed = []
            if request.auto_fix:
                dependencies_installed = await self._handle_dependencies(request)

            # Determine the best agent for this task
            agent_role = await self._select_agent(request)

            if not agent_role:
                return TaskResult(
                    task_id=task_id,
                    agent_role=AgentRole.ARCHITECT,  # Default fallback
                    success=False,
                    error="No suitable agent found for this task"
                )

            # Get or create conversation
            conversation_id = request.conversation_id
            if not conversation_id:
                conversation_id = await self.conversation_manager.create_conversation(
                    agent_role=agent_role,
                    user_id=request.user_id,
                    project_context=request.context
                )

            # Add user message to conversation
            await self.conversation_manager.add_message(
                conversation_id,
                "user",
                request.task_description
            )

            # Get conversation history for context
            messages = await self.conversation_manager.get_conversation_messages(
                conversation_id,
                limit=10  # Last 10 messages for context
            )

            # Execute the task
            result = await self._execute_with_agent(
                agent_role,
                messages,
                request.context
            )

            # Verify output if requested
            verification_status = None
            if request.verify_output and agent_role in [AgentRole.BACKEND, AgentRole.FRONTEND, AgentRole.TEST]:
                verification_result = await self._verify_output(agent_role, result.content, request)
                verification_status = "verified" if verification_result.is_valid else "needs_improvement"

                # Auto-fix if verification failed and auto_fix is enabled
                if not verification_result.is_valid and request.auto_fix:
                    fixed_result = await self._auto_fix_output(
                        agent_role, result.content, verification_result, messages, request.context
                    )
                    if fixed_result:
                        result = fixed_result
                        verification_status = "auto_fixed"

            # Add agent response to conversation
            await self.conversation_manager.add_message(
                conversation_id,
                "assistant",
                result.content
            )

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            return TaskResult(
                task_id=task_id,
                agent_role=agent_role,
                success=True,
                result=result.content,
                metadata={
                    "conversation_id": conversation_id,
                    "model_used": result.model,
                    "tokens_used": result.tokens_used,
                    **(result.metadata or {})
                },
                execution_time_ms=execution_time,
                verification_status=verification_status,
                dependencies_installed=dependencies_installed
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            logger.error(f"Task execution failed: {str(e)}")

            return TaskResult(
                task_id=task_id,
                agent_role=request.preferred_agent or AgentRole.ARCHITECT,
                success=False,
                error=str(e),
                execution_time_ms=execution_time
            )

    async def _handle_dependencies(self, request: TaskRequest) -> List[str]:
        """Automatically handle dependency installation using Shell Agent."""
        dependencies_detected = self._detect_dependencies(request.task_description)
        if not dependencies_detected:
            return []

        # Create a dependency installation request for Shell Agent
        dependency_request = TaskRequest(
            task_description=f"Install the following dependencies: {', '.join(dependencies_detected)}",
            preferred_agent=AgentRole.SHELL,
            context=request.context,
            user_id=request.user_id,
            verify_output=False,  # Don't verify dependency installation
            auto_fix=False  # Don't auto-fix dependency installation
        )

        result = await self.execute_task(dependency_request)

        if result.success:
            logger.info(f"Dependencies installed successfully: {dependencies_detected}")
            return dependencies_detected
        else:
            logger.warning(f"Failed to install dependencies: {result.error}")
            return []

    def _detect_dependencies(self, task_description: str) -> List[str]:
        """Detect dependencies mentioned in task description."""
        # Common dependency patterns
        patterns = {
            r'\bfastapi\b': 'fastapi',
            r'\buvicorn\b': 'uvicorn',
            r'\bsqlalchemy\b': 'sqlalchemy',
            r'\bpydantic\b': 'pydantic',
            r'\bpytest\b': 'pytest',
            r'\brequests\b': 'requests',
            r'\bnumpy\b': 'numpy',
            r'\bpandas\b': 'pandas',
            r'\bflask\b': 'flask',
            r'\bdjango\b': 'django',
            r'\bolama\b': 'ollama',
            r'\balembic\b': 'alembic',
            r'\bpsycopg2\b': 'psycopg2-binary',
            r'\bdocker\b': 'docker',
        }

        detected = set()
        task_lower = task_description.lower()

        for pattern, package in patterns.items():
            if re.search(pattern, task_lower):
                detected.add(package)

        return list(detected)

    async def _verify_output(self, agent_role: AgentRole, output: str, request: TaskRequest) -> VerificationResult:
        """Verify agent output using the Architect Agent."""
        # Create verification prompt based on agent role
        verification_prompts = {
            AgentRole.BACKEND: """
Review this backend code for:
1. Security vulnerabilities
2. Database query efficiency
3. API design best practices
4. Error handling
5. Code structure and maintainability

Provide a verification score (0.0-1.0) and list any issues or suggestions.
""",
            AgentRole.FRONTEND: """
Review this frontend code for:
1. React best practices
2. Accessibility standards
3. Performance optimization
4. Code maintainability
5. UI/UX principles

Provide a verification score (0.0-1.0) and list any issues or suggestions.
""",
            AgentRole.TEST: """
Review this test code for:
1. Test coverage adequacy
2. Test case quality
3. Edge case handling
4. Test maintainability
5. Testing best practices

Provide a verification score (0.0-1.0) and list any issues or suggestions.
"""
        }

        verification_prompt = verification_prompts.get(agent_role, "Review this code for quality and best practices.")

        # Create verification request for Architect Agent
        messages = [
            ChatMessage(role="user", content=f"""
{verification_prompt}

Code to review:
```
{output}
```

Original task: {request.task_description}

Please respond in this format:
SCORE: [0.0-1.0]
ISSUES:
- [List any issues found]
SUGGESTIONS:
- [List improvement suggestions]
""")
        ]

        # Execute verification with Architect Agent
        verification_result = await self._execute_with_agent(
            AgentRole.ARCHITECT,
            messages,
            request.context
        )

        # Parse verification result
        return self._parse_verification_result(verification_result.content)

    def _parse_verification_result(self, verification_text: str) -> VerificationResult:
        """Parse verification result from Architect Agent response."""
        score_match = re.search(r'SCORE:\s*([0-9]*\.?[0-9]+)', verification_text)
        confidence_score = float(score_match.group(1)) if score_match else 0.5

        issues = []
        suggestions = []

        # Extract issues
        issues_section = re.search(r'ISSUES:\s*(.*?)(?=SUGGESTIONS:|$)', verification_text, re.DOTALL)
        if issues_section:
            issues_text = issues_section.group(1).strip()
            issues = [line.strip('- ').strip() for line in issues_text.split('\n') if line.strip().startswith('-')]

        # Extract suggestions
        suggestions_section = re.search(r'SUGGESTIONS:\s*(.*?)$', verification_text, re.DOTALL)
        if suggestions_section:
            suggestions_text = suggestions_section.group(1).strip()
            suggestions = [line.strip('- ').strip() for line in suggestions_text.split('\n') if line.strip().startswith('-')]

        return VerificationResult(
            is_valid=confidence_score >= 0.7,  # Consider valid if score >= 0.7
            issues=issues,
            suggestions=suggestions,
            confidence_score=confidence_score
        )

    async def _auto_fix_output(
        self,
        agent_role: AgentRole,
        original_output: str,
        verification_result: VerificationResult,
        messages: List[ChatMessage],
        context: Optional[Dict[str, Any]]
    ) -> Optional[ChatResponse]:
        """Attempt to auto-fix output based on verification feedback."""
        if verification_result.confidence_score >= 0.9:  # Don't fix if score is very high
            return None

        # Create fix request
        fix_prompt = f"""
The previous output has some issues that need to be addressed.

Original code:
```
{original_output}
```

Issues identified:
{chr(10).join(f'- {issue}' for issue in verification_result.issues)}

Suggestions for improvement:
{chr(10).join(f'- {suggestion}' for suggestion in verification_result.suggestions)}

Please provide an improved version that addresses these issues while maintaining the original functionality.
"""

        fix_messages = messages + [ChatMessage(role="user", content=fix_prompt)]

        # Execute fix with the same agent
        try:
            fixed_result = await self._execute_with_agent(agent_role, fix_messages, context)
            logger.info(f"Auto-fix applied by {agent_role.value} agent")
            return fixed_result
        except Exception as e:
            logger.error(f"Auto-fix failed: {str(e)}")
            return None

    async def collaborate_agents(
        self,
        request: TaskRequest,
        collaboration: AgentCollaboration
    ) -> List[TaskResult]:
        """
        Execute a task using multiple collaborating agents with verification and dependency management.

        Args:
            request: The task request
            collaboration: Collaboration configuration

        Returns:
            List of task results from each agent
        """
        results = []

        # Handle dependencies first if enabled
        if collaboration.auto_dependency_management:
            await self._handle_dependencies(request)

        if collaboration.coordination_strategy == "sequential":
            # Execute agents sequentially with verification
            current_context = request.context or {}

            # Start with primary agent
            primary_request = TaskRequest(
                task_description=request.task_description,
                preferred_agent=collaboration.primary_agent,
                context=current_context,
                user_id=request.user_id,
                conversation_id=request.conversation_id,
                verify_output=collaboration.verification_enabled,
                auto_fix=True
            )

            primary_result = await self.execute_task(primary_request)
            results.append(primary_result)

            # Update context with primary result
            if primary_result.success and primary_result.result:
                current_context["primary_agent_result"] = primary_result.result

            # Execute supporting agents with context from previous agents
            for i, agent_role in enumerate(collaboration.supporting_agents):
                # Create enhanced task description with context from previous work
                enhanced_description = self._create_collaborative_task_description(
                    request.task_description,
                    results,
                    agent_role
                )

                supporting_request = TaskRequest(
                    task_description=enhanced_description,
                    preferred_agent=agent_role,
                    context=current_context,
                    user_id=request.user_id,
                    conversation_id=request.conversation_id,
                    verify_output=collaboration.verification_enabled,
                    auto_fix=True
                )

                supporting_result = await self.execute_task(supporting_request)
                results.append(supporting_result)

                # Update context with each result
                if supporting_result.success and supporting_result.result:
                    current_context[f"{agent_role.value}_result"] = supporting_result.result

            # Final verification by Architect if enabled and not already the primary agent
            if (collaboration.verification_enabled and
                collaboration.primary_agent != AgentRole.ARCHITECT and
                any(r.success for r in results)):

                verification_result = await self._final_verification(results, request)
                if verification_result:
                    results.append(verification_result)

        elif collaboration.coordination_strategy == "parallel":
            # PARALLEL EXECUTION DISABLED FOR SYSTEM STABILITY
            # Force sequential execution to prevent resource conflicts and ensure predictable behavior
            logger.warning("Parallel coordination strategy requested but DISABLED - forcing sequential execution")
            logger.info("Reason: Sequential execution enforced to prevent resource conflicts and ensure predictable agent behavior")

            # Fall back to sequential execution
            collaboration.coordination_strategy = "sequential"
            return await self.collaborate_agents(request, collaboration)

        elif collaboration.coordination_strategy == "hierarchical":
            # Architect coordinates all other agents
            architect_request = TaskRequest(
                task_description=f"Analyze this task and coordinate other agents: {request.task_description}",
                preferred_agent=AgentRole.ARCHITECT,
                context=request.context,
                user_id=request.user_id,
                conversation_id=request.conversation_id,
                verify_output=False,  # Architect doesn't need verification
                auto_fix=False
            )

            architect_result = await self.execute_task(architect_request)
            results.append(architect_result)

            if architect_result.success and architect_result.result:
                # Parse architect's plan and execute sub-tasks
                sub_tasks = self._parse_architect_plan(architect_result.result)
                for sub_task in sub_tasks:
                    sub_result = await self.execute_task(sub_task)
                    results.append(sub_result)

        return results

    def _create_collaborative_task_description(
        self,
        original_task: str,
        previous_results: List[TaskResult],
        current_agent: AgentRole
    ) -> str:
        """Create enhanced task description with context from previous agents."""
        context_summary = []

        for result in previous_results:
            if result.success and result.result:
                context_summary.append(f"- {result.agent_role.value} completed: {result.result[:200]}...")

        if context_summary:
            return f"""
Original task: {original_task}

Previous work completed:
{chr(10).join(context_summary)}

As the {current_agent.value} agent, build upon this work and contribute your specialized expertise.
"""
        else:
            return original_task

    async def _final_verification(
        self,
        results: List[TaskResult],
        original_request: TaskRequest
    ) -> Optional[TaskResult]:
        """Perform final verification by Architect Agent."""
        successful_results = [r for r in results if r.success and r.result]
        if not successful_results:
            return None

        # Compile all successful outputs
        combined_output = "\n\n".join([
            f"=== {result.agent_role.value.upper()} OUTPUT ===\n{result.result}"
            for result in successful_results
        ])

        verification_request = TaskRequest(
            task_description=f"""
Perform final quality verification of the collaborative work below:

Original task: {original_request.task_description}

Combined agent outputs:
{combined_output}

Please verify:
1. Task completion and requirement fulfillment
2. Code quality and consistency across agents
3. Integration points between components
4. Overall architecture and design quality
5. Any missing elements or improvements needed

Provide a final assessment and any recommendations.
""",
            preferred_agent=AgentRole.ARCHITECT,
            context=original_request.context,
            user_id=original_request.user_id,
            verify_output=False,  # Don't verify the verification
            auto_fix=False
        )

        return await self.execute_task(verification_request)

    def _parse_architect_plan(self, architect_output: str) -> List[TaskRequest]:
        """Parse architect's plan into executable sub-tasks."""
        # This is a simplified implementation - could be enhanced with more sophisticated parsing
        sub_tasks = []
        lines = architect_output.split('\n')

        current_task = None
        current_agent = None

        for line in lines:
            line = line.strip()

            # Look for agent assignments
            for role in AgentRole:
                if role.value in line.lower() and ('agent' in line.lower() or 'should' in line.lower()):
                    current_agent = role
                    break

            # Look for task descriptions
            if line.startswith('-') or line.startswith('*') or line.startswith('1.') or line.startswith('2.'):
                task_desc = line.lstrip('-*123456789. ').strip()
                if task_desc and current_agent:
                    sub_tasks.append(TaskRequest(
                        task_description=task_desc,
                        preferred_agent=current_agent
                    ))

        return sub_tasks

    async def _select_agent(self, request: TaskRequest) -> Optional[AgentRole]:
        """Select the best agent for a task."""
        # If preferred agent is specified, use it
        if request.preferred_agent:
            return request.preferred_agent

        # If required capabilities are specified, find an agent with those capabilities
        if request.required_capabilities:
            for capability in request.required_capabilities:
                agent = get_agent_by_capability(capability)
                if agent:
                    return agent

        # Use intelligent task analysis to determine the best agent
        task_lower = request.task_description.lower()

        # Frontend-related keywords
        if any(keyword in task_lower for keyword in [
            "ui", "frontend", "react", "component", "css", "html", "design", "interface", "responsive"
        ]):
            return AgentRole.FRONTEND

        # Backend-related keywords
        elif any(keyword in task_lower for keyword in [
            "api", "backend", "server", "database", "auth", "endpoint", "model", "service"
        ]):
            return AgentRole.BACKEND

        # Shell/DevOps-related keywords
        elif any(keyword in task_lower for keyword in [
            "deploy", "command", "shell", "script", "install", "setup", "configure", "environment"
        ]):
            return AgentRole.SHELL

        # Debug/Testing-related keywords
        elif any(keyword in task_lower for keyword in [
            "debug", "error", "fix", "bug", "test", "issue", "problem", "troubleshoot"
        ]):
            return AgentRole.DEBUG

        # Test-related keywords
        elif any(keyword in task_lower for keyword in [
            "test", "testing", "unit test", "integration", "coverage", "spec"
        ]):
            return AgentRole.TEST

        # Default to Architect for planning and coordination
        else:
            return AgentRole.ARCHITECT

    async def _execute_with_agent(
        self,
        agent_role: AgentRole,
        messages: List[ChatMessage],
        context: Optional[Dict[str, Any]] = None
    ) -> ChatResponse:
        """Execute a task with a specific agent."""
        # Get the primary provider (Ollama for now)
        provider = self.providers.get("ollama")
        if not provider:
            raise Exception("No AI provider available")

        # Create chat request
        chat_request = ChatRequest(
            messages=messages,
            agent_role=agent_role,
            context=context
        )

        # Execute the request
        return await provider.chat(chat_request)

    async def get_agent_health(self) -> Dict[AgentRole, Dict[str, Any]]:
        """Get health status for all agents."""
        agent_health = {}

        for agent_role in AgentRole:
            agent_config = AGENT_CONFIGS.get(agent_role)
            if not agent_config:
                continue

            # Check provider health for this agent's model
            provider_health = None
            for provider in self.providers.values():
                health_check = await provider.health_check()
                for model_check in health_check.models:
                    if model_check.model == agent_config.model:
                        provider_health = model_check
                        break
                if provider_health:
                    break

            # Handle capabilities - they might be strings or enum values
            capabilities = []
            for cap in agent_config.capabilities:
                if hasattr(cap, 'value'):
                    capabilities.append(cap.value)
                else:
                    capabilities.append(str(cap))

            agent_health[agent_role] = {
                "model": agent_config.model,
                "capabilities": capabilities,
                "health_status": provider_health.status.value if provider_health else "unknown",
                "latency_ms": provider_health.latency_ms if provider_health else None,
                "error": provider_health.error if provider_health else None
            }

        return agent_health

    async def get_orchestrator_stats(self) -> Dict[str, Any]:
        """Get orchestrator statistics."""
        conversation_stats = await self.conversation_manager.get_conversation_stats()
        agent_health = await self.get_agent_health()

        return {
            "task_counter": self.task_counter,
            "active_providers": list(self.providers.keys()),
            "agent_health": agent_health,
            "conversation_stats": conversation_stats,
            "supported_agents": [role.value for role in AgentRole],
            "available_capabilities": [cap.value for cap in AgentCapability]
        }
