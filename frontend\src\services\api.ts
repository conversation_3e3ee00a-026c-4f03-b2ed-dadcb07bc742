import { retryApiCall } from '../utils/retry';
import { validateAndSanitizeUrl } from '../utils/validation';
import { csrfToken } from '../utils/security';

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api';

class ApiService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Validate and sanitize the URL
    const fullUrl = `${API_BASE_URL}${endpoint}`;
    const urlValidation = validateAndSanitizeUrl(fullUrl);

    if (!urlValidation.isValid) {
      throw new Error(`Invalid URL: ${urlValidation.error}`);
    }

    // Add CSRF token to headers
    const headers = {
      'Content-Type': 'application/json',
      'X-CSRF-Token': csrfToken.get() || '',
      ...options.headers,
    };

    const requestOptions: RequestInit = {
      ...options,
      headers,
      credentials: 'include', // Include cookies for auth
    };

    // Use retry mechanism for the API call
    return retryApiCall(async () => {
      const response = await fetch(urlValidation.sanitizedUrl!, requestOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
          errorData.detail ||
          `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    });
  }

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.makeRequest('/health');
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.makeRequest(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.makeRequest(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.makeRequest(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.makeRequest(endpoint, { method: 'DELETE' });
  }

  // File upload with validation
  async uploadFile<T>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, string>
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    return this.makeRequest(endpoint, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-Token': csrfToken.get() || '',
      },
    });
  }
}

export const apiService = new ApiService();
export default apiService;
