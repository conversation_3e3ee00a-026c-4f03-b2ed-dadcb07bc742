#!/bin/bash
# Scaling Deployment Script for AI Coding Agent

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Default environment variables for scaling
export NODE_ENV=${NODE_ENV:-production}
export LOG_LEVEL=${LOG_LEVEL:-info}
export HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-30s}
export ENVIRONMENT=${ENVIRONMENT:-production}

# Database configuration
export DATABASE_URL=${DATABASE_URL:-*******************************************************/ai_coding_agent}
export REDIS_URL=${REDIS_URL:-redis://redis-1:6379,redis://redis-2:6379,redis://redis-3:6379}

# API configuration
export REACT_APP_API_URL=${REACT_APP_API_URL:-https://api.ai-coding-agent.com}
export REACT_APP_INTERNAL_API_URL=${REACT_APP_INTERNAL_API_URL:-http://haproxy:80}
export OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://ollama-1:11434}

# Scaling configuration
export MIN_REPLICAS=${MIN_REPLICAS:-2}
export MAX_REPLICAS=${MAX_REPLICAS:-10}
export TARGET_CPU_UTILIZATION=${TARGET_CPU_UTILIZATION:-70}

# PostgreSQL replication
export POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD:-$(openssl rand -base64 32)}

echo "🚀 Deploying AI Coding Agent in Scaling Mode..."

# Validate required environment variables
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ] || [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    echo "❌ Missing required Supabase environment variables"
    echo "   Please set: SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY"
    exit 1
fi

if [ -z "$SECRET_KEY" ] || [ -z "$CONFIG_ENCRYPTION_KEY" ]; then
    echo "❌ Missing required security keys"
    echo "   Please set: SECRET_KEY, CONFIG_ENCRYPTION_KEY"
    exit 1
fi

# Change to project root
cd "$PROJECT_ROOT"

# Create HAProxy configuration directory
mkdir -p infrastructure/docker/haproxy

# Copy HAProxy config if it doesn't exist
if [ ! -f "infrastructure/docker/haproxy/haproxy.cfg" ]; then
    cp infrastructure/docker/haproxy.cfg infrastructure/docker/haproxy/haproxy.cfg
fi

# Build images if needed
echo "🔨 Building Docker images..."
docker-compose -f infrastructure/docker/production.yml build

# Deploy scaling stack
echo "📦 Deploying scaling stack..."
docker-compose -f infrastructure/docker/scaling.yml up -d

# Wait for core services
echo "⏳ Waiting for core services to be ready..."
sleep 10

# Initialize PostgreSQL replication
echo "🔧 Setting up PostgreSQL replication..."
docker exec ai-coding-agent-postgres-primary psql -U postgres -c "
CREATE USER replicator REPLICATION LOGIN ENCRYPTED PASSWORD '$POSTGRES_REPLICATION_PASSWORD';
SELECT pg_create_physical_replication_slot('replica_slot');
"

# Initialize Redis cluster
echo "🔧 Initializing Redis cluster..."
if [ -f "$SCRIPT_DIR/init-redis-cluster.sh" ]; then
    bash "$SCRIPT_DIR/init-redis-cluster.sh"
else
    echo "⚠️  Redis cluster initialization script not found"
fi

# Wait for all services to be healthy
echo "🏥 Waiting for all services to be healthy..."
for i in {1..60}; do
    UNHEALTHY=$(docker-compose -f infrastructure/docker/scaling.yml ps --filter "health=unhealthy" -q | wc -l)
    if [ "$UNHEALTHY" -eq 0 ]; then
        echo "✅ All services are healthy"
        break
    fi
    echo "   Attempt $i/60: $UNHEALTHY unhealthy services remaining..."
    sleep 5
done

# Display service status
echo "📊 Service Status:"
docker-compose -f infrastructure/docker/scaling.yml ps

# Display access information
echo ""
echo "🌐 Access Information:"
echo "   Frontend: http://localhost:80"
echo "   API: http://localhost:80/api/"
echo "   HAProxy Stats: http://localhost:8404/stats (admin/admin)"
echo "   Prometheus: http://localhost:9090"
echo "   Grafana: http://localhost:3001"
echo "   PgAdmin: http://localhost:5050"
echo ""
echo "🔧 Management Commands:"
echo "   Scale backend: docker-compose -f infrastructure/docker/scaling.yml up -d --scale backend=5"
echo "   View logs: docker-compose -f infrastructure/docker/scaling.yml logs -f [service]"
echo "   Stop: docker-compose -f infrastructure/docker/scaling.yml down"
echo ""
echo "✅ AI Coding Agent scaling deployment complete!"
