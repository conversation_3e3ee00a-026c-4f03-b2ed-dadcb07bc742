"""
User Data Management Service for Data Isolation and Security.

This service implements comprehensive user data isolation, path validation,
and secure file operations following the architectural security requirements.
"""

import os
import re
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import stat
from pydantic import BaseModel, Field

# Unix-specific imports (not available on Windows)
try:
    import pwd
    import grp
    UNIX_AVAILABLE = True
except ImportError:
    UNIX_AVAILABLE = False

from ..config import get_settings
from ..middleware.input_validation import SecurityValidationMixin
from ..services.audit_service import log_audit_event, AuditCategory, AuditLevel

logger = logging.getLogger(__name__)


class UserDataError(Exception):
    """Custom exception for user data operations."""
    pass


class DataAccessViolation(UserDataError):
    """Exception raised when user tries to access unauthorized data."""
    pass


class UserDirectoryInfo(BaseModel):
    """Information about a user's directory."""
    user_id: str
    directory_path: str
    created_at: datetime
    last_accessed: datetime
    size_bytes: int
    file_count: int
    permissions: str
    is_isolated: bool


class FilePermissions(BaseModel):
    """File permission configuration."""
    owner_read: bool = True
    owner_write: bool = True
    owner_execute: bool = False
    group_read: bool = False
    group_write: bool = False
    group_execute: bool = False
    other_read: bool = False
    other_write: bool = False
    other_execute: bool = False


class UserDataManager(SecurityValidationMixin):
    """
    Manages user data isolation, directory creation, and access validation.

    Implements security-first approach with comprehensive path validation,
    permission enforcement, and audit logging.
    """

    def __init__(self):
        """Initialize the UserDataManager."""
        self.settings = get_settings()
        self.base_user_data_path = Path(self.settings.user_data_path)

        # Ensure base directory exists
        self._ensure_base_directory()

        # Default file permissions for user directories
        self.default_permissions = FilePermissions()

        logger.info("UserDataManager initialized successfully")

    def _validate_user_id(self, user_id: str) -> str:
        """Validate user ID format and security."""
        if not user_id or not isinstance(user_id, str):
            raise DataAccessViolation("Invalid user ID")

        # Basic validation - alphanumeric and hyphens only
        if not re.match(r'^[a-zA-Z0-9\-_]+$', user_id):
            raise DataAccessViolation("User ID contains invalid characters")

        if len(user_id) > 50:
            raise DataAccessViolation("User ID too long")

        return user_id.strip()

    def _ensure_base_directory(self) -> None:
        """Ensure the base user data directory exists with proper permissions."""
        try:
            self.base_user_data_path.mkdir(parents=True, exist_ok=True)

            # Set secure permissions on base directory (755)
            os.chmod(self.base_user_data_path, 0o755)

            logger.info(f"Base user data directory ensured: {self.base_user_data_path}")

        except Exception as e:
            logger.error(f"Failed to create base user data directory: {e}")
            raise UserDataError(f"Failed to initialize user data directory: {e}")

    def create_user_directory(self, user_id: str) -> UserDirectoryInfo:
        """
        Create an isolated directory for a user.

        Args:
            user_id: Unique user identifier

        Returns:
            UserDirectoryInfo: Information about the created directory

        Raises:
            UserDataError: If directory creation fails
            DataAccessViolation: If user_id is invalid
        """
        # Validate user_id
        user_id = self._validate_user_id(user_id)

        # Create user-specific directory path
        user_dir_name = f"user-{user_id}"
        user_directory = self.base_user_data_path / user_dir_name

        try:
            # Check if directory already exists
            if user_directory.exists():
                logger.info(f"User directory already exists: {user_directory}")
                return self._get_directory_info(user_id, user_directory)

            # Create the directory
            user_directory.mkdir(parents=True, exist_ok=True)

            # Set secure permissions (700 - owner only)
            os.chmod(user_directory, 0o700)

            # Create subdirectories for organization
            subdirs = ['projects', 'uploads', 'temp', 'backups']
            for subdir in subdirs:
                subdir_path = user_directory / subdir
                subdir_path.mkdir(exist_ok=True)
                os.chmod(subdir_path, 0o700)

            # Create a .user_info file with metadata
            user_info_file = user_directory / '.user_info'
            user_info_data = {
                'user_id': user_id,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'version': '1.0'
            }

            with open(user_info_file, 'w') as f:
                import json
                json.dump(user_info_data, f, indent=2)

            os.chmod(user_info_file, 0o600)

            # Log successful creation
            log_audit_event(
                category=AuditCategory.USER,
                level=AuditLevel.INFO,
                message=f"User directory created for user {user_id}",
                user_id=user_id,
                action="user_directory_created",
                directory_path=str(user_directory),
                subdirectories=subdirs
            )

            logger.info(f"Created user directory: {user_directory}")
            return self._get_directory_info(user_id, user_directory)

        except Exception as e:
            logger.error(f"Failed to create user directory for {user_id}: {e}")
            raise UserDataError(f"Failed to create user directory: {e}")

    def ensure_data_isolation(self, user_id: str, path: str) -> str:
        """
        Validate that a user can only access their own data.

        Args:
            user_id: User identifier
            path: Path to validate

        Returns:
            str: Validated and normalized path

        Raises:
            DataAccessViolation: If path is outside user's directory
        """
        # Validate inputs
        user_id = self._validate_user_id(user_id)
        path = self.validate_no_path_traversal(path, "file_path")

        # Get user's directory
        user_dir_name = f"user-{user_id}"
        user_directory = self.base_user_data_path / user_dir_name

        # Normalize the requested path
        try:
            # Handle both absolute and relative paths
            if os.path.isabs(path):
                requested_path = Path(path).resolve()
            else:
                requested_path = (user_directory / path).resolve()

            # Ensure the path is within the user's directory
            if not str(requested_path).startswith(str(user_directory.resolve())):
                log_audit_event(
                    category=AuditCategory.SECURITY,
                    level=AuditLevel.CRITICAL,
                    message=f"Critical data isolation violation attempt by user {user_id}",
                    user_id=user_id,
                    action="data_isolation_violation",
                    requested_path=path,
                    resolved_path=str(requested_path),
                    user_directory=str(user_directory)
                )

                raise DataAccessViolation(
                    f"Access denied: Path '{path}' is outside user directory"
                )

            return str(requested_path)

        except Exception as e:
            if isinstance(e, DataAccessViolation):
                raise
            logger.error(f"Path validation failed for user {user_id}, path {path}: {e}")
            raise DataAccessViolation(f"Invalid path: {e}")

    def get_user_directory_path(self, user_id: str) -> str:
        """
        Get the base directory path for a user.

        Args:
            user_id: User identifier

        Returns:
            str: User's directory path
        """
        user_id = self._validate_user_id(user_id)
        user_dir_name = f"user-{user_id}"
        return str(self.base_user_data_path / user_dir_name)

    def _get_directory_info(self, user_id: str, directory_path: Path) -> UserDirectoryInfo:
        """Get information about a user directory."""
        try:
            stat_info = directory_path.stat()

            # Calculate directory size and file count
            total_size = 0
            file_count = 0

            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    file_path = Path(root) / file
                    try:
                        total_size += file_path.stat().st_size
                        file_count += 1
                    except (OSError, IOError):
                        continue  # Skip files we can't access

            # Get permissions
            permissions = oct(stat_info.st_mode)[-3:]

            return UserDirectoryInfo(
                user_id=user_id,
                directory_path=str(directory_path),
                created_at=datetime.fromtimestamp(stat_info.st_ctime, timezone.utc),
                last_accessed=datetime.fromtimestamp(stat_info.st_atime, timezone.utc),
                size_bytes=total_size,
                file_count=file_count,
                permissions=permissions,
                is_isolated=True
            )

        except Exception as e:
            logger.error(f"Failed to get directory info for {user_id}: {e}")
            raise UserDataError(f"Failed to get directory information: {e}")

    def list_user_directories(self) -> List[UserDirectoryInfo]:
        """
        List all user directories for admin monitoring.

        Returns:
            List[UserDirectoryInfo]: Information about all user directories
        """
        directories = []

        try:
            for item in self.base_user_data_path.iterdir():
                if item.is_dir() and item.name.startswith('user-'):
                    user_id = item.name.replace('user-', '')
                    try:
                        dir_info = self._get_directory_info(user_id, item)
                        directories.append(dir_info)
                    except Exception as e:
                        logger.warning(f"Failed to get info for directory {item}: {e}")
                        continue

            return directories

        except Exception as e:
            logger.error(f"Failed to list user directories: {e}")
            raise UserDataError(f"Failed to list user directories: {e}")

    def cleanup_user_data(self, user_id: str, confirm: bool = False) -> bool:
        """
        Clean up all data for a user (admin only).

        Args:
            user_id: User identifier
            confirm: Confirmation flag to prevent accidental deletion

        Returns:
            bool: True if cleanup successful

        Raises:
            UserDataError: If cleanup fails
        """
        if not confirm:
            raise UserDataError("Cleanup requires explicit confirmation")

        user_id = self._validate_user_id(user_id)
        user_dir_name = f"user-{user_id}"
        user_directory = self.base_user_data_path / user_dir_name

        try:
            if not user_directory.exists():
                logger.info(f"User directory does not exist: {user_directory}")
                return True

            # Get directory info before deletion for audit
            dir_info = self._get_directory_info(user_id, user_directory)

            # Remove the entire directory
            shutil.rmtree(user_directory)

            # Log the cleanup
            log_audit_event(
                category=AuditCategory.USER,
                level=AuditLevel.WARNING,
                message=f"User data cleanup completed for user {user_id}",
                user_id=user_id,
                action="user_data_cleanup",
                directory_path=str(user_directory),
                files_removed=dir_info.file_count,
                size_removed_bytes=dir_info.size_bytes
            )

            logger.info(f"Successfully cleaned up user data for {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to cleanup user data for {user_id}: {e}")
            raise UserDataError(f"Failed to cleanup user data: {e}")

    def enforce_file_permissions(self, user_id: str, file_path: str) -> bool:
        """
        Enforce proper file permissions for user files.

        Args:
            user_id: User identifier
            file_path: Path to the file

        Returns:
            bool: True if permissions were enforced successfully
        """
        # Validate path is within user directory
        validated_path = self.ensure_data_isolation(user_id, file_path)

        try:
            path_obj = Path(validated_path)

            if not path_obj.exists():
                raise UserDataError(f"File does not exist: {file_path}")

            # Set appropriate permissions based on file type
            if path_obj.is_file():
                # Files: 600 (owner read/write only)
                os.chmod(path_obj, 0o600)
            elif path_obj.is_dir():
                # Directories: 700 (owner read/write/execute only)
                os.chmod(path_obj, 0o700)

            logger.debug(f"Enforced permissions for {validated_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to enforce permissions for {file_path}: {e}")
            raise UserDataError(f"Failed to enforce file permissions: {e}")


# Global instance
_user_data_manager: Optional[UserDataManager] = None


def get_user_data_manager() -> UserDataManager:
    """Get the global UserDataManager instance."""
    global _user_data_manager
    if _user_data_manager is None:
        _user_data_manager = UserDataManager()
    return _user_data_manager
