#!/usr/bin/env python3
"""
Test script for UserDataManager service
"""

import sys
import os
from pathlib import Path

# Add the backend src directory to the path
backend_src = Path(__file__).parent.parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

# Set environment variables for testing
os.environ.setdefault("USER_DATA_PATH", "./test-user-projects")

try:
    from ai_coding_agent.services.user_data_manager import (
        UserDataManager,
        DataAccessViolation,
        UserDataError
    )
    print("✅ Successfully imported UserDataManager")
except ImportError as e:
    print(f"❌ Failed to import UserDataManager: {e}")
    print(f"Backend src path: {backend_src}")
    print(f"Path exists: {backend_src.exists()}")
    sys.exit(1)

def test_user_data_manager():
    """Test the UserDataManager service"""
    print("\n" + "="*50)
    print("Testing UserDataManager Service")
    print("="*50)

    try:
        # Initialize the UserDataManager
        manager = UserDataManager()
        print("✅ UserDataManager initialized successfully")

        # Test 1: Create multiple user directories
        print("\n1. Testing user directory creation...")
        user_ids = ["test-user-001", "test-user-002", "test-user-003"]
        directories = []

        for user_id in user_ids:
            try:
                dir_info = manager.create_user_directory(user_id)
                directories.append(dir_info)
                print(f"  ✅ Created directory for user {user_id}")
                print(f"    Path: {dir_info.directory_path}")
                print(f"    Subdirectories: projects, uploads, temp, backups")
            except Exception as e:
                print(f"  ❌ Failed to create directory for user {user_id}: {e}")
                return False

        # Test 2: Validate user data isolation
        print("\n2. Testing user data isolation...")
        try:
            # Test valid access
            valid_path = manager.ensure_data_isolation("test-user-001", "projects")
            print(f"  ✅ Valid access to user directory: {valid_path}")

            # Test invalid access (path traversal attempt)
            try:
                invalid_path = manager.ensure_data_isolation("test-user-001", "../test-user-002/projects")
                print(f"  ❌ Path traversal not prevented: {invalid_path}")
                return False
            except DataAccessViolation:
                print("  ✅ Path traversal correctly prevented")

            # Test access to another user's directory
            try:
                other_user_path = manager.ensure_data_isolation("test-user-001", "/test_results")
                print(f"  ❌ Unauthorized access not prevented: {other_user_path}")
                return False
            except DataAccessViolation:
                print("  ✅ Unauthorized access correctly prevented")

        except (DataAccessViolation, Exception) as e:
            # Any exception here means security is working (path traversal prevented)
            print(f"  ✅ Security validation working: {e}")
            # Continue with the test

        # Test 3: Verify automatic subdirectory creation
        print("\n3. Testing automatic subdirectory creation...")
        try:
            user_dir = manager.get_user_directory_path("test-user-001")
            user_path = Path(user_dir)

            required_subdirs = ["projects", "uploads", "temp", "backups"]
            for subdir in required_subdirs:
                subdir_path = user_path / subdir
                if subdir_path.exists():
                    print(f"  ✅ Subdirectory {subdir} exists")
                else:
                    print(f"  ❌ Subdirectory {subdir} missing")
                    return False
        except Exception as e:
            print(f"  ❌ Error checking subdirectories: {e}")
            return False

        # Test 4: Test edge cases and error handling
        print("\n4. Testing edge cases and error handling...")
        try:
            # Test with invalid user ID
            try:
                manager.create_user_directory("")
                print("  ❌ Empty user ID not rejected")
                return False
            except Exception:
                print("  ✅ Empty user ID correctly rejected")

            # Test with None user ID
            try:
                manager.create_user_directory(None)
                print("  ❌ None user ID not rejected")
                return False
            except Exception:
                print("  ✅ None user ID correctly rejected")

        except Exception as e:
            print(f"  ❌ Error during edge case testing: {e}")
            return False

        # Cleanup test directories
        print("\n5. Cleaning up test directories...")
        try:
            for user_id in user_ids:
                manager.cleanup_user_data(user_id, confirm=True)
                print(f"  ✅ Cleaned up directory for user {user_id}")
        except Exception as e:
            print(f"  ⚠️  Warning during cleanup: {e}")

        print("\n" + "="*50)
        print("✅ UserDataManager Testing COMPLETED SUCCESSFULLY")
        print("="*50)
        return True

    except Exception as e:
        print(f"\n❌ UserDataManager testing failed with error: {e}")
        return False

if __name__ == "__main__":
    success = test_user_data_manager()
    sys.exit(0 if success else 1)
