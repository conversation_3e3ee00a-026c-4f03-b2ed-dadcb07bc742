"""
DynamicHosting class for user project preview subdomain management.
Follows Copilot rules: security, maintainability, separation of concerns.
"""
import os
from typing import Dict

class DynamicHosting:
    """
    Manages dynamic subdomain routing and NGINX config generation for user containers.
    """
    def __init__(self, nginx_conf_dir: str):
        self.nginx_conf_dir = nginx_conf_dir

    def generate_subdomain_config(self, user_id: str, container_port: int) -> str:
        subdomain = f"preview-{user_id}.yourdomain.com"
        conf_path = os.path.join(self.nginx_conf_dir, f"user-{user_id}.conf")
        config = f"""
server {{
    listen 80;
    server_name {subdomain};
    location / {{
        proxy_pass http://127.0.0.1:{container_port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
    location /ws/ {{
        proxy_pass http://127.0.0.1:{container_port};
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_http_version 1.1;
    }}
}}
"""
        with open(conf_path, "w") as f:
            f.write(config)
        return conf_path

    def remove_subdomain_config(self, user_id: str):
        conf_path = os.path.join(self.nginx_conf_dir, f"user-{user_id}.conf")
        if os.path.exists(conf_path):
            os.remove(conf_path)

    def list_configs(self) -> Dict[str, str]:
        return {
            fname: os.path.join(self.nginx_conf_dir, fname)
            for fname in os.listdir(self.nginx_conf_dir)
            if fname.startswith("user-") and fname.endswith(".conf")
        }
