# Automated backup and recovery for user data volumes
# Usage: python scripts/backup_user_data.py
# This script will backup all user-projects data to a timestamped archive

import shutil
from datetime import datetime
from pathlib import Path

def backup_user_projects(backup_dir: str = "./data/backups"):
    src_dir = Path("./user-projects")
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    backup_path = Path(backup_dir) / f"user-projects-backup-{timestamp}.zip"
    backup_path.parent.mkdir(parents=True, exist_ok=True)
    shutil.make_archive(str(backup_path.with_suffix('')), 'zip', str(src_dir))
    print(f"Backup created: {backup_path}")

if __name__ == "__main__":
    backup_user_projects()
