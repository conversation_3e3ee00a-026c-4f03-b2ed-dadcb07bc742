# AI Coding Agent

An intelligent no-code platform powered by collaborative AI agents, enabling users to build, configure, and deploy software projects through natural language interactions.

## Current Capabilities

- **FastAPI Backend**: Async REST API with health (`GET /api/v1/health`), readiness (`GET /api/v1/health/ready`), and liveness (`GET /api/v1/health/live`) endpoints.

- **Configuration Management**: Pydantic Settings loaded from environment variables and YAML files with validation and secret enforcement.

- **Project Structure**: Poetry-managed Python 3.11+ layout (`src/ai_coding_agent`, `tests/`, `config/`, `Docs/`), with pre-configured `.gitignore` and pre-commit hooks.

- **Code Quality Tooling**: Black, isort, flake8, mypy integrated via pre-commit for consistent formatting and type checking.

- **Automated Testing**: pytest suite covering health endpoints and configuration validation.

- **AI Model Overview**: `show_models.py` script to inspect available AI models and their agent assignments.

- **Modular Routing**: Health router implemented; ready for additional API modules (e.g., auth, users).

- **Configuration Files**: Support for `config/development.yaml` and `config/production.yaml` alongside `.env` files.

- **Security Middleware**: TrustedHost and CORS setup based on environment settings.

For a detailed roadmap and upcoming phases, see `projectroadmap.md`.

## Quick Start

### Prerequisites
- Python 3.11 or higher
- Poetry (recommended) or pip

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd AiCodingaagent
   ```

2. **Install dependencies**
   ```bash
   # Using Poetry (recommended)
   poetry install

   # Or using pip
   pip install -e .
   ```

3. **Set up environment variables**
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit .env with your configuration
   # At minimum, set SECRET_KEY and DB_PASSWORD
   ```

4. **Run the development server**
   ```bash
   # Using Poetry
   poetry run python -m ai_coding_agent.main

   # Or if activated in virtual environment
   python -m ai_coding_agent.main
   ```

5. **Access the application**
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/api/v1/health

### Development Tools

- **Format code**: `poetry run black src/ tests/`
- **Sort imports**: `poetry run isort src/ tests/`
- **Lint code**: `poetry run flake8 src/ tests/`
- **Type check**: `poetry run mypy src/`
- **Run tests**: `poetry run pytest`
- **Install pre-commit hooks**: `poetry run pre-commit install`

## Project Structure

```
AI Coding Agent/
├── src/ai_coding_agent/          # Main application package
│   ├── __init__.py
│   ├── main.py                   # FastAPI application entry point
│   ├── config.py                 # Configuration management
│   ├── models/                   # Pydantic models
│   ├── services/                 # Business logic
│   ├── routers/                  # API route handlers
│   │   └── health.py            # Health check endpoints
│   └── utils/                    # Utility functions
├── tests/                        # Test suite
│   ├── conftest.py              # Test configuration
│   ├── test_health.py           # Health endpoint tests
│   └── test_config.py           # Configuration tests
├── config/                       # Environment-specific configs
│   ├── development.yaml
│   └── production.yaml
├── docs/                         # Documentation
├── .env.example                  # Environment variables template
├── .gitignore                   # Git ignore rules
├── .pre-commit-config.yaml      # Pre-commit hooks
├── pyproject.toml               # Poetry configuration
├── projectroadmap.md            # Full project roadmap
├── .copilot-rules.md            # Copilot development rules
└── README.md                    # This file
```

## Configuration

The application uses a layered configuration approach:

1. **Default values** in Pydantic models
2. **Environment variables** (highest priority)
3. **YAML configuration files** for environment-specific settings
4. **`.env` files** for local development

### Required Environment Variables

```bash
# Security (Required)
SECRET_KEY=your_super_secret_key_that_must_be_at_least_32_characters_long
DB_PASSWORD=your_secure_database_password

# Optional (have defaults)
ENVIRONMENT=development
DEBUG=true
HOST=localhost
PORT=8000
```

## Security Features

Following the security-first approach defined in `.copilot-rules.md`:

- **Environment Variable Management**: All secrets in environment variables
- **Input Validation**: Pydantic schemas for all data validation
- **Secure Defaults**: Security headers and trusted host middleware
- **Type Safety**: Full type hints and mypy validation
- **Error Handling**: Global exception handling with debug control

## API Endpoints

### Health Monitoring
- `GET /api/v1/health` - Comprehensive health check
- `GET /api/v1/health/ready` - Readiness probe
- `GET /api/v1/health/live` - Liveness probe

## Testing

The project includes a comprehensive test suite:

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=src/ai_coding_agent

# Run specific test file
poetry run pytest tests/test_health.py
```

## What's Next: Phase 2

Phase 2 will implement core backend services including:
- Database integration with Supabase (PostgreSQL + pgvector)
- Redis cache for high-performance operations
- Authentication system with JWT
- API framework expansion
- Error handling and logging

## Technology Stack

- **Backend**: FastAPI (Python 3.11+)
- **Database**: Supabase (PostgreSQL + pgvector for embeddings)
- **Cache**: Redis for high-performance operations
- **AI Integration**: Ollama + LangChain (prepared)
- **Configuration**: Pydantic Settings
- **Testing**: pytest with async support
- **Code Quality**: Black, isort, flake8, mypy
- **Package Management**: Poetry

## Contributing

1. Ensure all tests pass: `poetry run pytest`
2. Format code: `poetry run black src/ tests/`
3. Check types: `poetry run mypy src/`
4. Run pre-commit hooks: `poetry run pre-commit run --all-files`

## License

This project is part of the AI Coding Agent development initiative.

---

**Phase 1 Status**: ✅ COMPLETE
**Next Phase**: Phase 2 - Core Backend Services
**Overall Progress**: 1/34 phases complete (3%)

## Docker Setup & Cleanup Instructions

This project uses a simplified Docker architecture:
- **app**: Combined backend (FastAPI) and frontend (React) container
- **postgres**: Persistent PostgreSQL database (with pgvector)
- **redis**: Lightweight cache, queue, and pub-sub service

### Build & Run
To build and start all services:
```sh
docker-compose up --build
```

### Healthchecks
- Backend/API: http://localhost:8000/health
- Postgres: Healthcheck via `pg_isready`
- Redis: Healthcheck via `redis-cli ping`

### Cleaning Up Old Docker Resources
Safe for local development (does not affect running system containers):
```sh
# Remove stopped containers, unused images, networks, and build cache
docker system prune -af
docker volume prune -f
```

### Notes
- All static frontend files are served by the backend (FastAPI) from `/app/frontend/dist`.
- Only Supabase (Postgres + pgvector) and Redis are used for database and cache.
- Unused services (nginx, pgAdmin, MongoDB, RabbitMQ, etc.) have been removed for simplicity and performance.

For more details, see `docker-compose.yml` and `Dockerfile.app`.

## Supabase + Redis Database Mode

This project uses Supabase (Postgres + pgvector) as the canonical database and Redis for cache/queues.

### Environment Configuration
Edit your `.env` file:
```
# Supabase (hosted)
SUPABASE_URL=
SUPABASE_KEY=

# Optional local Postgres for dev
DATABASE_URL=postgresql://user:password@localhost:5432/ai_coding_agent  # Only set if running local Postgres. If using Supabase, leave unset.

# Redis
REDIS_URL=redis://localhost:6379/0

# Misc
PROJECTS_DIR=/var/www/projects
APP_ENV=development
```

### Running Migrations
- To apply migrations (SQL files in `database/migrations/`):
  ```bash
  python -m database.migrations
  ```
- If using Alembic, run:
  ```bash
  alembic upgrade head
  ```

### Local Development
- By default, only Redis is required. To use local Postgres, uncomment the `postgres` service in `docker-compose.yml` and set `DATABASE_URL` in `.env`.
- If using hosted Supabase, do **not** start Postgres locally.

### Connectivity Check
- Run the check script to verify DB and Redis:
  ```bash
  python scripts/check_db.py
  ```

### Cleanup Instructions
- To safely prune Docker resources after confirming the new setup works:
  ```bash
  docker compose down --volumes --remove-orphans
  docker system prune -af
  docker volume prune -f
  docker builder prune -a -f
  ```
