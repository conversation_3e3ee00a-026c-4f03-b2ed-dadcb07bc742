# AI Coding Agent - Production Dependencies
# Core web framework
fastapi>=0.104.0,<0.117.0
uvicorn[standard]>=0.24.0,<0.36.0

# Security updates for CVE fixes
setuptools>=78.1.1,<81.0.0  # Fix for CVE-2025-47273, CVE-2024-6345

# Configuration and settings
pydantic>=2.4.0,<2.12.0
pydantic-core>=2.14.6
pydantic-settings>=2.0.0,<2.11.0
email-validator>=2.0.0,<2.3.0
python-dotenv>=1.0.0,<1.2.0
pyyaml>=6.0.0,<6.1.0

# Database
sqlalchemy>=2.0.0,<2.1.0
asyncpg>=0.29.0,<0.31.0
psycopg2-binary==2.9.10
alembic>=1.13.0,<1.17.0
supabase>=2.18.1,<2.19.0

# Vector Database for LTKB/STPM (pgvector via Supabase)
# chromadb>=0.4.0  # REMOVED: Replaced with pgvector

# Redis for caching and real-time features
redis>=6.4.0,<6.5.0
hiredis>=3.2.1,<3.3.0
aioredis>=2.0.1,<2.1.0

# Authentication and security
# python-jose[cryptography]>=3.3.0  # REPLACED: Has ecdsa vulnerability CVE-2024-23342
PyJWT[crypto]>=2.10.1,<2.11.0  # Secure alternative to python-jose, no ecdsa dependency
passlib[bcrypt]>=1.7.4,<1.8.0
python-multipart>=0.0.20,<0.1.0

# File handling
aiofiles>=24.1.0,<24.2.0

# AI and ML
langchain==0.3.27
langchain-community==0.3.27
langchain-ollama==0.3.6

# Ollama client
ollama==0.5.3

# HTTP client
httpx>=0.28.1,<0.29.0
aiohttp>=3.12.15,<3.13.0

# Container orchestration for user environments
docker>=7.1.0,<7.2.0

# Logging
structlog>=25.4.0,<25.5.0

# System monitoring
psutil>=6.1.0,<7.0.0
