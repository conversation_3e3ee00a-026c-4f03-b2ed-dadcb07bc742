"""
Unified Rate Limiting Middleware for AI Coding Agent.

This middleware provides comprehensive rate limiting for all endpoint types
using the unified rate limiting service.
"""

from typing import Callable, Optional, Dict, Any
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware

from ai_coding_agent.services.rate_limit_service import (
    get_rate_limit_service, 
    RateLimitType, 
    RateLimitConfig,
    RateLimitScope
)


class UnifiedRateLimitMiddleware(BaseHTTPMiddleware):
    """
    Unified rate limiting middleware that applies appropriate rate limits
    based on the endpoint being accessed.
    """
    
    def __init__(self, app: Any):
        super().__init__(app)
        self.rate_limit_service = get_rate_limit_service()
        
        # Define endpoint patterns and their rate limit types
        self.endpoint_patterns = {
            "/api/v1/admin/": RateLimitType.ADMIN,
            "/api/v1/ai/": RateLimitType.AI,
            "/api/v1/auth/": RateLimitType.AUTH,
            "/api/v1/upload/": RateLimitType.UPLOAD,
            "/api/": RateLimitType.API,  # Default for all API endpoints
        }
    
    def _determine_rate_limit_type(self, path: str) -> RateLimitType:
        """Determine the appropriate rate limit type for a given path."""
        for pattern, limit_type in self.endpoint_patterns.items():
            if path.startswith(pattern):
                return limit_type
        return RateLimitType.API  # Default
    
    def _extract_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from request for user-scoped rate limiting."""
        # Try to get user ID from various sources
        user_id = None
        
        # From JWT token in Authorization header
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            # In a real implementation, you'd decode the JWT here
            # For now, we'll check if there's a user in the request state
            pass
        
        # From request state (set by auth middleware)
        if hasattr(request.state, "user_id"):
            user_id = request.state.user_id
        elif hasattr(request.state, "user") and hasattr(request.state.user, "id"):
            user_id = str(request.state.user.id)
        
        # From query parameters (for API keys)
        if not user_id:
            user_id = request.query_params.get("user_id")
        
        return user_id
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Apply rate limiting based on the endpoint being accessed.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            The response from the next handler
            
        Raises:
            HTTPException: When rate limit is exceeded
        """
        # Skip rate limiting for health checks and static files
        if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        if request.url.path.startswith("/static/"):
            return await call_next(request)
        
        # Determine rate limit type
        limit_type = self._determine_rate_limit_type(request.url.path)
        
        # Extract user ID for user-scoped rate limiting
        user_id = self._extract_user_id(request)
        
        # Check rate limit
        result = await self.rate_limit_service.check_rate_limit(
            request=request,
            limit_type=limit_type,
            user_id=user_id
        )
        
        if not result.allowed:
            # Rate limit exceeded
            headers = result.headers or {}
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Rate limit exceeded",
                    "limit_type": limit_type.value,
                    "retry_after": result.retry_after,
                    "reset_time": result.reset_time
                },
                headers=headers
            )
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers to response
        if result.headers:
            for key, value in result.headers.items():
                response.headers[key] = value
        
        return response


# Convenience functions for specific rate limiting needs

async def check_admin_rate_limit(request: Request, user_id: Optional[str] = None) -> None:
    """Check admin-specific rate limits."""
    service = get_rate_limit_service()
    result = await service.check_rate_limit(
        request=request,
        limit_type=RateLimitType.ADMIN,
        user_id=user_id
    )
    
    if not result.allowed:
        headers = result.headers or {}
        raise HTTPException(
            status_code=429,
            detail="Admin rate limit exceeded",
            headers=headers
        )


async def check_ai_rate_limit(request: Request, user_id: Optional[str] = None) -> None:
    """Check AI-specific rate limits."""
    service = get_rate_limit_service()
    result = await service.check_rate_limit(
        request=request,
        limit_type=RateLimitType.AI,
        user_id=user_id
    )
    
    if not result.allowed:
        headers = result.headers or {}
        raise HTTPException(
            status_code=429,
            detail="AI rate limit exceeded",
            headers=headers
        )


async def check_auth_rate_limit(request: Request, user_id: Optional[str] = None) -> None:
    """Check authentication-specific rate limits."""
    service = get_rate_limit_service()
    result = await service.check_rate_limit(
        request=request,
        limit_type=RateLimitType.AUTH,
        user_id=user_id
    )
    
    if not result.allowed:
        headers = result.headers or {}
        raise HTTPException(
            status_code=429,
            detail="Authentication rate limit exceeded",
            headers=headers
        )


async def check_upload_rate_limit(request: Request, user_id: Optional[str] = None) -> None:
    """Check upload-specific rate limits."""
    service = get_rate_limit_service()
    result = await service.check_rate_limit(
        request=request,
        limit_type=RateLimitType.UPLOAD,
        user_id=user_id
    )
    
    if not result.allowed:
        headers = result.headers or {}
        raise HTTPException(
            status_code=429,
            detail="Upload rate limit exceeded",
            headers=headers
        )


# Custom rate limiting decorator for specific endpoints
def custom_rate_limit(
    limit: int,
    window_seconds: int,
    scope: RateLimitScope = RateLimitScope.IP,
    block_duration_seconds: int = 300
):
    """
    Decorator for applying custom rate limits to specific endpoints.
    
    Args:
        limit: Number of requests allowed
        window_seconds: Time window in seconds
        scope: Scope of rate limiting (IP, USER, GLOBAL)
        block_duration_seconds: How long to block after exceeding limit
    """
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            service = get_rate_limit_service()
            custom_config = RateLimitConfig(
                limit=limit,
                window_seconds=window_seconds,
                scope=scope,
                block_duration_seconds=block_duration_seconds
            )
            
            user_id = None
            if hasattr(request.state, "user_id"):
                user_id = request.state.user_id
            
            result = await service.check_rate_limit(
                request=request,
                limit_type=RateLimitType.API,  # Use API as default type
                user_id=user_id,
                custom_config=custom_config
            )
            
            if not result.allowed:
                headers = result.headers or {}
                raise HTTPException(
                    status_code=429,
                    detail="Custom rate limit exceeded",
                    headers=headers
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator
