apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ai-coding-agent-metrics-binding
  labels:
    project: ai-coding-agent
subjects:
  - kind: ServiceAccount
    name: backend-sa
    namespace: ai-coding-agent
  - kind: ServiceAccount
    name: frontend-sa
    namespace: ai-coding-agent
roleRef:
  kind: ClusterRole
  name: ai-coding-agent-metrics-role
  apiGroup: rbac.authorization.k8s.io
