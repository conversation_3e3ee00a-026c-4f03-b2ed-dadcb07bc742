"""
Supabase client setup and helpers.
"""
import os
from typing import Any, Dict, List, Optional
from supabase import create_client, Client

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
_supabase: Optional[Client] = None

def get_supabase() -> Client:
    global _supabase
    if _supabase is None:
        if not SUPABASE_URL or not SUPABASE_KEY:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in the environment.")
        _supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    return _supabase

def query_table(table: str, filters: Optional[Dict[str, Any]] = None) -> Any:
    client = get_supabase()
    query = client.table(table).select("*")
    if filters:
        for k, v in filters.items():
            query = query.eq(k, v)
    return query.execute()

def insert_row(table: str, row: Dict[str, Any]) -> Any:
    client = get_supabase()
    return client.table(table).insert(row).execute()

def upsert_embedding(table: str, id: str, embedding: List[float], metadata: Dict[str, Any]) -> Any:
    client = get_supabase()
    data = {"id": id, "embedding": embedding, **metadata}
    return client.table(table).upsert(data).execute()

# Async helpers (if needed)
async def async_query_table(table: str, filters: Optional[Dict[str, Any]] = None) -> Any:
    return query_table(table, filters)

async def async_insert_row(table: str, row: Dict[str, Any]) -> Any:
    return insert_row(table, row)

async def async_upsert_embedding(table: str, id: str, embedding: List[float], metadata: Dict[str, Any]) -> Any:
    return upsert_embedding(table, id, embedding, metadata)
