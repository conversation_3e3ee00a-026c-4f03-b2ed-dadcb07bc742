// src/service-worker.js
this.addEventListener('install', event => {
  this.skipWaiting();
});

this.addEventListener('activate', event => {
  event.waitUntil(this.clients.claim());
});

this.addEventListener('fetch', event => {
  event.respondWith(
    caches.open('v1').then(cache => {
      return cache.match(event.request).then(response => {
        return response || fetch(event.request).then(networkResponse => {
          // Only cache GET requests
          if (event.request.method === 'GET') {
            cache.put(event.request, networkResponse.clone());
          }
          return networkResponse;
        });
      });
    })
  );
});
