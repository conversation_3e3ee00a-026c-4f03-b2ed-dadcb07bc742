import requests
import subprocess
import time
import logging
from datetime import datetime

PROMETHEUS_URL = "http://localhost:9090/api/v1/query"
BACKEND_SERVICE = "ai-coding-agent-api-prod"
FRONTEND_SERVICE = "ai-coding-agent-frontend-prod"
DOCKER_COMPOSE_FILE = "docker-compose.yml"
MIN_REPLICAS = 2
MAX_REPLICAS = 10
COOLDOWN_SECONDS = 300
CPU_THRESHOLD = 0.7  # 70%
MEM_THRESHOLD = 0.8  # 80%
CUSTOM_METRIC = "rate(http_requests_total[1m])"
CUSTOM_THRESHOLD = 20

logging.basicConfig(filename="auto_scaler.log", level=logging.INFO)
last_scale_time = 0

def query_prometheus(metric):
    resp = requests.get(PROMETHEUS_URL, params={"query": metric})
    if resp.status_code == 200:
        result = resp.json()["data"]["result"]
        if result:
            return float(result[0]["value"][1])
    return None

def get_current_replicas(service):
    # Parse docker-compose.yml for current replicas
    import yaml
    with open(DOCKER_COMPOSE_FILE) as f:
        compose = yaml.safe_load(f)
    return compose["services"][service]["deploy"].get("replicas", MIN_REPLICAS)

def set_replicas(service, count):
    import yaml
    with open(DOCKER_COMPOSE_FILE) as f:
        compose = yaml.safe_load(f)
    compose["services"][service]["deploy"]["replicas"] = count
    with open(DOCKER_COMPOSE_FILE, "w") as f:
        yaml.dump(compose, f)


def scale_services(backend_replicas, frontend_replicas):
    subprocess.run([
        "docker-compose", "up", "-d",
        f"--scale", f"{BACKEND_SERVICE}={backend_replicas}",
        f"--scale", f"{FRONTEND_SERVICE}={frontend_replicas}"
    ])
    logging.info(f"[{datetime.now()}] Scaled backend to {backend_replicas}, frontend to {frontend_replicas}")
    # Send notification (placeholder)
    print(f"Scaling event: backend={backend_replicas}, frontend={frontend_replicas}")


def health_check(service):
    # Placeholder: implement actual health check logic
    return True


def main():
    global last_scale_time
    while True:
        now = time.time()
        if now - last_scale_time < COOLDOWN_SECONDS:
            time.sleep(60)
            continue
        cpu = query_prometheus('avg(rate(node_cpu_seconds_total{mode!="idle"}[1m]))')
        mem = query_prometheus('1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)')
        custom = query_prometheus(CUSTOM_METRIC)
        backend_replicas = get_current_replicas(BACKEND_SERVICE)
        frontend_replicas = get_current_replicas(FRONTEND_SERVICE)
        scale_needed = False
        # Scaling logic
        if cpu and cpu > CPU_THRESHOLD and backend_replicas < MAX_REPLICAS:
            backend_replicas += 1
            scale_needed = True
        if mem and mem > MEM_THRESHOLD and frontend_replicas < MAX_REPLICAS:
            frontend_replicas += 1
            scale_needed = True
        if custom and custom > CUSTOM_THRESHOLD and backend_replicas < MAX_REPLICAS:
            backend_replicas += 1
            scale_needed = True
        # Health check before scaling down
        if cpu and cpu < CPU_THRESHOLD/2 and backend_replicas > MIN_REPLICAS and health_check(BACKEND_SERVICE):
            backend_replicas -= 1
            scale_needed = True
        if mem and mem < MEM_THRESHOLD/2 and frontend_replicas > MIN_REPLICAS and health_check(FRONTEND_SERVICE):
            frontend_replicas -= 1
            scale_needed = True
        if scale_needed:
            set_replicas(BACKEND_SERVICE, backend_replicas)
            set_replicas(FRONTEND_SERVICE, frontend_replicas)
            scale_services(backend_replicas, frontend_replicas)
            last_scale_time = now
        time.sleep(60)

if __name__ == "__main__":
    main()
