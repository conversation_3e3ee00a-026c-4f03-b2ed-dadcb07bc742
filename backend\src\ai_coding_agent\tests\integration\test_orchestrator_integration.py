"""
Integration tests for Enhanced Orchestrator - Phase A2

This module contains integration tests that test the full orchestrator workflow
including real task dispatch, model selection, and end-to-end functionality.
"""

import pytest
import asyncio
from unittest.mock import patch, AsyncMock, Mock
from datetime import datetime

from ai_coding_agent.orchestrator import (
    EnhancedOrchestrator,
    TaskContext,
    TaskType,
    TaskComplexity,
    dispatch_to_agent,
    get_model_analytics,
    get_load_balancing_status
)


@pytest.fixture
def integration_config():
    """Configuration for integration tests."""
    return {
        "providers": {
            "ollama": {
                "host": "http://localhost:11434",
                "models": {
                    "llama3.2:3b": {
                        "role": "architect",
                        "agents": ["architect"],
                        "description": "Planning and coordination"
                    },
                    "starcoder2:3b": {
                        "role": "frontend",
                        "agents": ["frontend"],
                        "description": "UI and frontend development"
                    },
                    "deepseek-coder:6.7b-instruct": {
                        "role": "backend",
                        "agents": ["backend"],
                        "description": "Backend development"
                    }
                }
            }
        },
        "routing": {
            "architect": {
                "primary": "llama3.2:3b",
                "secondary": "deepseek-coder:6.7b-instruct",
                "fallback": "starcoder2:3b"
            },
            "frontend": {
                "primary": "starcoder2:3b",
                "secondary": "llama3.2:3b",
                "fallback": "deepseek-coder:6.7b-instruct"
            },
            "backend": {
                "primary": "deepseek-coder:6.7b-instruct",
                "secondary": "starcoder2:3b",
                "fallback": "llama3.2:3b"
            }
        },
        "load_balancing": {
            "strategy": "performance_weighted",
            "health_weight": 0.4,
            "performance_weight": 0.3,
            "availability_weight": 0.3
        },
        "model_analytics": {
            "track_performance": True,
            "track_quality_scores": True
        },
        "performance_settings": {
            "request_timeout": 120,
            "retry_attempts": 2,
            "backoff_factor": 1.5
        },
        "quality_thresholds": {
            "minimum_confidence": 0.7,
            "context_relevance": 0.8
        }
    }


@pytest.fixture
def mock_ollama_response():
    """Mock successful Ollama response."""
    return {
        "response": """
        Here's a React component for user display:

        ```jsx
        function UserProfile({ user }) {
            return (
                <div className="user-profile">
                    <h2>{user.name}</h2>
                    <p>{user.email}</p>
                    <p>{user.role}</p>
                </div>
            );
        }

        export default UserProfile;
        ```

        This component accepts a user prop and displays the user's name, email, and role.
        Next steps: Add styling and implement click handlers for user actions.
        """
    }


class TestFullTaskDispatch:
    """Test complete task dispatch workflow."""

    @pytest.mark.asyncio
    async def test_successful_task_dispatch(self, integration_config, mock_ollama_response):
        """Test successful end-to-end task dispatch."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            # Setup mock configuration
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = integration_config["performance_settings"]
            mock_config_instance.get_quality_thresholds.return_value = integration_config["quality_thresholds"]
            mock_config_class.return_value = mock_config_instance

            # Setup mock HTTP client
            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = mock_ollama_response
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                # Create orchestrator
                orchestrator = EnhancedOrchestrator()

                # Create task context
                context = TaskContext(
                    project_id="test_project",
                    user_id="test_user",
                    session_id="test_session"
                )

                # Dispatch task
                result = await orchestrator.dispatch_to_agent(
                    agent_name="frontend",
                    task="Create a React component for displaying user information",
                    context=context,
                    task_type=TaskType.UI_COMPONENT,
                    complexity=TaskComplexity.MODERATE
                )

                # Verify result
                assert result.agent_name == "frontend"
                assert result.task_type == TaskType.UI_COMPONENT
                assert result.status == "success"
                assert result.model_used == "starcoder2:3b"
                assert result.confidence_score > 0.7
                assert result.quality_score > 0.7
                assert len(result.artifacts) > 0
                assert any(artifact["type"] == "code" for artifact in result.artifacts)

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_task_dispatch_with_retry(self, integration_config):
        """Test task dispatch with retry on failure."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = integration_config["performance_settings"]
            mock_config_instance.get_quality_thresholds.return_value = integration_config["quality_thresholds"]
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()

                # First call fails, second succeeds
                mock_client.post.side_effect = [
                    Exception("Connection error"),
                    Mock(status_code=200, json=lambda: {"response": "Success"})
                ]
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                result = await orchestrator.dispatch_to_agent(
                    agent_name="backend",
                    task="Create an API endpoint",
                    context=context
                )

                # Should have retried and succeeded
                assert result.status == "success"
                assert mock_client.post.call_count == 2

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_task_dispatch_fallback_on_failure(self, integration_config):
        """Test task dispatch fallback when all retries fail."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = integration_config["performance_settings"]
            mock_config_instance.get_quality_thresholds.return_value = integration_config["quality_thresholds"]
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_client.post.side_effect = Exception("Model unavailable")
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                result = await orchestrator.dispatch_to_agent(
                    agent_name="backend",
                    task="Create an API endpoint",
                    context=context
                )

                # Should return error result
                assert result.status == "error"
                assert result.model_used == "error"

                await orchestrator.close()


class TestModelSelection:
    """Test model selection under different conditions."""

    @pytest.mark.asyncio
    async def test_healthy_model_selection(self, integration_config):
        """Test model selection when all models are healthy."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock(status_code=200)
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()

                # Test different agents
                agents_and_expected = [
                    ("architect", "llama3.2:3b"),
                    ("frontend", "starcoder2:3b"),
                    ("backend", "deepseek-coder:6.7b-instruct")
                ]

                for agent, expected_model in agents_and_expected:
                    selected = await orchestrator.route_model_by_task(
                        agent, TaskType.CODE_GENERATION, TaskComplexity.SIMPLE
                    )
                    assert selected == expected_model

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_unhealthy_model_fallback(self, integration_config):
        """Test model fallback when primary model is unhealthy."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()

                # Simulate primary model failure, secondary success
                def mock_post(*args, **kwargs):
                    model_name = kwargs.get('json', {}).get('model', '')
                    if model_name == "starcoder2:3b":  # Primary model fails
                        return Mock(status_code=500)
                    else:  # Other models succeed
                        return Mock(status_code=200)

                mock_client.post.side_effect = mock_post
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()

                # Should fall back to secondary/fallback model
                selected = await orchestrator.route_model_by_task(
                    "frontend", TaskType.UI_COMPONENT, TaskComplexity.SIMPLE
                )
                assert selected in ["llama3.2:3b", "deepseek-coder:6.7b-instruct"]

                await orchestrator.close()


class TestLoadBalancingIntegration:
    """Test load balancing in full workflow."""

    @pytest.mark.asyncio
    async def test_round_robin_load_balancing(self, integration_config):
        """Test round robin load balancing across multiple requests."""
        # Modify config for round robin
        integration_config["load_balancing"]["strategy"] = "round_robin"

        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock(status_code=200)
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()

                # Make multiple requests to same agent type
                selected_models = []
                for _ in range(6):
                    model = await orchestrator.route_model_by_task(
                        "frontend", TaskType.UI_COMPONENT, TaskComplexity.SIMPLE
                    )
                    selected_models.append(model)

                # Should cycle through available models
                unique_models = set(selected_models)
                assert len(unique_models) > 1  # Should use multiple models

                await orchestrator.close()


class TestAnalyticsIntegration:
    """Test analytics in full workflow."""

    @pytest.mark.asyncio
    async def test_performance_tracking(self, integration_config, mock_ollama_response):
        """Test that performance metrics are tracked during task execution."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = integration_config
            mock_config_instance.get_routing_config.return_value = integration_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = mock_ollama_response
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                # Execute a task
                result = await orchestrator.dispatch_to_agent(
                    agent_name="frontend",
                    task="Create a component",
                    context=context
                )

                # Check that metrics were recorded
                model_used = result.model_used
                assert model_used in orchestrator.model_metrics

                metrics = orchestrator.model_metrics[model_used]
                assert metrics.success_count > 0
                assert len(metrics.response_times) > 0
                assert len(metrics.quality_scores) > 0

                # Get analytics
                analytics = await orchestrator.get_model_analytics()
                assert model_used in analytics["models"]
                assert analytics["models"][model_used]["total_requests"] > 0

                await orchestrator.close()


class TestConvenienceFunctions:
    """Test convenience functions for orchestrator."""

    @pytest.mark.asyncio
    async def test_dispatch_to_agent_convenience(self, integration_config, mock_ollama_response):
        """Test the convenience dispatch_to_agent function."""
        with patch('ai_coding_agent.orchestrator.orchestrator') as mock_orchestrator:
            mock_result = Mock()
            mock_result.agent_name = "frontend"
            mock_result.status = "success"
            mock_orchestrator.dispatch_to_agent.return_value = mock_result

            result = await dispatch_to_agent(
                agent_name="frontend",
                task="Create component"
            )

            assert result.agent_name == "frontend"
            assert result.status == "success"
            mock_orchestrator.dispatch_to_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_analytics_convenience_functions(self):
        """Test analytics convenience functions."""
        with patch('ai_coding_agent.orchestrator.orchestrator') as mock_orchestrator:
            mock_analytics = {"models": {}, "summary": {}}
            mock_orchestrator.get_model_analytics.return_value = mock_analytics

            mock_lb_status = {"strategy": "round_robin"}
            mock_orchestrator.get_load_balancing_status.return_value = mock_lb_status

            # Test analytics function
            analytics = await get_model_analytics()
            assert analytics == mock_analytics

            # Test load balancing function
            lb_status = await get_load_balancing_status()
            assert lb_status == mock_lb_status


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
