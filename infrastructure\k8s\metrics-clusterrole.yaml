apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: ai-coding-agent-metrics-role
  labels:
    project: ai-coding-agent
rules:
  - apiGroups: [""]
    resources: ["pods", "services", "endpoints"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["metrics.k8s.io"]
    resources: ["pods", "nodes"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["health"]
    verbs: ["get"]
