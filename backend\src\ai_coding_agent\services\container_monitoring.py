"""
Container resource monitoring and alerting service.

This module provides comprehensive monitoring of Docker containers
including resource usage tracking, alerting, and automated responses
to resource violations.
"""

import asyncio
import time
import psutil
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
from enum import Enum
import logging
import docker
from docker.errors import DockerException

from ..services.audit_service import log_audit_event, get_audit_service, AdminAuditAction, AuditSeverity, AuditCategory, AuditLevel
from ..config import settings

# Configure monitoring logger
monitoring_logger = logging.getLogger("system.monitoring")
monitoring_logger.setLevel(logging.INFO)


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class ResourceType(Enum):
    """Types of resources to monitor."""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"


@dataclass
class ResourceThreshold:
    """Resource threshold configuration."""
    resource_type: ResourceType
    warning_threshold: float  # Percentage
    critical_threshold: float  # Percentage
    emergency_threshold: float  # Percentage
    check_interval: int  # Seconds


@dataclass
class ResourceUsage:
    """Current resource usage data."""
    cpu_percent: float
    memory_percent: float
    memory_usage_mb: float
    memory_limit_mb: float
    disk_usage_mb: float
    network_rx_mb: float
    network_tx_mb: float
    timestamp: datetime


@dataclass
class Alert:
    """Resource alert data."""
    container_id: str
    container_name: str
    resource_type: ResourceType
    severity: AlertSeverity
    current_value: float
    threshold: float
    message: str
    timestamp: datetime
    resolved: bool = False


class ContainerResourceMonitor:
    """Comprehensive container resource monitoring system."""

    def __init__(self):
        """Initialize the container monitor."""
        self.docker_client: Optional[docker.DockerClient] = None
        self.monitoring_active = False
        self.resource_data: Dict[str, List[ResourceUsage]] = {}
        self.active_alerts: Dict[str, List[Alert]] = {}
        self.alert_history: List[Alert] = []

        # Default resource thresholds
        self.thresholds = {
            ResourceType.CPU: ResourceThreshold(
                resource_type=ResourceType.CPU,
                warning_threshold=70.0,
                critical_threshold=85.0,
                emergency_threshold=95.0,
                check_interval=30
            ),
            ResourceType.MEMORY: ResourceThreshold(
                resource_type=ResourceType.MEMORY,
                warning_threshold=80.0,
                critical_threshold=90.0,
                emergency_threshold=95.0,
                check_interval=30
            ),
            ResourceType.DISK: ResourceThreshold(
                resource_type=ResourceType.DISK,
                warning_threshold=85.0,
                critical_threshold=95.0,
                emergency_threshold=98.0,
                check_interval=60
            )
        }

        self._initialize_docker_client()

    def _initialize_docker_client(self):
        """Initialize Docker client connection."""
        try:
            self.docker_client = docker.from_env()
            self.docker_client.ping()
            monitoring_logger.info("Docker client initialized successfully")
        except DockerException as e:
            monitoring_logger.error(f"Failed to initialize Docker client: {e}")
            self.docker_client = None

    async def start_monitoring(self):
        """Start the container monitoring service."""
        if not self.docker_client:
            monitoring_logger.error("Cannot start monitoring: Docker client not available")
            return

        self.monitoring_active = True
        monitoring_logger.info("Container resource monitoring started")

        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self._monitor_containers()),
            asyncio.create_task(self._check_alerts()),
            asyncio.create_task(self._cleanup_old_data())
        ]

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            monitoring_logger.error(f"Monitoring error: {e}")
        finally:
            self.monitoring_active = False

    async def stop_monitoring(self):
        """Stop the container monitoring service."""
        self.monitoring_active = False
        monitoring_logger.info("Container resource monitoring stopped")

    async def _monitor_containers(self):
        """Main monitoring loop for container resources."""
        while self.monitoring_active:
            try:
                if not self.docker_client:
                    monitoring_logger.warning("Docker client not available for monitoring")
                    await asyncio.sleep(30)  # Wait before retrying
                    continue

                containers = self.docker_client.containers.list()

                for container in containers:
                    try:
                        usage = await self._get_container_usage(container)
                        if usage:
                            self._store_usage_data(container.id, usage)
                            await self._check_thresholds(container, usage)
                    except Exception as e:
                        monitoring_logger.warning(f"Error monitoring container {container.id}: {e}")

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                monitoring_logger.error(f"Container monitoring loop error: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _get_container_usage(self, container) -> Optional[ResourceUsage]:
        """Get current resource usage for a container."""
        try:
            # Get container stats
            stats = container.stats(stream=False)

            # Calculate CPU usage
            cpu_percent = self._calculate_cpu_percent(stats)

            # Calculate memory usage
            memory_usage = stats['memory_stats'].get('usage', 0)
            memory_limit = stats['memory_stats'].get('limit', 0)
            memory_percent = (memory_usage / memory_limit * 100) if memory_limit > 0 else 0

            # Get network stats
            network_stats = stats.get('networks', {})
            network_rx = sum(net.get('rx_bytes', 0) for net in network_stats.values())
            network_tx = sum(net.get('tx_bytes', 0) for net in network_stats.values())

            # Get disk usage (approximate)
            disk_usage = 0
            try:
                disk_usage = sum(
                    blk.get('value', 0)
                    for blk in stats.get('blkio_stats', {}).get('io_service_bytes_recursive', [])
                    if blk.get('op') == 'Read'
                )
            except (KeyError, TypeError):
                pass

            return ResourceUsage(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_usage_mb=memory_usage / (1024 * 1024),
                memory_limit_mb=memory_limit / (1024 * 1024),
                disk_usage_mb=disk_usage / (1024 * 1024),
                network_rx_mb=network_rx / (1024 * 1024),
                network_tx_mb=network_tx / (1024 * 1024),
                timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            monitoring_logger.warning(f"Error getting container usage: {e}")
            return None

    def _calculate_cpu_percent(self, stats: Dict) -> float:
        """Calculate CPU usage percentage from Docker stats."""
        try:
            cpu_stats = stats['cpu_stats']
            precpu_stats = stats['precpu_stats']

            cpu_usage = cpu_stats['cpu_usage']['total_usage']
            precpu_usage = precpu_stats['cpu_usage']['total_usage']

            system_usage = cpu_stats['system_cpu_usage']
            presystem_usage = precpu_stats['system_cpu_usage']

            cpu_count = cpu_stats.get('online_cpus', len(cpu_stats['cpu_usage'].get('percpu_usage', [1])))

            cpu_delta = cpu_usage - precpu_usage
            system_delta = system_usage - presystem_usage

            if system_delta > 0 and cpu_delta > 0:
                return (cpu_delta / system_delta) * cpu_count * 100.0

            return 0.0

        except (KeyError, ZeroDivisionError, TypeError):
            return 0.0

    def _store_usage_data(self, container_id: str, usage: ResourceUsage):
        """Store usage data for historical tracking."""
        if container_id not in self.resource_data:
            self.resource_data[container_id] = []

        self.resource_data[container_id].append(usage)

        # Keep only last 24 hours of data
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        self.resource_data[container_id] = [
            u for u in self.resource_data[container_id]
            if u.timestamp > cutoff_time
        ]

    async def _check_thresholds(self, container, usage: ResourceUsage):
        """Check if resource usage exceeds thresholds and create alerts."""
        container_id = container.id
        container_name = container.name

        # Check CPU threshold
        await self._check_resource_threshold(
            container_id, container_name, ResourceType.CPU,
            usage.cpu_percent, self.thresholds[ResourceType.CPU]
        )

        # Check memory threshold
        await self._check_resource_threshold(
            container_id, container_name, ResourceType.MEMORY,
            usage.memory_percent, self.thresholds[ResourceType.MEMORY]
        )

    async def _check_resource_threshold(
        self,
        container_id: str,
        container_name: str,
        resource_type: ResourceType,
        current_value: float,
        threshold: ResourceThreshold
    ):
        """Check a specific resource against its threshold."""
        severity = None
        threshold_value = 0

        if current_value >= threshold.emergency_threshold:
            severity = AlertSeverity.EMERGENCY
            threshold_value = threshold.emergency_threshold
        elif current_value >= threshold.critical_threshold:
            severity = AlertSeverity.CRITICAL
            threshold_value = threshold.critical_threshold
        elif current_value >= threshold.warning_threshold:
            severity = AlertSeverity.WARNING
            threshold_value = threshold.warning_threshold

        if severity:
            await self._create_alert(
                container_id, container_name, resource_type,
                severity, current_value, threshold_value
            )

    async def _create_alert(
        self,
        container_id: str,
        container_name: str,
        resource_type: ResourceType,
        severity: AlertSeverity,
        current_value: float,
        threshold: float
    ):
        """Create and process a resource alert."""
        alert = Alert(
            container_id=container_id,
            container_name=container_name,
            resource_type=resource_type,
            severity=severity,
            current_value=current_value,
            threshold=threshold,
            message=f"{resource_type.value.upper()} usage {current_value:.1f}% exceeds {severity.value} threshold {threshold:.1f}%",
            timestamp=datetime.now(timezone.utc)
        )

        # Store alert
        if container_id not in self.active_alerts:
            self.active_alerts[container_id] = []

        self.active_alerts[container_id].append(alert)
        self.alert_history.append(alert)

        # Log alert
        monitoring_logger.warning(f"Resource alert: {alert.message}")

        # Audit log
        log_audit_event(
            category=AuditCategory.CONTAINER,
            level=AuditLevel.WARNING if severity != AlertSeverity.EMERGENCY else AuditLevel.CRITICAL,
            message=f"Container resource alert: {alert.message}",
            container_id=container_id,
            container_name=container_name,
            resource_type=resource_type.value,
            severity=severity.value,
            current_value=current_value,
            threshold=threshold
        )

        # Take automated action for critical alerts
        if severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]:
            await self._handle_critical_alert(alert)

    async def _handle_critical_alert(self, alert: Alert):
        """Handle critical resource alerts with automated responses."""
        try:
            if not self.docker_client:
                monitoring_logger.error("Docker client not available for critical alert handling")
                return

            container = self.docker_client.containers.get(alert.container_id)

            if alert.severity == AlertSeverity.EMERGENCY:
                # For emergency alerts, consider restarting the container
                monitoring_logger.critical(f"Emergency alert for container {container.name}: {alert.message}")

                # Log container status for debugging
                monitoring_logger.info(f"Container {container.name} status: {container.status}")

                # Log the emergency action
                log_audit_event(
                    category=AuditCategory.CONTAINER,
                    level=AuditLevel.CRITICAL,
                    message=f"Emergency resource alert triggered for container {alert.container_name}",
                    action="emergency_alert",
                    container_id=alert.container_id,
                    resource_type=alert.resource_type.value,
                    current_value=alert.current_value,
                    threshold=alert.threshold,
                    action_taken="logged_for_manual_intervention"
                )

        except Exception as e:
            monitoring_logger.error(f"Error handling critical alert: {e}")

    async def _check_alerts(self):
        """Periodic alert checking and cleanup."""
        while self.monitoring_active:
            try:
                # Clean up resolved alerts
                for container_id in list(self.active_alerts.keys()):
                    self.active_alerts[container_id] = [
                        alert for alert in self.active_alerts[container_id]
                        if not alert.resolved and
                        (datetime.now(timezone.utc) - alert.timestamp).total_seconds() < 3600  # Keep for 1 hour
                    ]

                    if not self.active_alerts[container_id]:
                        del self.active_alerts[container_id]

                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                monitoring_logger.error(f"Alert checking error: {e}")
                await asyncio.sleep(300)

    async def _cleanup_old_data(self):
        """Clean up old monitoring data."""
        while self.monitoring_active:
            try:
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)

                # Clean up resource data
                for container_id in list(self.resource_data.keys()):
                    self.resource_data[container_id] = [
                        usage for usage in self.resource_data[container_id]
                        if usage.timestamp > cutoff_time
                    ]

                    if not self.resource_data[container_id]:
                        del self.resource_data[container_id]

                # Clean up alert history (keep 7 days)
                alert_cutoff = datetime.now(timezone.utc) - timedelta(days=7)
                self.alert_history = [
                    alert for alert in self.alert_history
                    if alert.timestamp > alert_cutoff
                ]

                await asyncio.sleep(3600)  # Clean up every hour

            except Exception as e:
                monitoring_logger.error(f"Data cleanup error: {e}")
                await asyncio.sleep(3600)

    def get_container_status(self, container_id: str) -> Dict[str, Any]:
        """Get current status and recent usage for a container."""
        if container_id not in self.resource_data:
            return {"error": "No data available for container"}

        recent_usage = self.resource_data[container_id][-1] if self.resource_data[container_id] else None
        active_alerts = self.active_alerts.get(container_id, [])

        return {
            "container_id": container_id,
            "current_usage": {
                "cpu_percent": recent_usage.cpu_percent if recent_usage else 0,
                "memory_percent": recent_usage.memory_percent if recent_usage else 0,
                "memory_usage_mb": recent_usage.memory_usage_mb if recent_usage else 0,
                "timestamp": recent_usage.timestamp.isoformat() if recent_usage else None
            },
            "active_alerts": len(active_alerts),
            "alert_details": [
                {
                    "resource_type": alert.resource_type.value,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat()
                }
                for alert in active_alerts[-5:]  # Last 5 alerts
            ]
        }

    def get_system_overview(self) -> Dict[str, Any]:
        """Get overview of all monitored containers."""
        total_containers = len(self.resource_data)
        total_alerts = sum(len(alerts) for alerts in self.active_alerts.values())

        # Calculate average resource usage
        all_usage = []
        for usage_list in self.resource_data.values():
            if usage_list:
                all_usage.append(usage_list[-1])

        avg_cpu = sum(u.cpu_percent for u in all_usage) / len(all_usage) if all_usage else 0
        avg_memory = sum(u.memory_percent for u in all_usage) / len(all_usage) if all_usage else 0

        return {
            "total_containers": total_containers,
            "total_active_alerts": total_alerts,
            "average_cpu_usage": avg_cpu,
            "average_memory_usage": avg_memory,
            "monitoring_active": self.monitoring_active,
            "last_update": datetime.now(timezone.utc).isoformat()
        }


# Global container monitor instance
container_monitor = ContainerResourceMonitor()
