#!/bin/bash
# PostgreSQL Replica Initialization Script for AI Coding Agent

set -e

PRIMARY_HOST=${POSTGRES_PRIMARY_HOST:-postgres-primary}
PRIMARY_PORT=${POSTGRES_PRIMARY_PORT:-5432}
REPLICA_USER=${POSTGRES_REPLICA_USER:-replica}
REPLICA_PASSWORD=${POSTGRES_REPLICA_PASSWORD:-replica_password}
POSTGRES_DB=${POSTGRES_DB:-ai_coding_agent}

echo "🚀 Initializing PostgreSQL Replica for AI Coding Agent..."

# Wait for primary to be ready
echo "⏳ Waiting for primary PostgreSQL to be ready..."
for i in {1..60}; do
    if pg_isready -h $PRIMARY_HOST -p $PRIMARY_PORT -U postgres >/dev/null 2>&1; then
        echo "✅ Primary PostgreSQL is ready"
        break
    fi
    echo "   Attempt $i/60: Waiting for primary..."
    sleep 2
done

# Check if this is already a replica
if [ -f "$PGDATA/recovery.conf" ] || [ -f "$PGDATA/standby.signal" ]; then
    echo "✅ Already configured as replica"
    exec postgres
fi

# Stop PostgreSQL if running
pg_ctl stop -D "$PGDATA" -m fast || true

# Remove existing data directory
rm -rf "$PGDATA"/*

echo "🔧 Creating base backup from primary..."

# Create base backup
PGPASSWORD=$REPLICA_PASSWORD pg_basebackup \
    -h $PRIMARY_HOST \
    -p $PRIMARY_PORT \
    -U $REPLICA_USER \
    -D "$PGDATA" \
    -W \
    -v \
    -P \
    -R \
    -X stream

# Configure replica-specific settings
echo "📝 Configuring replica settings..."

# Create standby.signal for PostgreSQL 12+
touch "$PGDATA/standby.signal"

# Configure postgresql.conf for replica
cat >> "$PGDATA/postgresql.conf" << EOF

# Replica-specific configuration
hot_standby = on
max_standby_streaming_delay = 30s
max_standby_archive_delay = 30s
wal_receiver_status_interval = 10s
hot_standby_feedback = on
wal_receiver_timeout = 60s

# Logging
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# Performance tuning for replica
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
EOF

# Set proper permissions
chown -R postgres:postgres "$PGDATA"
chmod 700 "$PGDATA"

echo "✅ PostgreSQL replica configuration complete!"

# Start PostgreSQL
echo "🚀 Starting PostgreSQL replica..."
exec postgres
