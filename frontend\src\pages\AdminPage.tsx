import React, { useState } from 'react';
import {
  Settings,
  Users,
  Activity,
  Database,
  Cpu,
  Shield,
  Container,
  BarChart3
} from 'lucide-react';
import ModelConfigAdmin from '../components/admin/ModelConfigAdmin';
import ContainerManagement from '../components/admin/ContainerManagement';
import ResourceUsageVisualization from '../components/admin/ResourceUsageVisualization';

interface AdminTab {
  id: string;
  name: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  description: string;
}

const AdminPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('models');

  const adminTabs: AdminTab[] = [
    {
      id: 'models',
      name: 'AI Models',
      icon: <Cpu className="w-5 h-5" />,
      component: <ModelConfigAdmin />,
      description: 'Configure AI models for each agent role'
    },
    {
      id: 'containers',
      name: 'Container Management',
      icon: <Container className="w-5 h-5" />,
      component: <ContainerManagement />,
      description: 'Monitor and manage user containers'
    },
    {
      id: 'resources',
      name: 'Resource Usage',
      icon: <BarChart3 className="w-5 h-5" />,
      component: <ResourceUsageVisualization />,
      description: 'Real-time resource monitoring and visualization'
    },
    {
      id: 'users',
      name: 'User Management',
      icon: <Users className="w-5 h-5" />,
      component: (
        <div className="p-8 text-center">
          <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            User Management
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            User management features coming soon...
          </p>
        </div>
      ),
      description: 'Manage user accounts and permissions'
    },
    {
      id: 'projects',
      name: 'Project Isolation',
      icon: <Shield className="w-5 h-5" />,
      component: (
        <div className="p-8 text-center">
          <Shield className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Project Isolation
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Project isolation management coming soon...
          </p>
        </div>
      ),
      description: 'Configure user project isolation and security'
    },
    {
      id: 'system',
      name: 'System Health',
      icon: <Activity className="w-5 h-5" />,
      component: (
        <div className="p-8 text-center">
          <Activity className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            System Health
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            System monitoring and health checks coming soon...
          </p>
        </div>
      ),
      description: 'Monitor system health and performance'
    },
    {
      id: 'database',
      name: 'Database',
      icon: <Database className="w-5 h-5" />,
      component: (
        <div className="p-8 text-center">
          <Database className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Database Management
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Database administration tools coming soon...
          </p>
        </div>
      ),
      description: 'Database administration and maintenance'
    }
  ];

  const activeTabData = adminTabs.find(tab => tab.id === activeTab);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center">
              <Settings className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Admin Dashboard
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Manage your AI Coding Agent platform
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Admin Tools
                </h2>
              </div>

              <nav className="p-2">
                {adminTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    {tab.icon}
                    <span className="ml-3">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Quick Stats */}
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                Quick Stats
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Active Users</span>
                  <span className="font-medium text-gray-900 dark:text-white">--</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Projects</span>
                  <span className="font-medium text-gray-900 dark:text-white">--</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">AI Models</span>
                  <span className="font-medium text-gray-900 dark:text-white">--</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">System Status</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                    Healthy
                  </span>
                </div>
              </div>
              {/* Grafana Dashboard Link */}
              <div className="mt-6">
                <a
                  href="/grafana" // Use relative path for Grafana in consolidated deployment
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md shadow hover:bg-blue-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M12.293 2.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-8 8a1 1 0 01-.707.293H4a1 1 0 01-1-1v-4a1 1 0 01.293-.707l8-8z" />
                  </svg>
                  Grafana Dashboard
                </a>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 min-h-[600px]">
              {/* Tab Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  {activeTabData?.icon}
                  <div className="ml-3">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {activeTabData?.name}
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {activeTabData?.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* Tab Content */}
              <div className="p-0">
                {activeTabData?.component}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
