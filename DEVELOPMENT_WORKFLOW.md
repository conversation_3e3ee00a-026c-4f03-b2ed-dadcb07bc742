# AI Coding Agent Development Workflow (2025+)

## Recommended Development Setup

1. **Use the root `docker-compose.yml` for all development and production work.**
   - This file is optimized for both environments and includes all essential services: consolidated app, PostgreSQL, Redis, and Nginx.

2. **Override environment variables for development:**
   - Create a `.env.development` file in the root or use environment overrides in the Compose file.
   - Example overrides:
     - `NODE_ENV=development`
     - `DEBUG=true`
     - `LOG_LEVEL=debug`
     - `REACT_APP_API_URL=/api`

3. **Mount source code for hot reloading:**
   - Add or uncomment volume mounts in the `app` service to map your local `backend/src` and `frontend/src` directories into the container.
   - Example:
     ```yaml
     volumes:
       - ./backend/src:/app/backend/src:rw
       - ./frontend/src:/app/frontend/src:rw
     ```

4. **Enable debug ports if needed:**
   - Add `ports:` to expose FastAPI, React, or debugger ports for local development.
   - Example:
     ```yaml
     ports:
       - "8000:8000"
       - "3000:3000"
       - "5678:5678" # Debugger
     ```

5. **Run Compose in development mode:**
   - Use: `docker-compose up --build`
   - For scaling or testing, use: `docker-compose up --scale app=1`

6. **Use Docker secrets for sensitive values:**
   - Place secrets in the `secrets/` directory as described in the Compose file.

7. **Validate your Compose file before running:**
   - Run: `docker-compose config`

8. **Monitor resource usage:**
   - Use `docker stats` to check container CPU and memory usage during development.

9. **For advanced development (hot reload, test containers):**
   - Temporarily copy relevant sections from `archive/docker-compose.dev.yml` if you need extra features, but revert to the minimal setup when done.

## Summary
- Always use the root `docker-compose.yml` for development and production.
- Keep your setup minimal and resource-efficient.
- Use environment overrides, volume mounts, and debug ports as needed.
- Archive legacy Compose files and avoid using them directly.

---
For more details, see `.copilot-rules.md` and `DEVELOPMENT_WORKFLOW.md`.
