# GitHub Copilot Custom Instructions

## Docker Container Naming Standards

When generating Docker Compose files, Dockerfiles, or any container configuration, always follow these naming conventions:

### Container Naming Rules
- Use **lowercase letters only** for consistency (a-z, 0-9, hyphens, underscores, dots)
- Maximum **63 characters** for DNS compatibility
- Use **hyphens** (`-`) to separate words for better readability
- Start with **alphanumeric character**

### Naming Pattern
Follow this format: `[project]-[service]-[environment]`

**Examples:**
```yaml
# Good naming examples
ai-coding-agent-backend-dev
ai-coding-agent-frontend-prod
ecommerce-api-staging
user-service-test
payment-db-prod
redis-cache-dev

# Bad naming examples (avoid these)
BackendAPI
db1
web
mycontainer
app
```

### Service Type Indicators
Always include the service type in container names:
- `-api` for API services
- `-db` for databases
- `-cache` for caching services
- `-ui` or `-frontend` for frontend applications
- `-worker` for background workers
- `-proxy` for reverse proxies

### Environment Suffixes
Always include environment information:
- `-dev` for development
- `-staging` for staging
- `-prod` for production
- `-test` for testing

### Specific Requirements
- **Be descriptive**: Name should clearly indicate the container's purpose
- **Avoid generic terms**: Don't use "web", "app", "db", "server" alone
- **Use consistent patterns**: Apply the same naming convention across all containers in the project
- **No special characters**: Avoid `!`, `@`, `#`, `$`, `%`, etc.

## Docker Compose Service Naming

When creating `docker-compose.yml` files, ensure service names match container names but can be shorter:

```yaml
services:
  backend:
    container_name: myproject-backend-dev

  frontend:
    container_name: myproject-frontend-dev

  database:
    container_name: myproject-postgres-dev
```

## Container Name + Replicas Best Practice

When using Docker Compose with `deploy.replicas` (multiple instances), **do not set `container_name`** for those services. Docker Compose cannot create multiple replicas with the same container name.

- Remove `container_name` from any service with `deploy.replicas > 1`.
- Let Docker auto-generate unique names for each replica (e.g., `ai-coding-agent-frontend-prod_1`, `ai-coding-agent-frontend-prod_2`).
- You may use `hostname` for predictable names if needed.
- Keep `container_name` only for singleton services (no replicas or replicas: 1).

**Example Fix:**
```yaml
# WRONG
services:
  frontend:
    image: ai-coding-agent-frontend
    container_name: ai-coding-agent-frontend-prod
    deploy:
      replicas: 3

# CORRECT
services:
  frontend:
    image: ai-coding-agent-frontend
    deploy:
      replicas: 3
```

**Validation:**
- After updating, run `docker-compose config` to validate.
- Deploy with `docker-compose up -d --scale frontend=3`.
- Check container names with `docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"`.


## Project Context
This project is an AI Coding Agent with the following services:
- Backend API (Python/FastAPI)
- Frontend (React/TypeScript)
- PostgreSQL database
- Redis cache
- Nginx proxy
- PgAdmin interface

Use the prefix `ai-coding-agent` for all containers in this project.

## 📚 Related Documentation

- **Primary Architecture Rules**: [../docs/.copilot-rules.md](../docs/.copilot-rules.md)
- **AI Agent Configuration**: [prompts/ai-agent-config.prompt.md](prompts/ai-agent-config.prompt.md)
- **Security Guidelines**: [prompts/docker-security.prompt.md](prompts/docker-security.prompt.md)
- **Multi-User Architecture**: [prompts/multiuser-architecture.prompt.md](prompts/multiuser-architecture.prompt.md)
- **Documentation Index**: [README.md](README.md)
