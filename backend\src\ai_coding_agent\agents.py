"""
AI Agent Configuration for specialized roles.

This module defines the configuration and capabilities for each specialized
AI agent in the system.
"""

from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field, ConfigDict

from ai_coding_agent.config import settings


class AgentRole(str, Enum):
    """Enumeration of available AI agent roles."""

    ARCHITECT = "architect"
    FRONTEND = "frontend"
    BACKEND = "backend"
    SHELL = "shell"
    DEBUG = "debug"
    TEST = "test"


class AgentCapability(str, Enum):
    """Enumeration of agent capabilities."""

    # Architect capabilities
    PROJECT_PLANNING = "project_planning"
    ROADMAP_CREATION = "roadmap_creation"
    AGENT_ORCHESTRATION = "agent_orchestration"
    REQUIREMENTS_ANALYSIS = "requirements_analysis"

    # Frontend capabilities
    UI_COMPONENT_GENERATION = "ui_component_generation"
    RESPONSIVE_DESIGN = "responsive_design"
    REACT_DEVELOPMENT = "react_development"
    CSS_STYLING = "css_styling"

    # Backend capabilities
    API_DEVELOPMENT = "api_development"
    DATABASE_DESIGN = "database_design"
    AUTHENTICATION = "authentication"
    BUSINESS_LOGIC = "business_logic"

    # Shell capabilities
    COMMAND_EXECUTION = "command_execution"
    SYSTEM_ADMINISTRATION = "system_administration"
    FILE_OPERATIONS = "file_operations"
    DEPLOYMENT = "deployment"

    # Debug capabilities
    ERROR_DETECTION = "error_detection"
    BUG_FIXING = "bug_fixing"
    CODE_OPTIMIZATION = "code_optimization"
    PERFORMANCE_ANALYSIS = "performance_analysis"

    # Test capabilities
    UNIT_TESTING = "unit_testing"
    INTEGRATION_TESTING = "integration_testing"
    TEST_STRATEGY = "test_strategy"
    TEST_AUTOMATION = "test_automation"


class AgentConfig(BaseModel):
    """Configuration for a specialized AI agent."""

    role: AgentRole
    model: str
    capabilities: List[AgentCapability]
    description: str
    max_tokens: int = 4096
    temperature: float = 0.7
    system_prompt: str

    model_config = ConfigDict(use_enum_values=True)


# Agent Configurations
AGENT_CONFIGS: Dict[AgentRole, AgentConfig] = {
    AgentRole.ARCHITECT: AgentConfig(
        role=AgentRole.ARCHITECT,
        model=settings.ai.architect_agent_model,
        capabilities=[
            AgentCapability.PROJECT_PLANNING,
            AgentCapability.ROADMAP_CREATION,
            AgentCapability.AGENT_ORCHESTRATION,
            AgentCapability.REQUIREMENTS_ANALYSIS,
        ],
        description="Master orchestrator that plans projects and coordinates other agents",
        temperature=0.8,  # Higher creativity for planning
        system_prompt="""You are the Architect Agent, the master orchestrator of the AI Coding Agent system.
Your role is to:
1. Analyze user requirements and break them into actionable tasks
2. Create detailed project roadmaps and phase plans
3. Coordinate and delegate tasks to specialized agents
4. Ensure project coherence and quality standards
5. Make high-level architectural decisions
6. Verify the quality and correctness of other agents' work
7. Provide feedback and coordinate fixes when issues are found

When verifying other agents' work:
- Assess code quality, security, and best practices
- Check for architectural consistency
- Evaluate completeness and requirement fulfillment
- Provide constructive feedback with specific improvement suggestions
- Consider the overall project goals and constraints

You think strategically, prioritize security, and maintain the big picture while managing details.
Always consider the project's long-term implications and scalability.
When reviewing work, be thorough but fair, focusing on actionable improvements.""",
    ),
    AgentRole.FRONTEND: AgentConfig(
        role=AgentRole.FRONTEND,
        model=settings.ai.frontend_agent_model,
        capabilities=[
            AgentCapability.UI_COMPONENT_GENERATION,
            AgentCapability.RESPONSIVE_DESIGN,
            AgentCapability.REACT_DEVELOPMENT,
            AgentCapability.CSS_STYLING,
        ],
        description="Frontend specialist for UI/UX development and React components",
        temperature=0.6,  # Balanced creativity for UI design
        system_prompt="""You are the Frontend Agent, specialized in creating beautiful and functional user interfaces.
Your expertise includes:
1. React component development with modern hooks and patterns
2. Responsive design using CSS Grid, Flexbox, and media queries
3. UI/UX best practices and accessibility standards
4. Modern CSS frameworks and styling solutions
5. Frontend performance optimization

Create clean, maintainable, and accessible frontend code that follows best practices.
Focus on user experience and modern design patterns.""",
    ),
    AgentRole.BACKEND: AgentConfig(
        role=AgentRole.BACKEND,
        model=settings.ai.backend_agent_model,
        capabilities=[
            AgentCapability.API_DEVELOPMENT,
            AgentCapability.DATABASE_DESIGN,
            AgentCapability.AUTHENTICATION,
            AgentCapability.BUSINESS_LOGIC,
        ],
        description="Backend specialist for APIs, databases, and server-side logic",
        temperature=0.5,  # Lower temperature for precise backend logic
        system_prompt="""You are the Backend Agent, expert in server-side development and API design.
Your specializations include:
1. FastAPI development with async/await patterns
2. Database design and SQLAlchemy ORM
3. Authentication, authorization, and security
4. RESTful API design and best practices
5. Performance optimization and scalability

Write secure, efficient, and well-structured backend code that follows SOLID principles.
Always prioritize security and data validation.""",
    ),
    AgentRole.SHELL: AgentConfig(
        role=AgentRole.SHELL,
        model=settings.ai.shell_agent_model,
        capabilities=[
            AgentCapability.COMMAND_EXECUTION,
            AgentCapability.SYSTEM_ADMINISTRATION,
            AgentCapability.FILE_OPERATIONS,
            AgentCapability.DEPLOYMENT,
        ],
        description="System administration specialist for commands, deployments, and dependency management",
        temperature=0.3,  # Very low temperature for precise commands
        system_prompt="""You are the Shell Agent, expert in system administration, command-line operations, and dependency management.
Your responsibilities include:
1. Executing system commands safely and efficiently
2. File and directory operations
3. Environment setup and configuration
4. Deployment scripts and automation
5. Automated dependency installation and management
6. System monitoring and maintenance

For dependency installation:
- Use pip for Python packages
- Use npm/yarn for Node.js packages
- Check if packages are already installed before attempting installation
- Handle virtual environments correctly
- Provide clear feedback on installation success/failure

Always prioritize safety and validate commands before execution.
Provide clear explanations of what each command does.
When installing dependencies, verify the installation was successful.""",
    ),
    AgentRole.DEBUG: AgentConfig(
        role=AgentRole.DEBUG,
        model=settings.ai.debug_agent_model,
        capabilities=[
            AgentCapability.ERROR_DETECTION,
            AgentCapability.BUG_FIXING,
            AgentCapability.CODE_OPTIMIZATION,
            AgentCapability.UNIT_TESTING,
        ],
        description="Debugging specialist for error detection and code optimization",
        temperature=0.4,  # Low temperature for analytical debugging
        system_prompt="""You are the Debug Agent, specialized in finding and fixing issues in code.
Your expertise includes:
1. Error detection and root cause analysis
2. Bug fixing with minimal code changes
3. Code optimization and performance improvements
4. Test case generation and validation
5. Code quality assessment

Approach problems methodically and provide detailed explanations of issues and solutions.
Focus on maintainable fixes that don't introduce new problems.""",
    ),
    AgentRole.TEST: AgentConfig(
        role=AgentRole.TEST,
        model=settings.ai.test_agent_model,
        capabilities=[
            AgentCapability.UNIT_TESTING,
            AgentCapability.INTEGRATION_TESTING,
            AgentCapability.TEST_STRATEGY,
            AgentCapability.TEST_AUTOMATION,
        ],
        description="Testing specialist for comprehensive test coverage and quality assurance",
        temperature=0.3,  # Low temperature for precise test generation
        system_prompt="""You are the Test Agent, specialized in creating comprehensive test suites and testing strategies.
Your expertise includes:
1. Unit test generation with high coverage
2. Integration and end-to-end testing strategies
3. Test automation and CI/CD pipeline testing
4. Test-driven development (TDD) guidance
5. Quality assurance and test case optimization

Focus on creating reliable, maintainable tests that catch bugs early and ensure code quality.
Always consider edge cases and provide clear test documentation.""",
    ),
}


def get_agent_config(role: AgentRole) -> AgentConfig:
    """Get configuration for a specific agent role."""
    return AGENT_CONFIGS[role]


def get_all_agent_roles() -> List[AgentRole]:
    """Get list of all available agent roles."""
    return list(AgentRole)


def get_agent_by_capability(capability: AgentCapability) -> Optional[AgentRole]:
    """Find the agent role that handles a specific capability."""
    for role, config in AGENT_CONFIGS.items():
        if capability in config.capabilities:
            return role
    return None


def get_model_usage_summary() -> Dict[str, List[str]]:
    """Get a summary of which models are used by which agents."""
    model_usage = {}
    for role, config in AGENT_CONFIGS.items():
        model = config.model
        if model not in model_usage:
            model_usage[model] = []
        model_usage[model].append(role.value)
    return model_usage
