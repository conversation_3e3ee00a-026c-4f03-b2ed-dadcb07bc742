"""
Roadmap Versioning Service Layer
Implements comprehensive versioning and change tracking for roadmaps.
"""

import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from uuid import uuid4

from sqlalchemy.orm import Session, selectinload
from sqlalchemy import select, and_, desc
from fastapi import HTTPException
from fastapi import status as http_status

from ai_coding_agent.models import (
    Roadmap, RoadmapVersion, Phase, Step,
    RoadmapVersionCreate, RoadmapVersionUpdate, RoadmapVersionResponse,
    VersionComparison
)


class VersioningService:
    """Service for managing roadmap versions and change tracking."""

    def __init__(self, db: Session, user_id: Optional[str] = None, user_email: Optional[str] = None):
        self.db = db
        self.user_id = user_id
        self.user_email = user_email

    @staticmethod
    def _utc_now() -> datetime:
        """Get current UTC time in a timezone-aware manner."""
        return datetime.now(timezone.utc)

    def create_version(
        self,
        roadmap_id: str,
        version_data: RoadmapVersionCreate
    ) -> RoadmapVersionResponse:
        """Create a new version of a roadmap."""
        try:
            # Get the roadmap with full hierarchy
            roadmap = (
                self.db.execute(
                    select(Roadmap)
                    .options(
                        selectinload(Roadmap.phases)
                            .selectinload(Phase.steps)
                            .selectinload(Step.tasks)
                    )
                    .where(Roadmap.id == roadmap_id)
                )
                .scalar_one_or_none()
            )

            if not roadmap:
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail=f"Roadmap {roadmap_id} not found"
                )

            # Get the latest version to determine next version number
            latest_version = (
                self.db.execute(
                    select(RoadmapVersion)
                    .where(RoadmapVersion.roadmap_id == roadmap_id)
                    .order_by(desc(RoadmapVersion.created_at))
                    .limit(1)
                )
                .scalar_one_or_none()
            )

            # Calculate next version numbers
            if latest_version and version_data.auto_increment:
                major = latest_version.major_version
                minor = latest_version.minor_version
                patch = latest_version.patch_version

                if version_data.version_type == "major":
                    major += 1
                    minor = 0
                    patch = 0
                elif version_data.version_type == "minor":
                    minor += 1
                    patch = 0
                else:  # patch
                    patch += 1
            else:
                major, minor, patch = 1, 0, 0

            version_number = f"{major}.{minor}.{patch}"

            # Create roadmap snapshot
            roadmap_snapshot = self._create_roadmap_snapshot(roadmap)

            # Create version record
            version = RoadmapVersion(
                id=str(uuid4()),
                roadmap_id=roadmap_id,
                version_number=version_number,
                major_version=major,
                minor_version=minor,
                patch_version=patch,
                version_type=version_data.version_type,
                change_summary=version_data.change_summary,
                change_details=version_data.change_details,
                roadmap_snapshot=roadmap_snapshot,
                created_by=self.user_id,
                created_by_email=self.user_email,
                creation_reason=version_data.creation_reason
            )

            self.db.add(version)

            # Update roadmap's current version
            roadmap.version = version_number
            roadmap.updated_at = self._utc_now()

            self.db.commit()
            self.db.refresh(version)

            return RoadmapVersionResponse.model_validate(version)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create version: {str(e)}"
            )

    def get_versions(self, roadmap_id: str, limit: int = 50) -> List[RoadmapVersionResponse]:
        """Get all versions for a roadmap."""
        versions = (
            self.db.execute(
                select(RoadmapVersion)
                .where(RoadmapVersion.roadmap_id == roadmap_id)
                .order_by(desc(RoadmapVersion.created_at))
                .limit(limit)
            )
            .scalars()
            .all()
        )

        return [RoadmapVersionResponse.model_validate(version) for version in versions]

    def get_version(self, version_id: str) -> RoadmapVersionResponse:
        """Get a specific version by ID."""
        version = (
            self.db.execute(
                select(RoadmapVersion)
                .where(RoadmapVersion.id == version_id)
            )
            .scalar_one_or_none()
        )

        if not version:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Version {version_id} not found"
            )

        return RoadmapVersionResponse.model_validate(version)

    def release_version(
        self,
        version_id: str,
        release_notes: Optional[str] = None
    ) -> RoadmapVersionResponse:
        """Mark a version as released."""
        try:
            version = self.db.get(RoadmapVersion, version_id)
            if not version:
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail=f"Version {version_id} not found"
                )

            version.is_released = True
            version.released_at = self._utc_now()
            version.released_by = self.user_id
            version.release_notes = release_notes

            self.db.commit()
            self.db.refresh(version)

            return RoadmapVersionResponse.model_validate(version)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to release version: {str(e)}"
            )

    def compare_versions(
        self,
        from_version_id: str,
        to_version_id: str
    ) -> VersionComparison:
        """Compare two versions and return differences."""
        from_version = self.get_version(from_version_id)
        to_version = self.get_version(to_version_id)

        # Compare snapshots
        changes = self._compare_snapshots(
            from_version.roadmap_snapshot,
            to_version.roadmap_snapshot
        )

        return VersionComparison(
            from_version=from_version.version_number,
            to_version=to_version.version_number,
            changes=changes["changes"],
            added_items=changes["added"],
            removed_items=changes["removed"],
            modified_items=changes["modified"],
            summary=changes["summary"]
        )

    def _create_roadmap_snapshot(self, roadmap: Roadmap) -> Dict:
        """Create a complete snapshot of the roadmap structure."""
        snapshot = {
            "roadmap": {
                "id": roadmap.id,
                "name": roadmap.name,
                "status": roadmap.status,
                "project_metadata": roadmap.project_metadata
            },
            "phases": []
        }

        for phase in roadmap.phases:
            phase_data = {
                "id": phase.id,
                "name": phase.name,
                "description": phase.description,
                "order_index": phase.order_index,
                "status": phase.status,
                "dependencies": phase.dependencies,
                "estimated_duration": phase.estimated_duration,
                "steps": []
            }

            for step in phase.steps:
                step_data = {
                    "id": step.id,
                    "name": step.name,
                    "description": step.description,
                    "order_index": step.order_index,
                    "status": step.status,
                    "dependencies": step.dependencies,
                    "estimated_duration": step.estimated_duration,
                    "tasks": []
                }

                for task in step.tasks:
                    task_data = {
                        "id": task.id,
                        "name": task.name,
                        "description": task.description,
                        "order_index": task.order_index,
                        "status": task.status,
                        "assigned_agent": task.assigned_agent,
                        "dependencies": task.dependencies,
                        "estimated_duration": task.estimated_duration,
                        "artifacts": task.artifacts
                    }
                    step_data["tasks"].append(task_data)

                phase_data["steps"].append(step_data)

            snapshot["phases"].append(phase_data)

        return snapshot

    def _compare_snapshots(self, from_snapshot: Dict, to_snapshot: Dict) -> Dict:
        """Compare two roadmap snapshots and identify changes."""
        changes = {
            "changes": {},
            "added": [],
            "removed": [],
            "modified": [],
            "summary": ""
        }

        # This is a simplified comparison - in a real implementation,
        # you would want more sophisticated diff algorithms
        changes["summary"] = f"Comparison between snapshots (simplified)"

        # Compare basic roadmap properties
        if from_snapshot["roadmap"]["name"] != to_snapshot["roadmap"]["name"]:
            changes["changes"]["roadmap_name"] = {
                "from": from_snapshot["roadmap"]["name"],
                "to": to_snapshot["roadmap"]["name"]
            }

        # Count changes
        from_phase_count = len(from_snapshot["phases"])
        to_phase_count = len(to_snapshot["phases"])

        if from_phase_count != to_phase_count:
            changes["changes"]["phase_count"] = {
                "from": from_phase_count,
                "to": to_phase_count
            }

        changes["summary"] = f"Phases: {from_phase_count} → {to_phase_count}"

        return changes
