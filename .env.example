# AI Coding Agent Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SECURITY CONFIGURATION (REQUIRED)
# =============================================================================
# Application secret key (32+ characters, generate with: openssl rand -hex 32)
SECRET_KEY=your_super_secret_key_that_must_be_at_least_32_characters_long  # Used for signing JWTs and encrypting sensitive data
JWT_SECRET=your_jwt_secret_here  # Used for JWT authentication
CONFIG_ENCRYPTION_KEY=your_config_encryption_key_32_chars_min  # Used for config encryption

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME=AI Coding Agent  # Name of the application
APP_VERSION=0.1.0  # Application version
ENVIRONMENT=development  # Set to development, staging, or production
DEBUG=true  # Enable debug mode (true/false)
HOST=0.0.0.0  # Host for FastAPI server
PORT=8000  # Port for FastAPI server
CORS_ORIGINS=http://localhost:3000,http://localhost:8000  # Allowed CORS origins

# JWT Configuration
JWT_ALGORITHM=HS256  # Algorithm for JWT tokens
ACCESS_TOKEN_EXPIRE_MINUTES=30  # Access token expiration (minutes)
REFRESH_TOKEN_EXPIRE_DAYS=7  # Refresh token expiration (days)

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Supabase (hosted)
SUPABASE_URL=  # Supabase project URL
SUPABASE_KEY=  # Supabase API key

# Optional local Postgres for dev (if you want to run local Postgres instead)
DATABASE_URL=postgresql://user:password@localhost:5432/ai_coding_agent  # Only set if running local Postgres. If using Supabase, leave unset.

# Docker secrets (used in Compose)
POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password  # Path to Docker secret for Postgres password
REDIS_PASSWORD_FILE=/run/secrets/redis_password  # Path to Docker secret for Redis password
JWT_SECRET_FILE=/run/secrets/jwt_secret  # Path to Docker secret for JWT secret

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379/0  # Redis connection URL
REDIS_PASSWORD=  # Redis password (if required)
REDIS_DB=0  # Redis database number

# =============================================================================
# PROJECTS & PATHS
# =============================================================================
PROJECTS_DIR=/var/www/projects  # Directory for user projects
APP_ENV=development  # Application environment

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_AI=100  # Requests per minute for AI endpoints
RATE_LIMIT_API=1000  # Requests per minute for general API
RATE_LIMIT_WINDOW=60  # Rate limit window in seconds

# =============================================================================
# LOGGING & AUDIT
# =============================================================================
LOG_LEVEL=info  # Logging level (info, debug, warning, error)
LOG_RETENTION_DAYS=30  # Number of days to retain logs
ELK_URL=  # URL for centralized logging (ELK stack)

# =============================================================================
# OTHER CONFIGURATION
# =============================================================================
# Add any other required environment variables below
