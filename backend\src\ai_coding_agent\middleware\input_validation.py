"""
Enhanced input validation middleware for AI Coding Agent.

This module provides comprehensive input validation using Pydantic schemas
with security-focused validation rules to prevent injection attacks,
data corruption, and ensure data integrity.
"""

import re
import html
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator, root_validator
from fastapi import H<PERSON><PERSON>Ex<PERSON>, Request, status
from fastapi.responses import JSONResponse
import logging

# Create loggers for validation and audit events
validation_logger = logging.getLogger(__name__)
audit_logger = logging.getLogger('audit')

# Avoid circular import - define audit enums locally
from enum import Enum

class AuditLevel(Enum):
    """Audit logging levels."""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class AuditCategory(Enum):
    """Audit categories."""
    DATA_ACCESS = "data_access"
    SECURITY = "security"

# Configure validation logger
validation_logger = logging.getLogger("security.validation")
validation_logger.setLevel(logging.INFO)


class ValidationError(Exception):
    """Custom validation error for enhanced error handling."""

    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        self.message = message
        self.field = field
        self.value = value
        super().__init__(message)


class SecurityValidationMixin:
    """Mixin class providing security validation methods."""

    @staticmethod
    def validate_no_sql_injection(value: str, field_name: str = "field") -> str:
        """Validate that input doesn't contain SQL injection patterns."""
        if not isinstance(value, str):
            return value

        # Common SQL injection patterns
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(--|#|/\*|\*/)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\bOR\s+\w+\s*=\s*\w+)",
            r"(\';|\"\;)",
            r"(\bxp_cmdshell\b)",
            r"(\bsp_executesql\b)"
        ]

        for pattern in sql_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                validation_logger.critical(
                    f"SQL injection attempt detected - field: {field_name}, pattern: {pattern}, value: {value[:100]}"
                )
                raise ValidationError(f"Invalid input detected in {field_name}")

        return value

    @staticmethod
    def validate_no_xss(value: str, field_name: str = "field") -> str:
        """Validate that input doesn't contain XSS patterns."""
        if not isinstance(value, str):
            return value

        # XSS patterns
        xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>",
            r"<link[^>]*>",
            r"<meta[^>]*>",
            r"vbscript:",
            r"data:text/html"
        ]

        for pattern in xss_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                validation_logger.critical(
                    f"XSS attempt detected - field: {field_name}, pattern: {pattern}, value: {value[:100]}"
                )
                raise ValidationError(f"Invalid input detected in {field_name}")

        return value

    @staticmethod
    def validate_no_path_traversal(value: str, field_name: str = "field") -> str:
        """Validate that input doesn't contain path traversal patterns."""
        if not isinstance(value, str):
            return value

        # Path traversal patterns
        path_patterns = [
            r"\.\./",
            r"\.\.\\",
            r"%2e%2e%2f",
            r"%2e%2e%5c",
            r"\.\.%2f",
            r"\.\.%5c"
        ]

        for pattern in path_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                validation_logger.critical(
                    f"Path traversal attempt detected - field: {field_name}, pattern: {pattern}, value: {value[:100]}"
                )
                raise ValidationError(f"Invalid path detected in {field_name}")

        return value

    @staticmethod
    def sanitize_html(value: str) -> str:
        """Sanitize HTML content by escaping dangerous characters."""
        if not isinstance(value, str):
            return value

        return html.escape(value)

    @staticmethod
    def validate_length(value: str, min_length: int = 0, max_length: int = 10000, field_name: str = "field") -> str:
        """Validate string length within acceptable bounds."""
        if not isinstance(value, str):
            return value

        if len(value) < min_length:
            raise ValidationError(f"{field_name} must be at least {min_length} characters long")

        if len(value) > max_length:
            validation_logger.warning(
                f"Input length exceeded - field: {field_name}, length: {len(value)}, max: {max_length}"
            )
            raise ValidationError(f"{field_name} must not exceed {max_length} characters")

        return value


class EnhancedChatRequest(BaseModel, SecurityValidationMixin):
    """Enhanced chat request with security validation."""

    message: str = Field(..., min_length=1, max_length=10000)
    agent_role: Optional[str] = Field(None, pattern="^[a-zA-Z_]+$")
    conversation_id: Optional[str] = Field(None, pattern="^[a-zA-Z0-9_-]+$")
    context: Optional[Dict[str, Any]] = None
    stream: bool = False
    max_tokens: Optional[int] = Field(None, ge=1, le=8192)
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)

    @validator('message')
    def validate_message_security(cls, v):
        """Validate message for security threats."""
        v = cls.validate_no_sql_injection(v, "message")
        v = cls.validate_no_xss(v, "message")
        v = cls.validate_length(v, 1, 10000, "message")
        return v.strip()

    @validator('context')
    def validate_context_security(cls, v):
        """Validate context for security threats."""
        if v is None:
            return v

        # Validate context keys and values
        for key, value in v.items():
            if isinstance(key, str):
                cls.validate_no_sql_injection(key, f"context.{key}")
                cls.validate_no_xss(key, f"context.{key}")

            if isinstance(value, str):
                cls.validate_no_sql_injection(value, f"context.{key}")
                cls.validate_no_xss(value, f"context.{key}")
                cls.validate_length(value, 0, 5000, f"context.{key}")

        return v


class EnhancedUserInput(BaseModel, SecurityValidationMixin):
    """Enhanced user input validation for general use."""

    username: Optional[str] = Field(None, pattern="^[a-zA-Z0-9_.-]+$", min_length=3, max_length=50)
    email: Optional[str] = Field(None, pattern=r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
    full_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)

    @validator('username')
    def validate_username_security(cls, v):
        """Validate username for security."""
        if v is None:
            return v
        v = cls.validate_no_sql_injection(v, "username")
        v = cls.validate_no_xss(v, "username")
        return v.strip()

    @validator('full_name')
    def validate_full_name_security(cls, v):
        """Validate full name for security."""
        if v is None:
            return v
        v = cls.validate_no_sql_injection(v, "full_name")
        v = cls.validate_no_xss(v, "full_name")
        v = cls.sanitize_html(v)
        return v.strip()

    @validator('description')
    def validate_description_security(cls, v):
        """Validate description for security."""
        if v is None:
            return v
        v = cls.validate_no_sql_injection(v, "description")
        v = cls.validate_no_xss(v, "description")
        v = cls.sanitize_html(v)
        return v.strip()


class EnhancedFileInput(BaseModel, SecurityValidationMixin):
    """Enhanced file input validation."""

    filename: str = Field(..., min_length=1, max_length=255)
    content: str = Field(..., max_length=1000000)  # 1MB limit
    file_type: Optional[str] = Field(None, pattern="^[a-zA-Z0-9/.-]+$")

    @validator('filename')
    def validate_filename_security(cls, v):
        """Validate filename for security."""
        v = cls.validate_no_path_traversal(v, "filename")
        v = cls.validate_no_sql_injection(v, "filename")
        v = cls.validate_no_xss(v, "filename")

        # Additional filename validation
        if re.search(r'[<>:"|?*]', v):
            raise ValidationError("Filename contains invalid characters")

        return v.strip()

    @validator('content')
    def validate_content_security(cls, v):
        """Validate file content for security."""
        # For code files, we're more permissive but still check for obvious attacks
        if len(v) > 1000000:  # 1MB limit
            raise ValidationError("File content too large")

        return v


async def validation_exception_handler(request: Request, exc: ValidationError):
    """Custom exception handler for validation errors."""

    # Log validation failure
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")
    audit_logger.warning(
        f"Input validation failed - field: {exc.field or 'unknown'}, message: {exc.message}, "
        f"endpoint: {request.url}, method: {request.method}, ip: {client_ip}, ua: {user_agent}"
    )

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "detail": "Input validation failed",
            "field": exc.field,
            "message": exc.message
        }
    )


class InputValidationMiddleware:
    """Middleware for enhanced input validation."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """Process request through validation middleware."""
        if scope["type"] == "http":
            request = Request(scope, receive)

            # Log all incoming requests for audit
            client_ip = request.client.host if request.client else "unknown"
            user_agent = request.headers.get("user-agent", "unknown")
            audit_logger.info(
                f"API request: {request.method} {request.url} - ip: {client_ip}, ua: {user_agent}"
            )

        await self.app(scope, receive, send)
