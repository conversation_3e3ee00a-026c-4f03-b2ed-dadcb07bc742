#!/usr/bin/env python3
"""
Script to fix relative imports in the AI Coding Agent backend.

This script converts all relative imports to absolute imports to prevent
circular import issues and improve maintainability.
"""

import os
import re
from pathlib import Path
from typing import List, <PERSON><PERSON>

def find_python_files(directory: str) -> List[Path]:
    """Find all Python files in the given directory."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']

        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)

    return python_files

def find_relative_imports(file_path: Path) -> List[Tuple[int, str, str]]:
    """Find relative imports in a Python file."""
    relative_imports = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for i, line in enumerate(lines, 1):
            line = line.strip()

            # Match relative imports: from .module import ... or from ..module import ...
            if re.match(r'^from\s+\.+\w', line):
                relative_imports.append((i, line, line))

    except Exception as e:
        print(f"Error reading {file_path}: {e}")

    return relative_imports

def convert_relative_to_absolute(file_path: Path, base_package: str = "ai_coding_agent") -> bool:
    """Convert relative imports to absolute imports in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Get the relative path from the base package
        relative_path = file_path.relative_to(Path("backend/src"))
        package_path = str(relative_path.parent).replace(os.sep, '.')

        # Remove the base package from the beginning if it's there
        if package_path.startswith(f"{base_package}."):
            current_package = package_path
        else:
            current_package = f"{base_package}.{package_path}" if package_path != "." else base_package

        # More complex patterns for nested relative imports
        if current_package != base_package:
            # Calculate parent packages
            parts = current_package.split('.')
            if len(parts) > 1:
                parent_package = '.'.join(parts[:-1])
                # from .module -> from current_package.module
                content = re.sub(r'from\s+\.(\w+)', rf'from {current_package}.\1', content)
                # from ..module -> from parent_package.module
                content = re.sub(r'from\s+\.\.(\w+)', rf'from {parent_package}.\1', content)

        # Basic patterns for root level
        patterns = [
            # from .module import something
            (r'from\s+\.(\w+)', rf'from {base_package}.\1'),
            # from ..module import something
            (r'from\s+\.\.(\w+)', rf'from {base_package}.\1'),
            # from ...module import something
            (r'from\s+\.\.\.(\w+)', rf'from {base_package}.\1'),
        ]

        # Apply basic patterns for simple cases
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)

        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

    return False

def main():
    """Main function to fix all relative imports."""
    backend_dir = Path("backend/src/ai_coding_agent")

    if not backend_dir.exists():
        print(f"❌ Directory {backend_dir} does not exist")
        return

    print("🔍 Finding Python files with relative imports...")
    python_files = find_python_files(str(backend_dir))

    files_with_relative_imports = []
    total_relative_imports = 0

    for file_path in python_files:
        relative_imports = find_relative_imports(file_path)
        if relative_imports:
            files_with_relative_imports.append((file_path, relative_imports))
            total_relative_imports += len(relative_imports)

    print(f"📊 Found {total_relative_imports} relative imports in {len(files_with_relative_imports)} files")

    if not files_with_relative_imports:
        print("✅ No relative imports found!")
        return

    print("\n📝 Files with relative imports:")
    for file_path, imports in files_with_relative_imports:
        rel_path = file_path.relative_to(Path("backend/src"))
        print(f"  📄 {rel_path}")
        for line_num, original, _ in imports:
            print(f"    Line {line_num}: {original}")

    print(f"\n🔧 Converting relative imports to absolute imports...")

    fixed_files = 0
    for file_path, _ in files_with_relative_imports:
        if convert_relative_to_absolute(file_path):
            rel_path = file_path.relative_to(Path("backend/src"))
            print(f"  ✅ Fixed: {rel_path}")
            fixed_files += 1
        else:
            rel_path = file_path.relative_to(Path("backend/src"))
            print(f"  ⚠️  No changes: {rel_path}")

    print(f"\n🎉 Conversion complete!")
    print(f"   📊 Fixed {fixed_files} files")
    print(f"   📊 Converted {total_relative_imports} relative imports")

if __name__ == "__main__":
    main()
