"""
Redis client setup and helpers.
"""
import os
from typing import Any, Optional
import aioredis

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
_redis = None

def get_redis():
    global _redis
    if _redis is None:
        import redis
        _redis = redis.Redis.from_url(REDIS_URL)
    return _redis

def redis_get(key: str) -> Any:
    return get_redis().get(key)

def redis_set(key: str, value: Any) -> None:
    get_redis().set(key, value)

def publish(channel: str, message: str) -> None:
    get_redis().publish(channel, message)

def subscribe(channel: str):
    pubsub = get_redis().pubsub()
    pubsub.subscribe(channel)
    return pubsub

def enqueue_job(queue: str, job: Any) -> None:
    get_redis().rpush(queue, job)

def dequeue_job(queue: str) -> Any:
    return get_redis().lpop(queue)

# Async helpers
async def async_get_redis():
    return await aioredis.from_url(REDIS_URL)

async def async_redis_get(key: str) -> Any:
    redis = await async_get_redis()
    return await redis.get(key)

async def async_redis_set(key: str, value: Any) -> None:
    redis = await async_get_redis()
    await redis.set(key, value)
