[supervisord]
nodaemon=true
user=root
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/var/run/supervisord.pid
childlogdir=/tmp

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
user=root
priority=1

[program:fastapi]
command=python -m uvicorn ai_coding_agent.main:app --host 0.0.0.0 --port 8000 --reload
directory=/app/backend/src
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
user=appuser
environment=PATH="/usr/local/bin:%(ENV_PATH)s",PYTHONPATH="/usr/local/lib/python3.11/site-packages:/app/backend/src:/app"
priority=2
